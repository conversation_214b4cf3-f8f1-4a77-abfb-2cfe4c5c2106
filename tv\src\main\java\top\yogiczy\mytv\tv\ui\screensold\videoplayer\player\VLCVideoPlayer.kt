package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.graphics.PixelFormat
import android.graphics.SurfaceTexture
import android.content.Context
import android.net.Uri
import android.view.View
import android.view.Surface
import android.view.SurfaceView
import android.view.SurfaceHolder
import android.view.TextureView
import androidx.annotation.MainThread
import androidx.core.net.toUri
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.actor
import org.videolan.BuildConfig
import org.videolan.libvlc.LibVLC
import org.videolan.libvlc.Media
import org.videolan.libvlc.MediaPlayer
import org.videolan.libvlc.RendererItem
import org.videolan.libvlc.interfaces.IMedia
import org.videolan.libvlc.interfaces.IMediaList
import org.videolan.libvlc.interfaces.IVLCVout
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.data.utils.Logger
import top.yogiczy.mytv.core.util.utils.toHeaders
import top.yogiczy.mytv.tv.ui.utils.Configs
import kotlin.math.roundToInt
import kotlin.math.absoluteValue

class VLCVideoPlayer(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
) : VideoPlayer(coroutineScope),
    MediaPlayer.EventListener{
    
    private val logger = Logger.create("VLCVideoPlayer")
    
    private var currentSurfaceView: SurfaceView? = null
    private var currentTextureView: TextureView? = null
    private var subtitleSurfaceView: SurfaceView? = null
    private var subtitleTextureView: TextureView? = null
    private var trackIDStringList: MutableList<String> = mutableListOf()
    var libVLC: LibVLC? = null
    var mediaPlayer: MediaPlayer? = null
    var isMediaParsed = false
    var subtitleTrack: Int? = null
    var audioTrack: Int? = null
    var audioTrackDisabledID: Int? = null
    var subtitleTrackDisabledID: Int? = null

    override fun initialize() {
        super.initialize()
        val textSize = Configs.uiVideoPlayerSubtitle.textSize
        val style = Configs.uiVideoPlayerSubtitle.style
        val options = ArrayList<String>().apply {
            // add("--http-reconnect")
            add("--preparse-timeout=${Configs.videoPlayerLoadTimeout}") // 网络超时
            add("--network-caching=${Configs.videoPlayerBufferTime}") // 网络缓存
            add("--live-caching=${Configs.videoPlayerBufferTime}") // 直播缓存
            add("--prefetch-buffer-size=${getAvailableMemory(context) / 1024}") // 网络缓存大小
            add("--sout-keep")
            add("--audio-time-stretch")
            add("--audio-resampler")
            add("soxr")
            // add("--http-user-agent=${}")
            // add("--http-referrer=${}")
            add("--freetype-rel-fontsize=${textSize}") // 设置字体大小
            add("--freetype-color=${style.foregroundColor}") // 字体颜色
            // add("--freetype-opacity=255") // 字体透明度
            // add("--freetype-background-opacity=0") // 背景透明度
            // add("--freetype-shadow-color=0") // 阴影颜色
            // add("--freetype-shadow-opacity=128") // 阴影透明度
            add("--freetype-outline-thickness=4") // 字体轮廓厚度
            add("--freetype-outline-color=${style.edgeColor}") // 字体轮廓颜色
            // add("--freetype-outline-opacity=255") // 字体轮廓透明度
            add("--freetype-background-color=${style.backgroundColor}")
            add("--no-lua")
            add("--no-stats")
            add("--fullscreen")
            add("--preferred-resolution=-1")
            add("-vv")
        }
        isMediaParsed = false
        subtitleTrack = null
        audioTrack = null
        audioTrackDisabledID = null
        subtitleTrackDisabledID = null
        libVLC = LibVLC(context, options)
        mediaPlayer = MediaPlayer(libVLC)
        mediaPlayer?.setEventListener(this@VLCVideoPlayer)
        trackIDStringList = mutableListOf()
    }

    private fun updateMetadata() {
        // if(isMediaParsed) return
        // isMediaParsed = true
        trackIDStringList = mutableListOf()
        val videoTracks = mutableListOf<Metadata.Video>()
        val audioTracks = mutableListOf<Metadata.Audio>()
        val subtitleTracks = mutableListOf<Metadata.Subtitle>()
        
        var currentVideo: Metadata.Video? = null
        var currentAudio: Metadata.Audio? = null
        var currentSubtitle: Metadata.Subtitle? = null
        var trackIndex = 0
        // 处理视频轨道
        mediaPlayer?.let { mp ->
            mp.getMedia()?.let { media ->
                media.parse()
                mp.getVideoTracks()?.forEach { track ->
                    val isSelected = track.id == mp.getVideoTrack()
                    if(track.name.contains("Disable")) {
                    }else{
                        val videoTrack = Metadata.Video(
                            index = track.id,
                            isSelected = isSelected,
                            language = track.name,
                        )
                        videoTracks.add(videoTrack)
                    }
                }
                mp.getCurrentVideoTrack()?.let { track ->
                    val currentTrack = Metadata.Video(
                        index = track.id,
                        isSelected = true,
                        width = track.width,
                        height = track.height,
                        frameRate = track.frameRateNum.toFloat() / track.frameRateDen.toFloat(),
                        bitrate = track.bitrate / 1000,
                        mimeType = track.codec.toString(),
                        decoder = track.originalCodec.toString(),
                        language = track.language,
                    )
                    currentVideo = currentTrack
                    if(currentTrack.width != null && currentTrack.height != null && currentTrack.width > 0 && currentTrack.height > 0)
                        triggerResolution(currentTrack.width, currentTrack.height)
                    triggerSurfaceFrameRate(currentTrack.frameRate ?: -1f)
                }
                mp.getAudioTracks()?.forEach { track ->
                    val isSelected = track.id == mp.getAudioTrack()
                    if(track.name.contains("Disable")) {
                        audioTrackDisabledID = track.id
                    }else{
                        val audioTrack = Metadata.Audio(
                            index = track.id,
                            isSelected = isSelected,
                            language = track.name,
                        )
                        if(isSelected)
                            currentAudio = audioTrack
                        audioTracks.add(audioTrack)
                    }
                }
                mp.getSpuTracks()?.forEach { track ->
                    val isSelected = track.id == mp.getSpuTrack()
                    if(track.name.contains("Disable")) {
                        subtitleTrackDisabledID = track.id
                    }else{
                        val subtitleTrack = Metadata.Subtitle(
                            index = track.id,
                            isSelected = isSelected,
                            language = track.name,
                        )
                        if(isSelected)
                            currentSubtitle = subtitleTrack
                        subtitleTracks.add(subtitleTrack)
                    }
                }
                // media.getTracks(IMedia.Track.Type.Video)?.let { tracks ->
                //     for (track in tracks) {
                //         val isSelected = track.id == mp.getSelectedTrack(IMedia.Track.Type.Video)?.id
                    //     val videoTrackData = track as IMedia.VideoTrack
                    //     trackIDStringList.add(track.id.toString())
                    //     val videoTrack = Metadata.Video(
                    //         index = trackIndex,
                    //         isSelected = isSelected,
                    //         width = videoTrackData.width,
                    //         height = videoTrackData.height,
                    //         frameRate = videoTrackData.frameRateNum.toFloat() / videoTrackData.frameRateDen.toFloat(),
                    //         bitrate = videoTrackData.bitrate / 1000,
                    //         mimeType = videoTrackData.codec.toString(),
                    //         decoder = videoTrackData.originalCodec.toString(),
                    //         language = videoTrackData.language,
                    //     )
                    //     trackIndex = trackIndex + 1
                    //     videoTracks.add(videoTrack)
                    //     if (isSelected) {
                    //         currentVideo = videoTrack
                    //         if(videoTrack.width != null && videoTrack.height != null && videoTrack.width > 0 && videoTrack.height > 0)
                    //             triggerResolution(videoTrack.width, videoTrack.height)
                    //         triggerSurfaceFrameRate(videoTrack.frameRate ?: -1f)
                    //     }
                    // }
                // }
                // // 处理音频轨道
                // media.getTracks(IMedia.Track.Type.Audio)?.let { tracks ->
                // mp.getAudioTracks()?.let { tracks ->
                //     for (track in tracks) {
                //         val isSelected = track.id == mp.getSelectedTrack(IMedia.Track.Type.Audio)?.id
                //         val audioTrackData = track as IMedia.AudioTrack
                //         trackIDStringList.add(track.id.toString())
                //         val audioTrack = Metadata.Audio(
                //             index = trackIndex,
                //             isSelected = isSelected,
                //             channels = audioTrackData.channels,
                //             sampleRate = audioTrackData.rate,
                //             bitrate = audioTrackData.bitrate / 1000,
                //             language = audioTrackData.language,
                //             mimeType = audioTrackData.codec.toString(),
                //             decoder = audioTrackData.originalCodec.toString(),
                //         )
                //         trackIndex = trackIndex + 1
                //         audioTracks.add(audioTrack)
                //         if (isSelected) {
                //             currentAudio = audioTrack
                //         }
                //     }
                // }
                // // 处理字幕轨道
                // media.getTracks(IMedia.Track.Type.Text)?.let { tracks ->
                // mp.getSpuTracks()?.let { tracks ->
                //     for (track in tracks) {
                //         val isSelected = track.id == mp.getSelectedTrack(IMedia.Track.Type.Text)?.id
                //         val textTrackData = track as IMedia.SubtitleTrack
                //         trackIDStringList.add(track.id.toString())
                //         val subtitleTrack = Metadata.Subtitle(
                //             index = trackIndex,
                //             isSelected = isSelected,
                //             language = textTrackData.language,
                //             mimeType = textTrackData.codec.toString()
                //         )
                //         trackIndex = trackIndex + 1
                //         subtitleTracks.add(subtitleTrack)
                //         if (isSelected) {
                //             currentSubtitle = subtitleTrack
                //         }
                //     }
                // }
                metadata = metadata.copy(
                    video = currentVideo,
                    audio = currentAudio,
                    subtitle = currentSubtitle,
                    videoTracks = videoTracks,
                    audioTracks = audioTracks,
                    subtitleTracks = subtitleTracks
                )
                triggerMetadata(metadata)
            }
        }
    }
    
    override fun prepare(line: ChannelLine) {
        isMediaParsed = false
        super.prepare(line)
        if(mediaPlayer == null) initialize()
        if (Configs.videoPlayerStopPreviousMediaItem)
            mediaPlayer?.stop()
        coroutineScope.launch {
            val playData = getPlayableData()
            if (playData == null) {
                triggerError(PlaybackException("No playable data found for line: ${line}", 10087))
                logger.e("No playable data found for line: ${line}")
                return@launch
            }
            val uri = Uri.parse(playData.url)
            val media = Media(libVLC, uri)
            var header: Map<String, String> = playData.headers ?: emptyMap()
            var headers = Configs.videoPlayerHeaders.toHeaders() + mapOf(
                "Referer" to (line.httpReferrer ?: ""),
                "Origin" to (line.httpOrigin ?: ""),
                "Cookie" to (line.httpCookie ?: ""),
            ).filterValues { it.isNotEmpty() } + header
            
            val userAgent = headers["User-Agent"] ?: headers["user-agent"] ?: line.httpUserAgent ?: Configs.videoPlayerUserAgent
            val referer = headers["Referer"] ?: headers["referer"] ?: line.httpReferrer
            headers = headers - "User-Agent" - "user-agent" - "Referer" - "referer"
            // 使用应用内日志系统
            logger.i("播放地址: ${uri.toString()}")
            logger.i("请求头: UA = $userAgent, Referer = $referer")
            if (userAgent != null && userAgent.isNotEmpty()) {
                media.addOption(":http-user-agent=$userAgent")
            }
            if(subtitleTrack != null){
                media.addOption(":sub-track-id=${subtitleTrack}")
            }
            logger.i("当前字幕轨道：${subtitleTrack}, 音频轨道：${audioTrack}")
            if(audioTrack != null){
                media.addOption(":audio-track-id=${audioTrack}")
            }
            if (referer != null && referer.isNotEmpty()) {
                media.addOption(":http-referrer=$referer")
            }
            // 设置其他选项
            if (uri.toString().startsWith("rtsp://") || uri.toString().startsWith("rtp://")) {
                if (Configs.videoPlayerRtspTransport == Configs.RtspTransport.TCP) {
                    media.addOption(":rtsp-tcp")
                }
            }
            if (Configs.videoPlayerForceSoftDecode) {
                media.setHWDecoderEnabled(false, false)
            } else {
                media.setHWDecoderEnabled(true, false)
            }
            withContext(Dispatchers.IO) {
                if (mediaPlayer?.isReleased == false){
                    mediaPlayer?.setMedia(media)
                }
            }
            media.release()
            if (mediaPlayer?.isReleased == false) {
                mediaPlayer?.setVideoTitleDisplay(MediaPlayer.Position.Disable, 0)
                if (setPlay()) {
                    triggerPrepared()
                }
            }
            
        }
    }
    private fun setPlay(): Boolean {
        mediaPlayer?.let {
            if (it != null && !it.isReleased && it.getVLCVout().areViewsAttached()){
                it.play()
                return true
            }
        }
        return false
    }
    override fun play() {
        setPlay()
    }
    
    override fun pause() {
        mediaPlayer?.let {
            if (it != null) {
                it.pause()
            }
        }
    }
    
    override fun stop() {
        mediaPlayer?.let {
            if (it != null && !it.isReleased)
                it.stop()
        }
    }
    
    override fun seekTo(position: Long) {
        mediaPlayer?.let {
            if (it != null && !it.isReleased)
                it.setTime(position)
        }
    }
    
    override fun setVolume(volume: Float) {
        mediaPlayer?.let {
            if (it != null && !it.isReleased)
                it.setVolume(volume.toInt()) else -1
        }
    }
    
    override fun getVolume(): Float {
        mediaPlayer?.let {
            return if (it != null && !it.isReleased)
                it.volume.toFloat() else 100f
        }
        return 0f
    }
    
    override fun selectVideoTrack(track: Metadata.Video?) {
        mediaPlayer?.let {
            // it.unselectTrackType(IMedia.Track.Type.Video)
            // if (track?.index != null && track.index >= 0 && track.index < trackIDStringList.size) {
            //     it.selectTrack(trackIDStringList[track.index])
            // }
        }
    }
    
    override fun selectAudioTrack(track: Metadata.Audio?) {
        mediaPlayer?.let {
            // it.unselectTrackType(IMedia.Track.Type.Audio)
            // if (track?.index != null && track.index >= 0 && track.index < trackIDStringList.size) {
            //     it.selectTrack(trackIDStringList[track.index])
            // }
            if (track?.index != null)
                audioTrack = track.index
                // it.setAudioTrack(track.index)
            else
                audioTrack = audioTrackDisabledID
            line?.let { line ->
                prepare(line)
            }
        }
    }
    
    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {
        mediaPlayer?.let {
            // it.unselectTrackType(IMedia.Track.Type.Subtitle)
            // if (track?.index != null && track.index >= 0 && track.index < trackIDStringList.size) {
            //     it.selectTrack(trackIDStringList[track.index])
            // }
            if (track?.index != null)
                subtitleTrack = track.index
                // it.setSpuTrack(track.index)
            else
                subtitleTrack = subtitleTrackDisabledID
            line?.let { line ->
                prepare(line)
            }
        }
    }

    private fun setVideoView(): Boolean {
        val surfaceView = currentSurfaceView
        val textureView = currentTextureView
        val subSurfaceView = subtitleSurfaceView
        val subTextureView = subtitleTextureView
        if (subSurfaceView == null || subSurfaceView.getHolder().getSurface().isValid() == false) {
            if (subTextureView == null || subTextureView.isAvailable()  == false) {
                return false
            }
        }
        if (surfaceView == null || surfaceView.getHolder().getSurface().isValid() == false) {
            if (textureView == null || textureView.isAvailable() == false) {
                return false
            }
        }
        mediaPlayer?.let { player ->
            if (player.getVLCVout().areViewsAttached()) {
                player.getVLCVout().detachViews()
            }
            if (subSurfaceView != null) {
                player.getVLCVout().setSubtitlesView(subSurfaceView)
            } else {
                player.getVLCVout().setSubtitlesView(subTextureView)
            }
            if (surfaceView != null) {
                player.getVLCVout().setVideoView(surfaceView)
            } else {
                player.getVLCVout().setVideoView(textureView)
            } 
            val view: View = (surfaceView ?: textureView)!!
            player.getVLCVout().setWindowSize(view.getWidth(), view.getHeight())
            view.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
                override fun onLayoutChange(v: View, left: Int, top: Int, right: Int, bottom: Int,
                                            oldLeft: Int, oldTop: Int, oldRight: Int, oldBottom: Int) {
                    val newWidth = right - left
                    val newHeight = bottom - top
                    // 设置VLC播放器的宽高参数
                    mediaPlayer?.let {
                        it.getVLCVout().setWindowSize(newWidth, newHeight)
                    }
                }
            })
            player.getVLCVout().attachViews()
            player.setVideoTrackEnabled(true)
            if (setPlay()) {
                triggerPrepared()
            }
            return true
        }
        return false
    }
    
    override fun setVideoSurfaceView(surfaceView: SurfaceView) {
        currentSurfaceView = surfaceView
        currentTextureView = null
        processSurfaceView(surfaceView)
    }
    
    override fun setVideoTextureView(textureView: TextureView) {
        currentTextureView = textureView
        currentSurfaceView = null
        processTextureView(textureView)
    }

    fun processSurfaceView(surfaceView: SurfaceView) {
        if(surfaceView.getHolder().getSurface().isValid()) {
            setVideoView()
            return
        }
        surfaceView.getHolder().addCallback(object : SurfaceHolder.Callback {
            override fun surfaceCreated(holder: SurfaceHolder) {
                setVideoView()
            }
            override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) { }
            override fun surfaceDestroyed(holder: SurfaceHolder) { }
        })
    }

    fun processTextureView(textureView: TextureView) {
        if(textureView.isAvailable()) {
            setVideoView()
            return
        }
        textureView.setSurfaceTextureListener(object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                setVideoView()
            }
            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) { }
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                return true
            }
            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) { }
        })
    }

    override fun setSubtitleSurfaceView(surfaceView: SurfaceView) {
        subtitleSurfaceView = surfaceView
        subtitleTextureView = null
        processSurfaceView(surfaceView)
    }

    override fun setSubtitleTextureView(textureView: TextureView) {
        subtitleTextureView = textureView
        subtitleSurfaceView = null
        processTextureView(textureView)
    }
    
    override fun setSurfaceFrameRate(frameRate: Float) {
        try{
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                currentSurfaceView?.getHolder()?.getSurface()?.setFrameRate(frameRate,
                        Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE,
                        Surface.CHANGE_FRAME_RATE_ALWAYS)
            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                currentSurfaceView?.getHolder()?.getSurface()?.setFrameRate(
                    frameRate, Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE)
            }
        } catch (e: Exception) {
            // 如果设置失败，忽略
        }
    }
    
    override fun release() {
        super.release()
        try {
            mediaPlayer?.setEventListener(null)
            mediaPlayer?.let {
                if (!it.isReleased()) {
                    it.stop()
                    it.getVLCVout().detachViews()
                    it.release()
                    mediaPlayer = null
                }
            }
            libVLC?.let {
                if (!it.isReleased()) {
                    it.release()
                    libVLC = null
                }
            }
        } catch (e: Exception) {
            logger.e("release error", e)
        }
    }

    override fun onEvent(event: MediaPlayer.Event?) {
        if (event == null) return
        when (event.type) {
            MediaPlayer.Event.MediaChanged -> {
                updateMetadata()
            }
            MediaPlayer.Event.Opening -> {
                triggerBuffering(false)
            }
            MediaPlayer.Event.Buffering -> {
                if (event.getBuffering() < 95) {
                    triggerBuffering(true)
                } else {
                    triggerBuffering(false)
                    updateMetadata()
                }
            }
            MediaPlayer.Event.Playing -> {
                triggerReady()
                updateMetadata()
                triggerIsPlayingChanged(true)
                triggerBuffering(false)
            }
            MediaPlayer.Event.Paused -> {
                triggerBuffering(false)
                triggerIsPlayingChanged(false)
            }
            MediaPlayer.Event.Stopped -> {
                triggerBuffering(false)
                triggerIsPlayingChanged(false)
            }
            MediaPlayer.Event.EndReached -> {
                triggerBuffering(false)
                triggerIsPlayingChanged(false)
            }
            MediaPlayer.Event.EncounteredError -> {
                triggerError(PlaybackException("Play Failed", event.type))
            }
            MediaPlayer.Event.TimeChanged -> {
                triggerCurrentPosition(event.getTimeChanged())
            }
            MediaPlayer.Event.PositionChanged -> {

            }
            MediaPlayer.Event.SeekableChanged -> {
                
            }
            MediaPlayer.Event.PausableChanged -> {
                
            }
            MediaPlayer.Event.LengthChanged -> {
                triggerDuration(event.getLengthChanged())
            }
            MediaPlayer.Event.Vout -> {
                updateMetadata()
            }
            MediaPlayer.Event.ESAdded -> {
                
            }
            MediaPlayer.Event.ESDeleted -> {
                
            }
            MediaPlayer.Event.ESSelected -> {
                
            }
            MediaPlayer.Event.RecordChanged -> {
                
            }
        }
    }
}