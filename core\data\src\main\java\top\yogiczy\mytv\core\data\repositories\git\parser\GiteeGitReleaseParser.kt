package top.yogiczy.mytv.core.data.repositories.git.parser

import android.os.Build
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import top.yogiczy.mytv.core.data.entities.git.GitRelease
import top.yogiczy.mytv.core.data.utils.Globals

/**
 * gitee发行版解析
 */
class GiteeGitReleaseParser : GitReleaseParser {
    override fun isSupport(url: String): Boolean {
        return url.contains("gitee.com")
    }

    override suspend fun parse(data: String): GitRelease {
        val json = Globals.json.parseToJsonElement(data).jsonObject
        val arch = Build.SUPPORTED_ABIS.firstOrNull() ?: "unknown"
        val downloadUrl = json.getValue("assets").jsonArray.firstOrNull {
            it.jsonObject["name"]?.jsonPrimitive?.content?.contains(arch, ignoreCase = true) ?: false
        }?.jsonObject?.get("browser_download_url")?.jsonPrimitive?.content ?: run {
            throw Exception("No suitable download URL found for architecture: $arch")
        }
        return GitRelease(
            version = json.getValue("tag_name").jsonPrimitive.content.substring(1),
            downloadUrl = downloadUrl,
            description = json.getValue("body").jsonPrimitive.content,
        )
    }
}