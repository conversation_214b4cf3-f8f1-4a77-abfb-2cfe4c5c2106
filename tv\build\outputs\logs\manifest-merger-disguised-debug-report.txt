-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:89:9-97:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:93:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:91:13-64
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:92:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:90:13-62
manifest
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d4e98c92be75c2303e0d2810d3d016a9\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aff0a4e7236056764cabe678e059991a\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cab515afc7d794d03ee10473c40eb78\transformed\coil-svg-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b06b60f527eac6792fa28b1cb5f9437\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6f36c6f9b88206bbc0782dd48ec05c5\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4119ce110c4cb24d80c3660a70b916b\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c8ee00287bca8e9af48e7446162a635e\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f02348ccb0e7f9cc920731d17c38172\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57909a5533f41ad27907668886664dc3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efbd2119718ae0bfeda059930e1194a7\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8e6007a452cea9a61deeb7ee9d7c128\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0390848ee78f173243bbaf7411fdc905\transformed\libvlc-all-3.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\901c62c253373a1ba61e071ec92e3ddd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e809a4557f286a4df4416c5fc8b40c0d\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ac8297ac893d40065497418c73e02\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.5.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1cb8d8cb32f9b6b54ba7b00e42464438\transformed\material3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c818d827b91a2191cf3f4dcefe10f525\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b953b9ef09835f700c6408491a803f0b\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f42fcb51fd3bb7fa7d6a544151362c1e\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\af9619106ca5a74782ec42e5c552ec6a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\96d010939395be32a69a992c882b4db4\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5849074be76a4c0d0f35e172fcf93d43\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab96961d1dab3d7e40e5bfedaab514c4\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5333a80b0fe68b99990e90c2d086162\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0615017894abf0bd7b33401f847396d0\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\63e1195e3a6573e9e7a9fde678dc92a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f1ad126e3dd2ecd6df0f4f55b684ab94\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c97d9cfeeffdeb4b13a0bfd1d00fc97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f012db532efca603faf7e2f1b1bb4867\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f2b5b25f7a8e645b3cd12bea3eb3a36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\833b0c813414bd66fb545cbc035dc1e2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2ddc148ab2850d14a8acfd77bd2da82c\transformed\media3-ui-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\70c6c26fa193f642fd9b05f0da7da929\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8cd342aac98369f7c46f3523fe2f8eba\transformed\media3-extractor-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4116ba62e7fe4bb61d451346cb9ab9cb\transformed\media3-container-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae9f3f6e20462a971f03c27309215647\transformed\media3-datasource-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e3bb0a67f10c4c098f870af10e363468\transformed\media3-decoder-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02d90d3994b46b644d1075128a27b19f\transformed\media3-database-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01078d44cb24a05a9754244885981d2\transformed\media3-common-1.8.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bf2510e092dbf826cc6b1c89f2dc6ce0\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0fcc4916bb0dcddefd58238ed35b2596\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0766020c4843ec3150cc21dd20c9acb\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\099c27caf1c718359c373e4e428d1a48\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\151a69a7f886af7aa3234b82508a55dc\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7ce58255463d36265f6049fe9390ff3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e421f90c22810ec885b6a02cd295f912\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2d561beb03dfda8138c45e95fe5c0b80\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2217fe3dcf708380453a761385649ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f0b99c239d9c219a4002298aed66c81\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14841292f169b6b3d6cd3f049dd7da9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad01708dea991c04529fd04f8d79e493\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a6420713937a19eed0d0658ad58ff07d\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95f1b4c76ab5d4456c6d5d56e3ae51d\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\84bbce96c3ac6552d3233b4673b0afa3\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86e5380642e8f59dadd272587b78caaf\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6792a5c31123b5b9417ca48841ad4abe\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b4884e7705c0171a0acb90c93500610\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e92f510cb176552fb10380ec72670dc7\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83ba0c5cb47302e8a83060ba4a68b507\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e32d17248dcbde9827dba710d49e7804\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\638739b15b6613760119af1fe02bc62e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c95d115402668d25cc6e24420d4853\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ecb62d8759e715b1b4fa0d8e66334372\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c29221ce9c68d83012cc3945ca400388\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f97da89eac0fc41342000cdcaf05834f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb9c3301855b64340e9a3ba90141e562\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71ad99cb288e9a4c50441fb10934b125\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1c729ae53863038070962a189432046d\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4258475a35a235d2942e638282bb8a92\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f570db518546982e31794f84e73cb1b\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\189389f5b60b26e639e387bd453f0473\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0f0df49903dae0e54b022124708ba75\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\797cb2bfa8ea4b49554b5b1246168244\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ef825cb11d8e1edcb8e7728c41c183cf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ee0e85defb66d723b5bacbd22e8fea5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05ee3d42f5b1adab2edec5b49bbaa2be\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97871514283cc216ff487f321a0a5e37\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6da60ba0d9d2ebff88ff4791a942a33\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7573f74c3ad07832627b4ae047c2be13\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\834986d56ec82d0862a15e5cf454cc76\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cbc108cc86e02b296c444579f1caa7e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ee21b4eecaca06e8d47bdb5dff6ef4a9\transformed\qrose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7829d6bc7ee3b87b82f04f0307bb47b2\transformed\qrose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2971bdc7d1c5a9df55a113e9413ea59f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d3ae0d91a79510abf6b8d344ff17e9c\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f568cf557dd9151ad1dcecbd4a2479bc\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\675067a6b0ef7dc58058a5633fb77705\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3dac38bd6d7f7041169ac5fa81e3096b\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed0655579c4da080363e79c6c8825972\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d5b6915865010c3046c2c3a0f15f3a2f\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc423ea9b0054663f9ce8a86023fe20a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d809ce6e79402ff8554e248bd93f9aa\transformed\graphics-shapes-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\513dd212c35d3d333716e2a909830546\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4424f82f483dce040ec7343d9b1f6a83\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b17df47565a6a7225ba5f3d11b7b305\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dfce483f78b523ba9a9b9efc48adc9ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f787107385cdc60946aee89b91096482\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d99eee0d757b58a98207ee4528f9950\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aee4571b660d469380ad98cdc743496c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0448581b0035e3c0171ba7bde0841803\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8e8338334c40510b7dd32f129a64d13\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3ddec477fbc1692c93f80ce9c318abc0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8588e24114685cef204b72b563b8204b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3316c514475025ff5b55d4b14cb1f200\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\032d252a695fda8f9a172ed0dfcedb3d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:2:1-16:12
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83841ce76a960d9b7ad1149dd1e1c7f1\transformed\wrapper-android-3.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\093673b58f5d13fd133f975cbf8dfa8a\transformed\rtmp-client-3.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\68aba0c7fefdd007012f01ba77858dd8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:6:9-52
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:10:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:9:9-49
queries
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:12:5-14:15
package#com.google.android.webview
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:9-62
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:18-59
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:8:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:8:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\093673b58f5d13fd133f975cbf8dfa8a\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\093673b58f5d13fd133f975cbf8dfa8a\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:5-81
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:5-83
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:22-80
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:22-65
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:5-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:22-73
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01078d44cb24a05a9754244885981d2\transformed\media3-common-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01078d44cb24a05a9754244885981d2\transformed\media3-common-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\151a69a7f886af7aa3234b82508a55dc\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\151a69a7f886af7aa3234b82508a55dc\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:22-76
uses-permission#android.permission.GET_TASKS
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:22-65
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:5-75
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:22-72
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:26:5-28:40
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:28:9-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:27:9-66
application
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:30:5-103:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aff0a4e7236056764cabe678e059991a\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aff0a4e7236056764cabe678e059991a\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57909a5533f41ad27907668886664dc3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57909a5533f41ad27907668886664dc3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0448581b0035e3c0171ba7bde0841803\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0448581b0035e3c0171ba7bde0841803\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:37:9-52
	android:extractNativeLibs
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:45:9-41
	android:largeHeap
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:42:9-33
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:34:9-43
	android:banner
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:33:9-45
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:36:9-69
	android:directBootAware
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:43:9-40
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:35:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:41:9-43
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:44:9-28
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:39:9-42
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:40:9-44
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:31:9-40
property#android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:9-118
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:95-115
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:19-94
activity#top.yogiczy.mytv.tv.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:47:9-60:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:52:13-56
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:50:13-36
	android:supportsPictureInPicture
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:53:13-52
	android:resizeableActivity
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:51:13-46
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:54:13-42
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:49:13-119
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:48:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:17-86
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:27-83
activity#top.yogiczy.mytv.tv.CrashHandlerActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:9-58
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:19-55
receiver#top.yogiczy.mytv.tv.BootReceiver
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:63:9-72:20
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:66:13-37
	android:permission
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:67:13-75
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:64:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:68:13-71:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:17-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:25-76
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:17-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:27-73
service#top.yogiczy.mytv.tv.HttpServerService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:9-54
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:18-51
service#top.yogiczy.mytv.tv.X5CorePreLoadService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:75:9-79:40
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:78:13-35
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:79:13-37
	android:permission
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:77:13-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:76:13-49
service#androidx.appcompat.app.AppLocalesMetadataHolderService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:80:9-87:19
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:82:13-36
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:81:13-82
meta-data#autoStoreLocales
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:84:13-86:36
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:86:13-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:85:13-44
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:94:13-96:54
	android:resource
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:96:17-51
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:95:17-67
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d4e98c92be75c2303e0d2810d3d016a9\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d4e98c92be75c2303e0d2810d3d016a9\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aff0a4e7236056764cabe678e059991a\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aff0a4e7236056764cabe678e059991a\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cab515afc7d794d03ee10473c40eb78\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cab515afc7d794d03ee10473c40eb78\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b06b60f527eac6792fa28b1cb5f9437\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b06b60f527eac6792fa28b1cb5f9437\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6f36c6f9b88206bbc0782dd48ec05c5\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6f36c6f9b88206bbc0782dd48ec05c5\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4119ce110c4cb24d80c3660a70b916b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4119ce110c4cb24d80c3660a70b916b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c8ee00287bca8e9af48e7446162a635e\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c8ee00287bca8e9af48e7446162a635e\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f02348ccb0e7f9cc920731d17c38172\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f02348ccb0e7f9cc920731d17c38172\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57909a5533f41ad27907668886664dc3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57909a5533f41ad27907668886664dc3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efbd2119718ae0bfeda059930e1194a7\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efbd2119718ae0bfeda059930e1194a7\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8e6007a452cea9a61deeb7ee9d7c128\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8e6007a452cea9a61deeb7ee9d7c128\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0390848ee78f173243bbaf7411fdc905\transformed\libvlc-all-3.6.2\AndroidManifest.xml:7:5-44
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0390848ee78f173243bbaf7411fdc905\transformed\libvlc-all-3.6.2\AndroidManifest.xml:7:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\901c62c253373a1ba61e071ec92e3ddd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\901c62c253373a1ba61e071ec92e3ddd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e809a4557f286a4df4416c5fc8b40c0d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e809a4557f286a4df4416c5fc8b40c0d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ac8297ac893d40065497418c73e02\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ac8297ac893d40065497418c73e02\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.5.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1cb8d8cb32f9b6b54ba7b00e42464438\transformed\material3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.5.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1cb8d8cb32f9b6b54ba7b00e42464438\transformed\material3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c818d827b91a2191cf3f4dcefe10f525\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c818d827b91a2191cf3f4dcefe10f525\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b953b9ef09835f700c6408491a803f0b\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b953b9ef09835f700c6408491a803f0b\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f42fcb51fd3bb7fa7d6a544151362c1e\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f42fcb51fd3bb7fa7d6a544151362c1e\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\af9619106ca5a74782ec42e5c552ec6a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\af9619106ca5a74782ec42e5c552ec6a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\96d010939395be32a69a992c882b4db4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\96d010939395be32a69a992c882b4db4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5849074be76a4c0d0f35e172fcf93d43\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5849074be76a4c0d0f35e172fcf93d43\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab96961d1dab3d7e40e5bfedaab514c4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab96961d1dab3d7e40e5bfedaab514c4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5333a80b0fe68b99990e90c2d086162\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5333a80b0fe68b99990e90c2d086162\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0615017894abf0bd7b33401f847396d0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0615017894abf0bd7b33401f847396d0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\63e1195e3a6573e9e7a9fde678dc92a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\63e1195e3a6573e9e7a9fde678dc92a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f1ad126e3dd2ecd6df0f4f55b684ab94\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f1ad126e3dd2ecd6df0f4f55b684ab94\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c97d9cfeeffdeb4b13a0bfd1d00fc97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c97d9cfeeffdeb4b13a0bfd1d00fc97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f012db532efca603faf7e2f1b1bb4867\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f012db532efca603faf7e2f1b1bb4867\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f2b5b25f7a8e645b3cd12bea3eb3a36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f2b5b25f7a8e645b3cd12bea3eb3a36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\833b0c813414bd66fb545cbc035dc1e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\833b0c813414bd66fb545cbc035dc1e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2ddc148ab2850d14a8acfd77bd2da82c\transformed\media3-ui-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2ddc148ab2850d14a8acfd77bd2da82c\transformed\media3-ui-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\70c6c26fa193f642fd9b05f0da7da929\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\70c6c26fa193f642fd9b05f0da7da929\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8cd342aac98369f7c46f3523fe2f8eba\transformed\media3-extractor-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8cd342aac98369f7c46f3523fe2f8eba\transformed\media3-extractor-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4116ba62e7fe4bb61d451346cb9ab9cb\transformed\media3-container-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4116ba62e7fe4bb61d451346cb9ab9cb\transformed\media3-container-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae9f3f6e20462a971f03c27309215647\transformed\media3-datasource-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae9f3f6e20462a971f03c27309215647\transformed\media3-datasource-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e3bb0a67f10c4c098f870af10e363468\transformed\media3-decoder-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e3bb0a67f10c4c098f870af10e363468\transformed\media3-decoder-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02d90d3994b46b644d1075128a27b19f\transformed\media3-database-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02d90d3994b46b644d1075128a27b19f\transformed\media3-database-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01078d44cb24a05a9754244885981d2\transformed\media3-common-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01078d44cb24a05a9754244885981d2\transformed\media3-common-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bf2510e092dbf826cc6b1c89f2dc6ce0\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bf2510e092dbf826cc6b1c89f2dc6ce0\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0fcc4916bb0dcddefd58238ed35b2596\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0fcc4916bb0dcddefd58238ed35b2596\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0766020c4843ec3150cc21dd20c9acb\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0766020c4843ec3150cc21dd20c9acb\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\099c27caf1c718359c373e4e428d1a48\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\099c27caf1c718359c373e4e428d1a48\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\151a69a7f886af7aa3234b82508a55dc\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\151a69a7f886af7aa3234b82508a55dc\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7ce58255463d36265f6049fe9390ff3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7ce58255463d36265f6049fe9390ff3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e421f90c22810ec885b6a02cd295f912\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e421f90c22810ec885b6a02cd295f912\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2d561beb03dfda8138c45e95fe5c0b80\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2d561beb03dfda8138c45e95fe5c0b80\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2217fe3dcf708380453a761385649ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2217fe3dcf708380453a761385649ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f0b99c239d9c219a4002298aed66c81\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f0b99c239d9c219a4002298aed66c81\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14841292f169b6b3d6cd3f049dd7da9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14841292f169b6b3d6cd3f049dd7da9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad01708dea991c04529fd04f8d79e493\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad01708dea991c04529fd04f8d79e493\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a6420713937a19eed0d0658ad58ff07d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a6420713937a19eed0d0658ad58ff07d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95f1b4c76ab5d4456c6d5d56e3ae51d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95f1b4c76ab5d4456c6d5d56e3ae51d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\84bbce96c3ac6552d3233b4673b0afa3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\84bbce96c3ac6552d3233b4673b0afa3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86e5380642e8f59dadd272587b78caaf\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86e5380642e8f59dadd272587b78caaf\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6792a5c31123b5b9417ca48841ad4abe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6792a5c31123b5b9417ca48841ad4abe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b4884e7705c0171a0acb90c93500610\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b4884e7705c0171a0acb90c93500610\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e92f510cb176552fb10380ec72670dc7\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e92f510cb176552fb10380ec72670dc7\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83ba0c5cb47302e8a83060ba4a68b507\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83ba0c5cb47302e8a83060ba4a68b507\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e32d17248dcbde9827dba710d49e7804\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e32d17248dcbde9827dba710d49e7804\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\638739b15b6613760119af1fe02bc62e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\638739b15b6613760119af1fe02bc62e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c95d115402668d25cc6e24420d4853\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c95d115402668d25cc6e24420d4853\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ecb62d8759e715b1b4fa0d8e66334372\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ecb62d8759e715b1b4fa0d8e66334372\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c29221ce9c68d83012cc3945ca400388\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c29221ce9c68d83012cc3945ca400388\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f97da89eac0fc41342000cdcaf05834f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f97da89eac0fc41342000cdcaf05834f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb9c3301855b64340e9a3ba90141e562\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb9c3301855b64340e9a3ba90141e562\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71ad99cb288e9a4c50441fb10934b125\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71ad99cb288e9a4c50441fb10934b125\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1c729ae53863038070962a189432046d\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1c729ae53863038070962a189432046d\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4258475a35a235d2942e638282bb8a92\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4258475a35a235d2942e638282bb8a92\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f570db518546982e31794f84e73cb1b\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f570db518546982e31794f84e73cb1b\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\189389f5b60b26e639e387bd453f0473\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0-beta03] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\189389f5b60b26e639e387bd453f0473\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0f0df49903dae0e54b022124708ba75\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e0f0df49903dae0e54b022124708ba75\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\797cb2bfa8ea4b49554b5b1246168244\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\797cb2bfa8ea4b49554b5b1246168244\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ef825cb11d8e1edcb8e7728c41c183cf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ef825cb11d8e1edcb8e7728c41c183cf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ee0e85defb66d723b5bacbd22e8fea5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ee0e85defb66d723b5bacbd22e8fea5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05ee3d42f5b1adab2edec5b49bbaa2be\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05ee3d42f5b1adab2edec5b49bbaa2be\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97871514283cc216ff487f321a0a5e37\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97871514283cc216ff487f321a0a5e37\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6da60ba0d9d2ebff88ff4791a942a33\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6da60ba0d9d2ebff88ff4791a942a33\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7573f74c3ad07832627b4ae047c2be13\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7573f74c3ad07832627b4ae047c2be13\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\834986d56ec82d0862a15e5cf454cc76\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\834986d56ec82d0862a15e5cf454cc76\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cbc108cc86e02b296c444579f1caa7e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2cbc108cc86e02b296c444579f1caa7e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ee21b4eecaca06e8d47bdb5dff6ef4a9\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ee21b4eecaca06e8d47bdb5dff6ef4a9\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7829d6bc7ee3b87b82f04f0307bb47b2\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7829d6bc7ee3b87b82f04f0307bb47b2\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2971bdc7d1c5a9df55a113e9413ea59f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2971bdc7d1c5a9df55a113e9413ea59f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d3ae0d91a79510abf6b8d344ff17e9c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d3ae0d91a79510abf6b8d344ff17e9c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f568cf557dd9151ad1dcecbd4a2479bc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f568cf557dd9151ad1dcecbd4a2479bc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\675067a6b0ef7dc58058a5633fb77705\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\675067a6b0ef7dc58058a5633fb77705\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3dac38bd6d7f7041169ac5fa81e3096b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3dac38bd6d7f7041169ac5fa81e3096b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed0655579c4da080363e79c6c8825972\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed0655579c4da080363e79c6c8825972\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d5b6915865010c3046c2c3a0f15f3a2f\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d5b6915865010c3046c2c3a0f15f3a2f\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc423ea9b0054663f9ce8a86023fe20a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc423ea9b0054663f9ce8a86023fe20a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d809ce6e79402ff8554e248bd93f9aa\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d809ce6e79402ff8554e248bd93f9aa\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\513dd212c35d3d333716e2a909830546\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\513dd212c35d3d333716e2a909830546\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4424f82f483dce040ec7343d9b1f6a83\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4424f82f483dce040ec7343d9b1f6a83\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b17df47565a6a7225ba5f3d11b7b305\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b17df47565a6a7225ba5f3d11b7b305\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dfce483f78b523ba9a9b9efc48adc9ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dfce483f78b523ba9a9b9efc48adc9ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f787107385cdc60946aee89b91096482\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f787107385cdc60946aee89b91096482\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d99eee0d757b58a98207ee4528f9950\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d99eee0d757b58a98207ee4528f9950\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aee4571b660d469380ad98cdc743496c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aee4571b660d469380ad98cdc743496c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0448581b0035e3c0171ba7bde0841803\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0448581b0035e3c0171ba7bde0841803\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8e8338334c40510b7dd32f129a64d13\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8e8338334c40510b7dd32f129a64d13\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3ddec477fbc1692c93f80ce9c318abc0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3ddec477fbc1692c93f80ce9c318abc0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8588e24114685cef204b72b563b8204b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8588e24114685cef204b72b563b8204b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3316c514475025ff5b55d4b14cb1f200\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3316c514475025ff5b55d4b14cb1f200\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\032d252a695fda8f9a172ed0dfcedb3d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\032d252a695fda8f9a172ed0dfcedb3d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b1e51be48c764f6676825adb9de0f4d\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83841ce76a960d9b7ad1149dd1e1c7f1\transformed\wrapper-android-3.2.3\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\83841ce76a960d9b7ad1149dd1e1c7f1\transformed\wrapper-android-3.2.3\AndroidManifest.xml:5:5-44
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\093673b58f5d13fd133f975cbf8dfa8a\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\093673b58f5d13fd133f975cbf8dfa8a\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\68aba0c7fefdd007012f01ba77858dd8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\68aba0c7fefdd007012f01ba77858dd8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c61c40d132e7ad1dd4c85d788c105d8c\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
meta-data#okhttp3.internal.platform.PlatformInitializer
ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
	android:value
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:18:17-49
	android:name
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:17:17-77
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
