package top.yogiczy.mytv.tv.ui.screen.settings.subcategories

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.tv.material3.ListItem
import androidx.tv.material3.RadioButton
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.R
import top.yogiczy.mytv.tv.ui.rememberChildPadding
import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents

@Composable
fun SettingsVideoPlayerPlaybackModeScreen(
    modifier: Modifier = Modifier,
    playbackModeProvider: () -> VideoPlayerPlaybackMode = { VideoPlayerPlaybackMode.RELOAD_URL },
    onPlaybackModeChanged: (VideoPlayerPlaybackMode) -> Unit = {},
    onBackPressed: () -> Unit = {},
) {
    val currentPlaybackMode = playbackModeProvider()
    val childPadding = rememberChildPadding()

    AppScreen(
        modifier = modifier.padding(top = 10.dp),
        header = { Text("${stringResource(R.string.ui_dashboard_module_settings)} / ${stringResource(R.string.ui_channel_view_player)} / ${stringResource(R.string.ui_player_view_playback_mode)}") },
        canBack = true,
        onBackPressed = onBackPressed,
    ) {
        LazyVerticalGrid(
            modifier = Modifier,
            columns = GridCells.Fixed(2),
            contentPadding = childPadding.copy(top = 10.dp).paddingValues,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
        ) {
            items(VideoPlayerPlaybackMode.entries) { mode ->
                ListItem(
                    modifier = Modifier.handleKeyEvents(
                        onSelect = { onPlaybackModeChanged(mode) },
                    ),
                    selected = mode == currentPlaybackMode,
                    onClick = {},
                    headlineContent = { Text(mode.label) },
                    trailingContent = {
                        RadioButton(
                            selected = mode == currentPlaybackMode,
                            onClick = {},
                        )
                    },
                )
            }
        }
    }
}

@Preview
@Composable
private fun SettingsVideoPlayerPlaybackModeScreenPreview() {
    MyTvTheme {
        SettingsVideoPlayerPlaybackModeScreen()
    }
}
