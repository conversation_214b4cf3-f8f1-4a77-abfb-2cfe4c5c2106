{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-76:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\efbd2119718ae0bfeda059930e1194a7\\transformed\\appcompat-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,945,1040,1140,1222,1319,1425,1502,1577,1668,1761,1858,1954,2048,2141,2236,2328,2419,2510,2588,2684,2779,2874,2971,3067,3165,3313,19683", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "940,1035,1135,1217,1314,1420,1497,1572,1663,1756,1853,1949,2043,2136,2231,2323,2414,2505,2583,2679,2774,2869,2966,3062,3160,3308,3402,19757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\20edd988a343bb05e5a649cb0d8f4996\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "56,57,58,59,60,61,62,267", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3821,3913,4014,4108,4202,4295,4389,20345", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3908,4009,4103,4197,4290,4384,4480,20441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\af9619106ca5a74782ec42e5c552ec6a\\transformed\\material-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "207", "startColumns": "4", "startOffsets": "16027", "endColumns": "84", "endOffsets": "16107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\aff0a4e7236056764cabe678e059991a\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "703,3478,3542,3604,3674,3744,4485,4576,4682,5161,5212,8454,8531,8741,15194,15272,15333,15390,15446,15505,15563,15617,15702,15758,15816,15870,15935,16112,16186,16258,16337,16411,16487,16609,16671,16733,16832,16911,16985,17035,17086,17152,17216,17285,17356,17427,17488,17559,17626,17686,17772,17851,17918,18001,18086,18160,18225,18301,18349,18422,18486,18562,18640,18702,18766,18829,18894,18974,19050,19128,19204,19258,19466,19762,19837,19978", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "845,3537,3599,3669,3739,3816,4571,4677,4750,5207,5269,8526,8585,8795,15267,15328,15385,15441,15500,15558,15612,15697,15753,15811,15865,15930,16022,16181,16253,16332,16406,16482,16604,16666,16728,16827,16906,16980,17030,17081,17147,17211,17280,17351,17422,17483,17554,17621,17681,17767,17846,17913,17996,18081,18155,18220,18296,18344,18417,18481,18557,18635,18697,18761,18824,18889,18969,19045,19123,19199,19253,19308,19530,19832,19905,20043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\2ddc148ab2850d14a8acfd77bd2da82c\\transformed\\media3-ui-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1568,1651,1739,1802,1869,1934,1988,2062,2135,2196,2258,2310,2368,2415,2476,2533,2595,2652,2713,2769,2824,2887,2936,2987,3052,3117,3166", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,48,50,64,64,48,60", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1563,1646,1734,1797,1864,1929,1983,2057,2130,2191,2253,2305,2363,2410,2471,2528,2590,2647,2708,2764,2819,2882,2931,2982,3047,3112,3161,3222"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,545,5274,5344,5413,5481,5558,5634,5688,5750,5824,5898,5960,6021,6080,6146,6234,6317,6405,6468,6535,6600,6654,6728,6801,6862,7485,7537,7595,7642,7703,7760,7822,7879,7940,7996,8051,8114,8163,8214,8279,8344,8393", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,48,50,64,64,48,60", "endOffsets": "377,540,698,5339,5408,5476,5553,5629,5683,5745,5819,5893,5955,6016,6075,6141,6229,6312,6400,6463,6530,6595,6649,6723,6796,6857,6919,7532,7590,7637,7698,7755,7817,7874,7935,7991,8046,8109,8158,8209,8274,8339,8388,8449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\675067a6b0ef7dc58058a5633fb77705\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "66,67,68,69,70,125,126,253,254,256,257,261,263,264,265,266,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4755,4832,4908,4993,5084,8590,8664,19313,19391,19535,19608,19910,20048,20129,20202,20274,20446,20519,20585", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "4827,4903,4988,5079,5156,8659,8736,19386,19461,19603,19678,19973,20124,20197,20269,20340,20514,20580,20696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\151a69a7f886af7aa3234b82508a55dc\\transformed\\media3-exoplayer-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6924,6980,7036,7094,7147,7219,7273,7348,7426", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "6975,7031,7089,7142,7214,7268,7343,7421,7480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\1cb8d8cb32f9b6b54ba7b00e42464438\\transformed\\material3\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4145,4237,4320,4402,4487,4579,4669,4749,4841,4923,5021,5115,5208,5303,5387,5483,5579,5676,5784,5864,5968,6068,6160,6250,6349", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,93,91,82,81,84,91,89,79,91,81,97,93,92,94,83,95,95,96,107,79,103,99,91,89,98,99", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4140,4232,4315,4397,4482,4574,4664,4744,4836,4918,5016,5110,5203,5298,5382,5478,5574,5671,5779,5859,5963,6063,6155,6245,6344,6444"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8800,8904,9007,9111,9213,9305,9393,9497,9605,9708,9823,9905,10001,10085,10174,10280,10394,10495,10605,10713,10821,10937,11044,11145,11249,11355,11440,11535,11640,11749,11839,11937,12035,12145,12260,12360,12451,12524,12614,12703,12796,12890,12982,13065,13147,13232,13324,13414,13494,13586,13668,13766,13860,13953,14048,14132,14228,14324,14421,14529,14609,14713,14813,14905,14995,15094", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,93,91,82,81,84,91,89,79,91,81,97,93,92,94,83,95,95,96,107,79,103,99,91,89,98,99", "endOffsets": "8899,9002,9106,9208,9300,9388,9492,9600,9703,9818,9900,9996,10080,10169,10275,10389,10490,10600,10708,10816,10932,11039,11140,11244,11350,11435,11530,11635,11744,11834,11932,12030,12140,12255,12355,12446,12519,12609,12698,12791,12885,12977,13060,13142,13227,13319,13409,13489,13581,13663,13761,13855,13948,14043,14127,14223,14319,14416,14524,14604,14708,14808,14900,14990,15089,15189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0615017894abf0bd7b33401f847396d0\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "50,271,272", "startColumns": "4,4,4", "startOffsets": "3407,20701,20782", "endColumns": "70,80,76", "endOffsets": "3473,20777,20854"}}]}]}