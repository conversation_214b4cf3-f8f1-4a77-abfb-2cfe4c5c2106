{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-76:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0615017894abf0bd7b33401f847396d0\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,91", "endOffsets": "137,225,317"}, "to": {"startLines": "50,271,272", "startColumns": "4,4,4", "startOffsets": "3740,24162,24250", "endColumns": "86,87,91", "endOffsets": "3822,24245,24337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\af9619106ca5a74782ec42e5c552ec6a\\transformed\\material-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "207", "startColumns": "4", "startOffsets": "18704", "endColumns": "95", "endOffsets": "18795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\151a69a7f886af7aa3234b82508a55dc\\transformed\\media3-exoplayer-1.8.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8017,8088,8149,8221,8291,8368,8449,8552,8649", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "8083,8144,8216,8286,8363,8444,8547,8644,8722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\20edd988a343bb05e5a649cb0d8f4996\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "56,57,58,59,60,61,62,267", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4282,4384,4487,4589,4693,4796,4897,23799", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "4379,4482,4584,4688,4791,4892,5014,23895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\1cb8d8cb32f9b6b54ba7b00e42464438\\transformed\\material3\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4937,5045,5132,5229,5325,5433,5529,5609,5716,5804,5902,6015,6110,6221,6311,6426,6528,6641,6773,6853,6997,7127,7234,7331,7441", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,104,107,86,96,95,107,95,79,106,87,97,112,94,110,89,114,101,112,131,79,143,129,106,96,109,108", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4932,5040,5127,5224,5320,5428,5524,5604,5711,5799,5897,6010,6105,6216,6306,6421,6523,6636,6768,6848,6992,7122,7229,7326,7436,7545"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10201,10321,10447,10573,10696,10796,10890,11001,11153,11271,11428,11513,11618,11718,11820,11943,12076,12186,12322,12464,12595,12799,12933,13057,13187,13321,13422,13520,13638,13769,13868,13970,14083,14221,14367,14481,14590,14666,14764,14864,14978,15083,15191,15278,15375,15471,15579,15675,15755,15862,15950,16048,16161,16256,16367,16457,16572,16674,16787,16919,16999,17143,17273,17380,17477,17587", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,104,107,86,96,95,107,95,79,106,87,97,112,94,110,89,114,101,112,131,79,143,129,106,96,109,108", "endOffsets": "10316,10442,10568,10691,10791,10885,10996,11148,11266,11423,11508,11613,11713,11815,11938,12071,12181,12317,12459,12590,12794,12928,13052,13182,13316,13417,13515,13633,13764,13863,13965,14078,14216,14362,14476,14585,14661,14759,14859,14973,15078,15186,15273,15370,15466,15574,15670,15750,15857,15945,16043,16156,16251,16362,16452,16567,16669,16782,16914,16994,17138,17268,17375,17472,17582,17691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\aff0a4e7236056764cabe678e059991a\\transformed\\material-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1084,1149,1248,1314,1374,1476,1538,1614,1672,1750,1815,1869,1986,2050,2114,2168,2248,2382,2468,2555,2658,2754,2843,2979,3064,3152,3304,3399,3482,3540,3592,3658,3737,3819,3890,3977,4053,4130,4207,4278,4388,4495,4575,4672,4772,4846,4927,5032,5090,5178,5245,5336,5428,5490,5554,5617,5686,5789,5896,6001,6106,6168,6224,6308,6402,6480", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "266,346,429,516,622,721,815,925,1017,1079,1144,1243,1309,1369,1471,1533,1609,1667,1745,1810,1864,1981,2045,2109,2163,2243,2377,2463,2550,2653,2749,2838,2974,3059,3147,3299,3394,3477,3535,3587,3653,3732,3814,3885,3972,4048,4125,4202,4273,4383,4490,4570,4667,4767,4841,4922,5027,5085,5173,5240,5331,5423,5485,5549,5612,5681,5784,5891,5996,6101,6163,6219,6303,6397,6475,6551"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,3827,3907,3990,4077,4183,5019,5113,5223,5790,5852,9789,9888,10141,17696,17798,17860,17936,17994,18072,18137,18191,18308,18372,18436,18490,18570,18800,18886,18973,19076,19172,19261,19397,19482,19570,19722,19817,19900,19958,20010,20076,20155,20237,20308,20395,20471,20548,20625,20696,20806,20913,20993,21090,21190,21264,21345,21450,21508,21596,21663,21754,21846,21908,21972,22035,22104,22207,22314,22419,22524,22586,22817,23157,23251,23407", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "986,3902,3985,4072,4178,4277,5108,5218,5310,5847,5912,9883,9949,10196,17793,17855,17931,17989,18067,18132,18186,18303,18367,18431,18485,18565,18699,18881,18968,19071,19167,19256,19392,19477,19565,19717,19812,19895,19953,20005,20071,20150,20232,20303,20390,20466,20543,20620,20691,20801,20908,20988,21085,21185,21259,21340,21445,21503,21591,21658,21749,21841,21903,21967,22030,22099,22202,22309,22414,22519,22581,22637,22896,23246,23324,23478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\efbd2119718ae0bfeda059930e1194a7\\transformed\\appcompat-1.7.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "991,1098,1204,1315,1406,1511,1633,1711,1786,1877,1970,2071,2165,2265,2359,2454,2553,2644,2735,2817,2926,3030,3129,3241,3353,3474,3639,23074", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "1093,1199,1310,1401,1506,1628,1706,1781,1872,1965,2066,2160,2260,2354,2449,2548,2639,2730,2812,2921,3025,3124,3236,3348,3469,3634,3735,23152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\675067a6b0ef7dc58058a5633fb77705\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,375,474,578,668,754,855,942,1030,1116,1203,1281,1373,1450,1524,1597,1673,1740", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "283,370,469,573,663,749,850,937,1025,1111,1198,1276,1368,1445,1519,1592,1668,1735,1854"}, "to": {"startLines": "66,67,68,69,70,125,126,253,254,256,257,261,263,264,265,266,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5315,5410,5497,5596,5700,9954,10040,22642,22729,22901,22987,23329,23483,23575,23652,23726,23900,23976,24043", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "5405,5492,5591,5695,5785,10035,10136,22724,22812,22982,23069,23402,23570,23647,23721,23794,23971,24038,24157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\2ddc148ab2850d14a8acfd77bd2da82c\\transformed\\media3-ui-1.8.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1950,2072,2191,2270,2351,2423,2500,2596,2691,2760,2825,2878,2936,2986,3047,3113,3175,3236,3306,3367,3431,3497,3553,3615,3691,3767,3821", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,60,63,65,55,61,75,75,53,65", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1945,2067,2186,2265,2346,2418,2495,2591,2686,2755,2820,2873,2931,2981,3042,3108,3170,3231,3301,3362,3426,3492,3548,3610,3686,3762,3816,3882"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,389,616,5917,6018,6118,6209,6302,6411,6488,6555,6647,6739,6816,6887,6948,7022,7142,7264,7383,7462,7543,7615,7692,7788,7883,7952,8727,8780,8838,8888,8949,9015,9077,9138,9208,9269,9333,9399,9455,9517,9593,9669,9723", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,60,63,65,55,61,75,75,53,65", "endOffsets": "384,611,815,6013,6113,6204,6297,6406,6483,6550,6642,6734,6811,6882,6943,7017,7137,7259,7378,7457,7538,7610,7687,7783,7878,7947,8012,8775,8833,8883,8944,9010,9072,9133,9203,9264,9328,9394,9450,9512,9588,9664,9718,9784"}}]}]}