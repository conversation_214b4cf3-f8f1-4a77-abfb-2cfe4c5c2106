1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.chinablue.tv"
4    android:versionCode="1"
5    android:versionName="2.0.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.touchscreen"
12-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:6:9-52
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.software.leanback"
15-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:9:9-49
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:10:9-33
17
18    <queries>
18-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:12:5-14:15
19        <package android:name="com.google.android.webview" />
19-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:9-62
19-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:18-59
20    </queries>
21
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:5-81
23-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
24-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:5-83
24-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:22-80
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:5-80
25-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:5-67
26-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:22-65
27    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- X5权限需求 -->
27-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:5-76
27-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:22-73
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- X5权限需求 -->
28-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:5-79
28-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:22-76
29    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- X5权限需求 -->
29-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:5-67
29-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:22-65
30    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- X5权限需求 -->
30-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:5-75
30-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:22-72
31    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- X5权限需求 -->
31-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:5-81
31-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:22-78
32    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
32-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:26:5-28:40
32-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:27:9-66
33
34    <permission
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
39
40    <application
40-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:30:5-103:19
41        android:name="top.yogiczy.mytv.tv.MyTVApplication"
41-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:31:9-40
42        android:allowBackup="true"
42-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:32:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20edd988a343bb05e5a649cb0d8f4996\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
44        android:banner="@drawable/ic_banner"
44-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:33:9-45
45        android:debuggable="true"
46        android:directBootAware="false"
46-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:43:9-40
47        android:extractNativeLibs="true"
47-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:45:9-41
48        android:hardwareAccelerated="true"
48-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:41:9-43
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:34:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:35:9-41
51        android:largeHeap="true"
51-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:42:9-33
52        android:localeConfig="@xml/_generated_res_locale_config"
53        android:networkSecurityConfig="@xml/network_security_config"
53-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:36:9-69
54        android:requestLegacyExternalStorage="true"
54-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:37:9-52
55        android:supportsRtl="true"
55-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:38:9-35
56        android:testOnly="true"
57        android:theme="@style/Theme.MyTV"
57-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:39:9-42
58        android:usesCleartextTraffic="true" >
58-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:40:9-44
59        <property
59-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:9-118
60            android:name="android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY"
60-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:19-94
61            android:value="true" />
61-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:95-115
62
63        <activity
63-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:47:9-60:20
64            android:name="top.yogiczy.mytv.tv.MainActivity"
64-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:48:13-41
65            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
65-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:49:13-119
66            android:exported="true"
66-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:50:13-36
67            android:resizeableActivity="true"
67-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:51:13-46
68            android:screenOrientation="sensorLandscape"
68-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:52:13-56
69            android:supportsPictureInPicture="true" >
69-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:53:13-52
70            <intent-filter>
70-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:55:13-59:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:17-69
71-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:17-77
73-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:27-74
74                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
74-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:17-86
74-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:27-83
75            </intent-filter>
76        </activity>
77        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />
77-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:9-58
77-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:19-55
78
79        <receiver
79-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:63:9-72:20
80            android:name="top.yogiczy.mytv.tv.BootReceiver"
80-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:64:13-41
81            android:enabled="true"
81-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:65:13-35
82            android:exported="false"
82-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:66:13-37
83            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
83-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:67:13-75
84            <intent-filter>
84-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:68:13-71:29
85                <action android:name="android.intent.action.BOOT_COMPLETED" />
85-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:17-79
85-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:25-76
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:17-76
87-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:27-73
88            </intent-filter>
89        </receiver>
90
91        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
91-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:9-54
91-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:18-51
92        <service
92-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:75:9-79:40
93            android:name="top.yogiczy.mytv.tv.X5CorePreLoadService"
93-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:76:13-49
94            android:enabled="true"
94-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:78:13-35
95            android:exported="false"
95-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:79:13-37
96            android:permission="android.permission.BIND_JOB_SERVICE" />
96-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:77:13-69
97        <service
97-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:80:9-87:19
98            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
98-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:81:13-82
99            android:enabled="false"
99-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:82:13-36
100            android:exported="false" >
100-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:83:13-37
101            <meta-data
101-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:84:13-86:36
102                android:name="autoStoreLocales"
102-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:85:13-44
103                android:value="true" />
103-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:86:13-33
104        </service>
105
106        <provider
107            android:name="androidx.core.content.FileProvider"
107-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:90:13-62
108            android:authorities="com.chinablue.tv.FileProvider"
108-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:91:13-64
109            android:exported="false"
109-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:92:13-37
110            android:grantUriPermissions="true" >
110-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:93:13-47
111            <meta-data
111-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:94:13-96:54
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:95:17-67
113                android:resource="@xml/file_paths" />
113-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:96:17-51
114        </provider>
115
116        <!-- <meta-data -->
117        <!-- android:name="io.sentry.auto-init" -->
118        <!-- android:value="false" /> -->
119
120        <provider
120-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.chinablue.tv.androidx-startup"
122-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ab6e98d1158d6aa494e1aac43adb164\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57e573628a98fa15f97410f9b57936c4\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
131                android:name="okhttp3.internal.platform.PlatformInitializer"
131-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:17:17-77
132                android:value="androidx.startup" />
132-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97fdd35e01899c8097fc048b424a8c97\transformed\okhttp-release\AndroidManifest.xml:18:17-49
133            <meta-data
133-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
134-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
135                android:value="androidx.startup" />
135-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
136        </provider>
137
138        <activity
138-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
139            android:name="androidx.compose.ui.tooling.PreviewActivity"
139-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
140            android:exported="true" />
140-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02c49d462a42a87d703355c0e4b460f3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
141        <activity
141-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
142            android:name="androidx.activity.ComponentActivity"
142-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
143            android:exported="true"
143-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
144            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
144-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e8c903270ba1ede59876b0be022218ab\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
145
146        <receiver
146-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
147            android:name="androidx.profileinstaller.ProfileInstallReceiver"
147-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
148            android:directBootAware="false"
148-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
149            android:enabled="true"
149-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
150            android:exported="true"
150-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
151            android:permission="android.permission.DUMP" >
151-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
153                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
153-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
156                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
156-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
156-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
159                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
159-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
159-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
160            </intent-filter>
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
162                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
162-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
162-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52e155cc81077c966c35b9379ce47526\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
163            </intent-filter>
164        </receiver>
165    </application>
166
167</manifest>
