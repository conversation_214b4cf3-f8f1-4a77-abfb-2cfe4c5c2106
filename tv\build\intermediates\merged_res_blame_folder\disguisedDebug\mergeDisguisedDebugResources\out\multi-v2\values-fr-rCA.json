{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-76:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0615017894abf0bd7b33401f847396d0\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,94", "endOffsets": "140,228,323"}, "to": {"startLines": "50,271,272", "startColumns": "4,4,4", "startOffsets": "3714,24163,24251", "endColumns": "89,87,94", "endOffsets": "3799,24246,24341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\151a69a7f886af7aa3234b82508a55dc\\transformed\\media3-exoplayer-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8013,8087,8152,8230,8302,8385,8461,8558,8651", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "8082,8147,8225,8297,8380,8456,8553,8646,8738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\aff0a4e7236056764cabe678e059991a\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,3804,3911,4019,4101,4202,5022,5122,5244,5807,5872,9824,9921,10175,17818,17910,17977,18051,18112,18191,18255,18309,18425,18484,18546,18600,18682,18900,18992,19067,19162,19243,19327,19471,19550,19631,19778,19871,19950,20005,20056,20122,20201,20282,20353,20433,20505,20583,20658,20730,20841,20938,21015,21113,21211,21289,21370,21470,21527,21611,21677,21760,21847,21909,21973,22036,22112,22214,22321,22418,22524,22583,22817,23159,23246,23399", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "959,3906,4014,4096,4197,4294,5117,5239,5324,5867,5933,9916,9996,10232,17905,17972,18046,18107,18186,18250,18304,18420,18479,18541,18595,18677,18806,18987,19062,19157,19238,19322,19466,19545,19626,19773,19866,19945,20000,20051,20117,20196,20277,20348,20428,20500,20578,20653,20725,20836,20933,21010,21108,21206,21284,21365,21465,21522,21606,21672,21755,21842,21904,21968,22031,22107,22209,22316,22413,22519,22578,22633,22901,23241,23318,23475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\675067a6b0ef7dc58058a5633fb77705\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,295,383,481,587,674,754,848,940,1027,1108,1193,1269,1354,1429,1507,1581,1660,1729", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "290,378,476,582,669,749,843,935,1022,1103,1188,1264,1349,1424,1502,1576,1655,1724,1846"}, "to": {"startLines": "66,67,68,69,70,125,126,253,254,256,257,261,263,264,265,266,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5329,5428,5516,5614,5720,10001,10081,22638,22730,22906,22987,23323,23480,23565,23640,23718,23893,23972,24041", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "5423,5511,5609,5715,5802,10076,10170,22725,22812,22982,23067,23394,23560,23635,23713,23787,23967,24036,24158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\2ddc148ab2850d14a8acfd77bd2da82c\\transformed\\media3-ui-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3499,3564,3643,3722,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3494,3559,3638,3717,3771,3837"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,594,5938,6027,6118,6197,6295,6392,6471,6537,6634,6731,6796,6859,6923,6995,7116,7242,7367,7442,7530,7603,7683,7782,7883,7949,8743,8796,8854,8902,8963,9030,9107,9174,9246,9304,9363,9429,9481,9546,9625,9704,9758", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "375,589,776,6022,6113,6192,6290,6387,6466,6532,6629,6726,6791,6854,6918,6990,7111,7237,7362,7437,7525,7598,7678,7777,7878,7944,8008,8791,8849,8897,8958,9025,9102,9169,9241,9299,9358,9424,9476,9541,9620,9699,9753,9819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\af9619106ca5a74782ec42e5c552ec6a\\transformed\\material-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "207", "startColumns": "4", "startOffsets": "18811", "endColumns": "88", "endOffsets": "18895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\efbd2119718ae0bfeda059930e1194a7\\transformed\\appcompat-1.7.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "964,1075,1182,1292,1379,1485,1615,1700,1780,1871,1964,2062,2157,2257,2350,2443,2538,2629,2720,2806,2916,3027,3130,3241,3349,3456,3615,23072", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1070,1177,1287,1374,1480,1610,1695,1775,1866,1959,2057,2152,2252,2345,2438,2533,2624,2715,2801,2911,3022,3125,3236,3344,3451,3610,3709,23154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\20edd988a343bb05e5a649cb0d8f4996\\transformed\\core-1.16.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,267", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4299,4397,4499,4598,4700,4804,4908,23792", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4392,4494,4593,4695,4799,4903,5017,23888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\1cb8d8cb32f9b6b54ba7b00e42464438\\transformed\\material3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4932,5047,5163,5253,5339,5428,5535,5636,5716,5825,5910,6007,6118,6211,6315,6403,6519,6620,6729,6851,6931,7076,7207,7317,7414,7523", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,114,115,89,85,88,106,100,79,108,84,96,110,92,103,87,115,100,108,121,79,144,130,109,96,108,112", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4927,5042,5158,5248,5334,5423,5530,5631,5711,5820,5905,6002,6113,6206,6310,6398,6514,6615,6724,6846,6926,7071,7202,7312,7409,7518,7631"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10237,10384,10529,10651,10799,10925,11019,11131,11273,11392,11551,11635,11736,11837,11938,12059,12194,12300,12450,12596,12732,12934,13063,13181,13304,13437,13539,13644,13768,13896,13998,14110,14215,14360,14512,14621,14730,14808,14901,14996,15114,15229,15345,15435,15521,15610,15717,15818,15898,16007,16092,16189,16300,16393,16497,16585,16701,16802,16911,17033,17113,17258,17389,17499,17596,17705", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,114,115,89,85,88,106,100,79,108,84,96,110,92,103,87,115,100,108,121,79,144,130,109,96,108,112", "endOffsets": "10379,10524,10646,10794,10920,11014,11126,11268,11387,11546,11630,11731,11832,11933,12054,12189,12295,12445,12591,12727,12929,13058,13176,13299,13432,13534,13639,13763,13891,13993,14105,14210,14355,14507,14616,14725,14803,14896,14991,15109,15224,15340,15430,15516,15605,15712,15813,15893,16002,16087,16184,16295,16388,16492,16580,16696,16797,16906,17028,17108,17253,17384,17494,17591,17700,17813"}}]}]}