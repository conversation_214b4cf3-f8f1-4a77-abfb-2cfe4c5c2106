[versions]
agp = "8.11.1"
coil = "2.7.0"
desugar_jdk_libs = "2.1.5"
coreKtx = "1.16.0"
kotlin = "2.2.0"
appcompat = "1.7.1"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
kotlinxCollectionsImmutable = "0.4.0"
lifecycleRuntimeKtx = "2.9.1"
activityCompose = "1.10.1"
media3 = "1.8.0"
composeBom = "2025.06.01"
okhttp = "5.1.0"
qrose = "1.0.1"
kotlinx-serialization = "1.9.0"
ksp = "2.2.0-2.0.2"
androidasync = "3.1.0"
compose-foundation = "1.8.3"
tinypinyin = "2.0.3.RELEASE"
androidxMaterial = "1.5.0-alpha01"
tvMaterial = "1.1.0-alpha01"
navigationCompose = "2.9.2"
android_material = "1.12.0"
sentry = "5.8.0"
quickjs = "3.2.3"
libvlcAll = "3.6.2"
minSdk = "21"
targetSdk = "36"
compileSdk = "36"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coil" }
coil-svg = { module = "io.coil-kt:coil-svg", version.ref = "coil" }
desugar_jdk_libs = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugar_jdk_libs" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
android-material = {module = "com.google.android.material:material", version.ref = "android_material"}
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3" }
androidx-media3-exoplayer-hls = { group = "androidx.media3", name = "media3-exoplayer-hls", version.ref = "media3" }
androidx-media3-exoplayer-smoothstreaming = { group = "androidx.media3", name = "media3-exoplayer-smoothstreaming", version.ref = "media3" }
androidx-media3-exoplayer-rtsp = { group = "androidx.media3", name = "media3-exoplayer-rtsp", version.ref = "media3" }
androidx-media3-exoplayer-dash = { group = "androidx.media3", name = "media3-exoplayer-dash", version.ref = "media3" }
androidx-media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3" }
androidx-media3-common = { group = "androidx.media3", name = "media3-common", version.ref = "media3" }
androidx-media3-datasource-rtmp = { group = "androidx.media3", name = "media3-datasource-rtmp", version.ref = "media3" }
kotlinx-collections-immutable = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinxCollectionsImmutable" }
kotlinx-serialization = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
qrose = { module = "io.github.alexzhirkevich:qrose", version.ref = "qrose" }
androidasync = { module = "com.koushikdutta.async:androidasync", version.ref = "androidasync" }
androidx-tv-material = { group = "androidx.tv", name = "tv-material", version.ref = "tvMaterial" }
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended" }
androidx-compose-foundation-base = { module = "androidx.compose.foundation:foundation", version.ref = "compose-foundation" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3", version.ref = "androidxMaterial" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
tinypinyin = { module = "io.github.biezhi:TinyPinyin", version.ref = "tinypinyin" }
quickjs-kt = { group = "wang.harlon.quickjs", name = "wrapper-android", version.ref = "quickjs" }
libvlc = { group = "org.videolan.android", name = "libvlc-all", version.ref = "libvlcAll" }
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
android-library = { id = "com.android.library", version.ref = "agp" }
sentry-android-gradle = { id = "io.sentry.android.gradle", version.ref = "sentry" }
