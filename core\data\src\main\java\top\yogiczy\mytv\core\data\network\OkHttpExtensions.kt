package top.yogiczy.mytv.core.data.network

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import android.content.Context
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.ResponseBody.Companion.toResponseBody
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.internal.closeQuietly
import java.io.IOException
import java.net.URLConnection
import kotlin.coroutines.resumeWithException

@OptIn(ExperimentalCoroutinesApi::class)
suspend fun Call.await(): Response = suspendCancellableCoroutine { continuation ->
    enqueue(
        object : Callback {
            override fun onResponse(call: Call, response: Response) {
                continuation.resume(response) {
                    response.closeQuietly()
                }
            }

            override fun onFailure(call: Call, e: IOException) {
                continuation.resumeWithException(e)
            }
        }
    )

    continuation.invokeOnCancellation {
        try {
            cancel()
        } catch (t: Throwable) {
            // Ignore cancel exception
        }
    }
}

suspend fun <T> String.requestMaybeAsset(
    context: Context,
    builder: (Request.Builder) -> Request.Builder = { it -> it },
    action: suspend CoroutineScope.(ResponseBody) -> T,
) : T? {
    val url = this
    val assetPrefix = "file:///android_asset/"
    return if (url.startsWith(assetPrefix, ignoreCase = true)) {
        requestAsset(context, action)
    } else {
        request(builder, action)
    }
}

suspend fun <T> String.request(
    builder: (Request.Builder) -> Request.Builder = { it -> it },
    action: suspend CoroutineScope.(ResponseBody) -> T,
) = request(builder) { response, _ -> response.body?.let { action(it) } }

suspend fun <T> String.request(
    builder: (Request.Builder) -> Request.Builder = { it -> it },
    action: suspend CoroutineScope.(Response, Request) -> T,
): T {
    val url = this

    return withContext(Dispatchers.IO) {
        val client = OkHttpClient.Builder()
            .sslSocketFactory(
                TrustAllSSLSocketFactory.sslSocketFactory,
                TrustAllSSLSocketFactory.trustManager
            )
            .hostnameVerifier { _, _ -> true }
            .followRedirects(true).build()
        val request = Request.Builder()
            .url(url)
            .let(builder)
            .build()

        val response = client.newCall(request).await()

        if (!response.isSuccessful) throw Exception("${response.code}: ${response.message}")

        action(response, response.request)
    }
}

suspend fun <T> String.requestAsset(
    context: Context,
    action: suspend CoroutineScope.(ResponseBody) -> T,
): T {
    val url = this
    val assetPrefix = "file:///android_asset/"
    return withContext(Dispatchers.IO) {
        val path = url.removePrefix(assetPrefix)
        val body = context.assets.open(path).use { input ->
            val bytes = input.readBytes()
            val mediaType = URLConnection
                .guessContentTypeFromName(path)
                ?.toMediaTypeOrNull()
            bytes.toResponseBody(mediaType)
        } ?: ResponseBody.create(null, ByteArray(0))
        action(body)
    }
}