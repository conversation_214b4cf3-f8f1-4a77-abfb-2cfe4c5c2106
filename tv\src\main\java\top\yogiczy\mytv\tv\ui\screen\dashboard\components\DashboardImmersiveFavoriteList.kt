package top.yogiczy.mytv.tv.ui.screen.dashboard.components

import android.graphics.Bitmap
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Border
import androidx.tv.material3.ClickableSurfaceDefaults
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.Surface
import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM
import top.yogiczy.mytv.core.data.entities.channel.Channel
import top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.data.entities.epg.EpgList
import top.yogiczy.mytv.core.data.entities.epg.EpgList.Companion.recentProgramme
import top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeRecent
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource
import top.yogiczy.mytv.core.util.utils.urlHost
import top.yogiczy.mytv.core.util.utils.M3u8AnalysisUtil
import top.yogiczy.mytv.tv.ui.material.itemsFirstFocused
import top.yogiczy.mytv.tv.ui.material.LazyRow
import top.yogiczy.mytv.tv.ui.utils.Configs
import top.yogiczy.mytv.tv.ui.utils.ifElse
import top.yogiczy.mytv.tv.ui.utils.focusOnLaunched
import top.yogiczy.mytv.tv.ui.rememberChildPadding
import top.yogiczy.mytv.tv.ui.screen.channels.components.rememberEpgProgrammeRecent
import top.yogiczy.mytv.tv.ui.screen.channels.components.ChannelsChannelItemWithoutPreview
import top.yogiczy.mytv.tv.ui.screen.dashboard.components.DashboardFavoriteList
import kotlinx.coroutines.launch

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun DashboardImmersiveFavoriteList(
    modifier: Modifier = Modifier,
    channelFavoriteListProvider: () -> ChannelFavoriteList = { ChannelFavoriteList() },
    onChannelSelected: (Channel) -> Unit = {},
    onChannelUnFavorite: (Channel) -> Unit = {},
    epgListProvider: () -> EpgList = { EpgList() },
    currentIptvSourceProvider: () -> IptvSource = { IptvSource() },
    toSettingsIptvSourceScreen: () -> Unit = {},
    onReload: () -> Unit = {},
    onPreviewChanged: (Bitmap?) -> Unit = {},
) {
    val childPadding = rememberChildPadding()
    val channelFavoriteList = channelFavoriteListProvider()
    val epgList = epgListProvider()
    val coroutineScope = rememberCoroutineScope()

    if(  !settingsVM.uiShowChannelLogo || !settingsVM.uiShowChannelPreview 
        || channelFavoriteList.isEmpty() || !settingsVM.iptvChannelFavoriteEnable
    ) {
        Box(
            modifier = modifier
        ){
            DashboardHeader(
                modifier = Modifier.align(Alignment.TopStart),
                currentIptvSourceProvider = currentIptvSourceProvider,
                toSettingsIptvSourceScreen = toSettingsIptvSourceScreen,
                onReload = onReload,
            )
            Spacer(modifier = Modifier.height(8.dp))
            if (settingsVM.iptvChannelFavoriteEnable) {
                DashboardFavoriteList(
                    modifier = Modifier.align(Alignment.BottomStart),
                    channelFavoriteListProvider = channelFavoriteListProvider,
                    onChannelSelected = onChannelSelected,
                    onChannelUnFavorite = onChannelUnFavorite,
                    epgListProvider = epgListProvider,
                )
            }
        }
    }else{
        var isPicAvailable by remember { mutableStateOf(false) }
        // val screenHeightDp by remember(LocalConfiguration.current.screenHeightDp){
        //     mutableStateOf(LocalConfiguration.current.screenHeightDp.dp)
        // }
        var currentChannelFavorite by remember { mutableStateOf(if (channelFavoriteList.isNotEmpty()) channelFavoriteList[0]?.channel else null) }
        LaunchedEffect(currentChannelFavorite) {
            coroutineScope.launch {
                val line = currentChannelFavorite?.lineList?.firstOrNull { line ->
                    Configs.iptvChannelLinePlayableUrlList.contains(line.url) && line.hybridType == ChannelLine.HybridType.None
                } ?: currentChannelFavorite?.lineList?.firstOrNull { line ->
                    Configs.iptvChannelLinePlayableHostList.contains(line.url.urlHost())  && line.hybridType == ChannelLine.HybridType.None
                } ?: currentChannelFavorite?.lineList?.first()
                if (line != null) {
                    onPreviewChanged(M3u8AnalysisUtil.getFirstFrame(line))
                    isPicAvailable = true
                }else {
                    onPreviewChanged(null)
                    isPicAvailable = false
                }
            }
        }
        Box(
            modifier = modifier.then(
                if (isPicAvailable)
                    Modifier.height(LocalConfiguration.current.screenHeightDp.dp)
                else
                    Modifier.height(350.dp)
            )
        ){
            DashboardHeader(
                modifier = Modifier.align(Alignment.TopStart),
                currentIptvSourceProvider = currentIptvSourceProvider,
                toSettingsIptvSourceScreen = toSettingsIptvSourceScreen,
                onReload = onReload,
            )
            if(currentChannelFavorite != null){
                DashboardChannelInfoHeader(
                    modifier = Modifier.align(Alignment.CenterStart),
                    channelProvider = { currentChannelFavorite!! },
                    epgListProvider = { epgList }
                )
            }

            Spacer(modifier = Modifier.height(8.dp))
            
            LazyRow(
                modifier = Modifier.align(Alignment.BottomStart),
                horizontalArrangement = Arrangement.spacedBy(20.dp),
                contentPadding = PaddingValues(start = childPadding.start, end = childPadding.end),
            ) { runtime ->
                itemsFirstFocused(channelFavoriteList, runtime) { itemModifier, channel ->
                    ChannelsChannelItemWithoutPreview(
                        modifier = itemModifier,
                        channelProvider = { channel.channel },
                        onChannelSelected = { onChannelSelected(channel.channel) },
                        onChannelFocused = { currentChannelFavorite = it },
                        onChannelFavoriteToggle = { onChannelUnFavorite(channel.channel) },
                        recentEpgProgrammeProvider = { epgList.recentProgramme(channel.channel) },
                    )
                }
            }
        }
    }
}

@Composable
fun DashboardChannelInfoHeader(
    modifier: Modifier = Modifier,
    channelProvider: () -> Channel = { Channel() },
    epgListProvider: () -> EpgList? = { null },
) {
    val channel = channelProvider()
    val recentEpgProgramme = rememberEpgProgrammeRecent({epgListProvider()?.recentProgramme(channel)})

    Column(
        modifier = modifier
            .fillMaxWidth(0.8f),
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        Text(
            channel.name,
            maxLines = 1,
            style = MaterialTheme.typography.headlineLarge.copy(
                fontWeight = FontWeight.Bold
            ),
            color = Color.White,
        )
        Text(
            recentEpgProgramme?.now?.title ?: "",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.headlineMedium,
            color = Color.White,
        )
        Text(
            recentEpgProgramme?.now?.description ?: "",
            maxLines = 3,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White,
        )
    }
}