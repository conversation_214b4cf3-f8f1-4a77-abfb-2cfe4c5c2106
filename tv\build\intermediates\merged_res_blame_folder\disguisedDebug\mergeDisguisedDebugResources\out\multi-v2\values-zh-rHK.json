{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-76:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\aff0a4e7236056764cabe678e059991a\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,3476,3539,3600,3667,3736,4474,4564,4671,5151,5202,8451,8529,8739,15175,15253,15314,15371,15427,15486,15544,15598,15684,15740,15798,15852,15917,16095,16169,16241,16321,16395,16473,16593,16656,16719,16818,16895,16969,17019,17070,17136,17200,17268,17339,17411,17472,17543,17610,17670,17758,17838,17901,17984,18069,18143,18208,18284,18332,18406,18470,18546,18624,18686,18750,18813,18879,18959,19039,19115,19196,19250,19457,19753,19828,19968", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,259,260,262", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "839,3534,3595,3662,3731,3808,4559,4666,4739,5197,5259,8524,8583,8792,15248,15309,15366,15422,15481,15539,15593,15679,15735,15793,15847,15912,16005,16164,16236,16316,16390,16468,16588,16651,16714,16813,16890,16964,17014,17065,17131,17195,17263,17334,17406,17467,17538,17605,17665,17753,17833,17896,17979,18064,18138,18203,18279,18327,18401,18465,18541,18619,18681,18745,18808,18874,18954,19034,19110,19191,19245,19300,19521,19823,19896,20033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\2ddc148ab2850d14a8acfd77bd2da82c\\transformed\\media3-ui-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2934,2987,3054,3121,3170", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2929,2982,3049,3116,3165,3226"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,5264,5334,5403,5473,5549,5624,5679,5740,5814,5888,5950,6011,6070,6135,6224,6310,6399,6462,6529,6594,6649,6723,6796,6857,7478,7530,7588,7635,7696,7752,7814,7871,7931,7987,8042,8105,8154,8207,8274,8341,8390", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "371,534,692,5329,5398,5468,5544,5619,5674,5735,5809,5883,5945,6006,6065,6130,6219,6305,6394,6457,6524,6589,6644,6718,6791,6852,6915,7525,7583,7630,7691,7747,7809,7866,7926,7982,8037,8100,8149,8202,8269,8336,8385,8446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\675067a6b0ef7dc58058a5633fb77705\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,253,327,415,506,584,658,735,813,887,960,1035,1102,1183,1256,1326,1395,1470,1535", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "248,322,410,501,579,653,730,808,882,955,1030,1097,1178,1251,1321,1390,1465,1530,1646"}, "to": {"startLines": "66,67,68,69,70,125,126,253,254,256,257,261,263,264,265,266,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4744,4820,4894,4982,5073,8588,8662,19305,19383,19526,19599,19901,20038,20119,20192,20262,20432,20507,20572", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "4815,4889,4977,5068,5146,8657,8734,19378,19452,19594,19669,19963,20114,20187,20257,20326,20502,20567,20683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\151a69a7f886af7aa3234b82508a55dc\\transformed\\media3-exoplayer-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6920,6976,7032,7090,7143,7215,7269,7343,7419", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "6971,7027,7085,7138,7210,7264,7338,7414,7473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\1cb8d8cb32f9b6b54ba7b00e42464438\\transformed\\material3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4042,4136,4228,4311,4393,4478,4567,4657,4737,4829,4911,5008,5102,5195,5288,5372,5468,5564,5659,5767,5847,5953,6054,6146,6236,6335", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,93,91,82,81,84,88,89,79,91,81,96,93,92,92,83,95,95,94,107,79,105,100,91,89,98,97", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4037,4131,4223,4306,4388,4473,4562,4652,4732,4824,4906,5003,5097,5190,5283,5367,5463,5559,5654,5762,5842,5948,6049,6141,6231,6330,6428"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8797,8900,9002,9106,9207,9298,9387,9492,9597,9702,9818,9900,9996,10080,10168,10273,10386,10487,10595,10701,10809,10925,11030,11132,11237,11343,11428,11523,11628,11737,11827,11929,12027,12136,12250,12350,12441,12514,12604,12693,12784,12878,12970,13053,13135,13220,13309,13399,13479,13571,13653,13750,13844,13937,14030,14114,14210,14306,14401,14509,14589,14695,14796,14888,14978,15077", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,93,91,82,81,84,88,89,79,91,81,96,93,92,92,83,95,95,94,107,79,105,100,91,89,98,97", "endOffsets": "8895,8997,9101,9202,9293,9382,9487,9592,9697,9813,9895,9991,10075,10163,10268,10381,10482,10590,10696,10804,10920,11025,11127,11232,11338,11423,11518,11623,11732,11822,11924,12022,12131,12245,12345,12436,12509,12599,12688,12779,12873,12965,13048,13130,13215,13304,13394,13474,13566,13648,13745,13839,13932,14025,14109,14205,14301,14396,14504,14584,14690,14791,14883,14973,15072,15170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0615017894abf0bd7b33401f847396d0\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "50,271,272", "startColumns": "4,4,4", "startOffsets": "3405,20688,20769", "endColumns": "70,80,76", "endOffsets": "3471,20764,20841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\efbd2119718ae0bfeda059930e1194a7\\transformed\\appcompat-1.7.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "844,939,1032,1132,1214,1311,1419,1496,1571,1663,1757,1848,1944,2039,2133,2229,2321,2413,2505,2583,2679,2774,2869,2966,3062,3160,3311,19674", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "934,1027,1127,1209,1306,1414,1491,1566,1658,1752,1843,1939,2034,2128,2224,2316,2408,2500,2578,2674,2769,2864,2961,3057,3155,3306,3400,19748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\20edd988a343bb05e5a649cb0d8f4996\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "56,57,58,59,60,61,62,267", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3813,3905,4004,4098,4192,4285,4378,20331", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3900,3999,4093,4187,4280,4373,4469,20427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\af9619106ca5a74782ec42e5c552ec6a\\transformed\\material-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "207", "startColumns": "4", "startOffsets": "16010", "endColumns": "84", "endOffsets": "16090"}}]}]}