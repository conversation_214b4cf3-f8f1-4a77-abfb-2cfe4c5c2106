package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.content.Context
import android.graphics.SurfaceTexture
import android.view.Surface
import android.view.SurfaceView
import android.view.TextureView
import android.view.TextureView.SurfaceTextureListener
import androidx.media3.common.text.Cue
import com.google.common.collect.ImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.util.utils.toHeaders
import top.yogiczy.mytv.tv.ui.utils.Configs
import tv.danmaku.ijk.media.player.misc.IjkTrackInfo
import tv.danmaku.ijk.media.player.IMediaPlayer
import tv.danmaku.ijk.media.player.IjkMediaMeta
import tv.danmaku.ijk.media.player.IjkMediaPlayer
import tv.danmaku.ijk.media.player.IjkTimedText
import top.yogiczy.mytv.core.data.utils.Logger
import top.yogiczy.mytv.core.data.utils.Loggable
import kotlin.math.max
import kotlin.text.Regex

class IjkVideoPlayer(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
) : VideoPlayer(coroutineScope),
    IMediaPlayer.OnPreparedListener,
    IMediaPlayer.OnVideoSizeChangedListener,
    IMediaPlayer.OnErrorListener,
    IMediaPlayer.OnTimedTextListener,
    IMediaPlayer.OnSeekCompleteListener {

    private val logger = Logger.create("IjkVideoPlayer")
    private val player by lazy {
        IjkMediaPlayer().apply{
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "dns_cache_clear", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "dns_cache_timeout", 0)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "http-detect-range-support", 0)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "reconnect", 2)
            setOption(
                IjkMediaPlayer.OPT_CATEGORY_FORMAT,
                "timeout",
                Configs.videoPlayerLoadTimeout
            )
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzemaxduration", 500L)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzeduration", 100L)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "probesize", 1024 * 10)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "seek2any", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "fflags", "fastseek")
        }
    }
    private var cacheSurfaceView: SurfaceView? = null
    private var cacheSurfaceTexture: Surface? = null
    private var updateJob: Job? = null

    private fun setOption(userAgent: String = "okhttp") {
        player.apply {
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "allowed_extensions", "ALL")
            if (Configs.videoPlayerForceSoftDecode)
                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 0)
            else{
                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 1)
                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-all-videos", 1)
                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-hevc", 1)
                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-handle-resolution-change", 1)
            }
            // setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "protocol_whitelist", "crypto,file,dash,http,https,rtp,tcp,tls,udp,rtmp,rtsp,data")
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "opensles", 0)
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 5)
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "fast", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1)
            
            // rtsp设置 https://ffmpeg.org/ffmpeg-protocols.html#rtsp
            val transport = if (Configs.videoPlayerRtspTransport == Configs.RtspTransport.TCP) "tcp" else "udp"
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", transport)
            // setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "buffer_size", 1316 * 512)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "max-buffer-size", getAvailableMemory(context).toLong())
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "infbuf", 1)  // 无限读
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "flush_packets", 1L)
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", 
                    max(25L, (Configs.videoPlayerBufferTime.toLong() * 0.03).toLong())
            )
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "user_agent", userAgent)
            //  关闭播放器缓冲，这个必须关闭，否则会出现播放一段时间后，一直卡住，控制台打印 FFP_MSG_BUFFERING_START
            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 0)

            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "subtitle", 1)
            
            //https://www.cnblogs.com/Fitz/p/18537127
            // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter",0) //丢弃一些“无用”的数据包，例如AVI格式中的零大小数据包
            // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_frame", 0) //不跳帧，解码所有帧
        }
    }

    override fun prepare(line: ChannelLine) {
        super.prepare(line)
        if (Configs.videoPlayerStopPreviousMediaItem)
            player.stop()
        coroutineScope.launch {
            val playData = getPlayableData()
            if (playData == null)
            {
                triggerError(PlaybackException("No playable data found for line: ${line.name}", 10087))
                logger.e("No playable data found for line: ${line.name}")
                return@launch
            }
            var uri = playData.url
            var header: Map<String, String> = playData.headers ?: emptyMap()
            var headers = Configs.videoPlayerHeaders.toHeaders() + mapOf(
                "Referer" to (line.httpReferrer ?: ""),
                "Origin" to (line.httpOrigin ?: ""),
                "Cookie" to (line.httpCookie ?: ""),
            ).filterValues { it.isNotEmpty() } + header
            
            val userAgent = headers["User-Agent"] ?: headers["user-agent"] ?: line.httpUserAgent ?: Configs.videoPlayerUserAgent
            headers = headers - "User-Agent" - "user-agent" 
            // 使用应用内日志系统
            logger.i("播放地址: ${uri.toString()}")
            logger.i("请求头: $headers")
            logger.i("User-Agent: $userAgent")
            player.reset()
            player.setDataSource(
                uri,
                headers
            )
            setOption(userAgent)
            player.prepareAsync()
            triggerPrepared()
        }
    }

    override fun play() {
        player.start()
    }

    override fun pause() {
        player.pause()
    }

    override fun seekTo(position: Long) {
        player.seekTo(position)
    }

    override fun setVolume(volume: Float) {
    }

    override fun getVolume(): Float {
        return 1f
    }

    override fun stop() {
        player.stop()
        updateJob?.cancel()
        super.stop()
    }

    override fun selectVideoTrack(track: Metadata.Video?) {
        if (track?.index == null)
            metadata.video?.index?.let { player.deselectTrack(it) }
        else
            player.selectTrack(track.index)
        updateVideoInfo(player)
        if(track != null) {
            metadata = metadata.copy(
                video = track.copy(
                    decoder = player.mediaInfo.mVideoDecoderImpl,
                ),
            )
        } else {
            metadata = metadata.copy(video = null)
        }
        triggerMetadata(metadata)
    }

    override fun selectAudioTrack(track: Metadata.Audio?) {
        if (track?.index == null)
            metadata.audio?.index?.let { player.deselectTrack(it) }
        else
            player.selectTrack(track.index)
        updateAudioInfo(player)
        
        if(track != null) {
            metadata = metadata.copy(
                audio = track.copy(
                    decoder = player.mediaInfo.mAudioDecoderImpl,
                ),
            )
        } else {
            metadata = metadata.copy(audio = null)
        }
        triggerMetadata(metadata)
    }

    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {
        if (track?.index == null)
            metadata.subtitle?.index?.let { player.deselectTrack(it) }
        else
            player.selectTrack(track.index)
        updateSubtitleInfo(player)
        metadata = metadata.copy(
            subtitle = track,
        )
        triggerMetadata(metadata)
    }

    override fun setVideoSurfaceView(surfaceView: SurfaceView) {
        cacheSurfaceView = surfaceView
        cacheSurfaceTexture?.release()
        cacheSurfaceTexture = null
    }

    override fun setSubtitleSurfaceView(surfaceView: SurfaceView) {
    }

    override fun setSubtitleTextureView(textureView: TextureView) {
    }

    override fun setSurfaceFrameRate(frameRate: Float) {
        try{
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                cacheSurfaceView?.getHolder()?.getSurface()?.setFrameRate(frameRate,
                        Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE,
                        Surface.CHANGE_FRAME_RATE_ALWAYS)
            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                cacheSurfaceView?.getHolder()?.getSurface()?.setFrameRate(
                    frameRate, Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE)
            }
        } catch (e: Exception) {
            // 如果设置失败，忽略
        }
    }

    override fun setVideoTextureView(textureView: TextureView) {
        cacheSurfaceView = null
        textureView.surfaceTextureListener = object : SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                cacheSurfaceTexture = Surface(surfaceTexture)
                player.setSurface(cacheSurfaceTexture)
            }

            override fun onSurfaceTextureSizeChanged(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
            }

            override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
                return true
            }

            override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {
            }
        }
    }

    override fun initialize() {
        super.initialize()
        player.setOnPreparedListener(this)
        player.setOnVideoSizeChangedListener(this)
        player.setOnErrorListener(this)
    }

    override fun release() {
        player.setOnPreparedListener(null)
        player.setOnVideoSizeChangedListener(null)
        player.setOnErrorListener(null)
        player.stop()
        player.release()
        cacheSurfaceTexture?.release()
        super.release()
    }

    override fun onPrepared(player: IMediaPlayer) {
        cacheSurfaceView?.let { player.setDisplay(it.holder) }
        cacheSurfaceTexture?.let { player.setSurface(it) }

        metadata = metadata.copy(
                videoTracks = emptyList(),
                audioTracks = emptyList(),
                subtitleTracks = emptyList()
            )
        triggerMetadata(metadata)

        updateVideoInfo(player)
        updateAudioInfo(player)
        updateSubtitleInfo(player)

        triggerReady()
        triggerBuffering(false)
        triggerDuration(player.duration)

        updateJob?.cancel()
        updateJob = coroutineScope.launch {
            while (true) {
                triggerIsPlayingChanged(player.isPlaying)
                triggerCurrentPosition(player.currentPosition)
                delay(500)
            }
        }
    }

    private fun IjkMediaMeta.IjkStreamMeta.toVideoMetadata(): Metadata.Video {
        return Metadata.Video(
            index = mIndex,
            width = mWidth,
            height = mHeight,
            frameRate = run {
                val fpsNum = mFpsNum?.toFloat()
                val fpsDen = mFpsDen?.toFloat() ?: 1f
                if (fpsNum != null && fpsDen != 0f) fpsNum / fpsDen else null
            },
            bitrate = mBitrate?.toInt(),
            mimeType = mCodecName,
            isSelected = false,
            language = mLanguage,
        )
    }

    private fun IjkMediaMeta.IjkStreamMeta.toAudioMetadata(): Metadata.Audio {
        return Metadata.Audio(
            index = mIndex,
            channels = when (mChannelLayout) {
                IjkMediaMeta.AV_CH_LAYOUT_MONO -> 1
                IjkMediaMeta.AV_CH_LAYOUT_STEREO,
                IjkMediaMeta.AV_CH_LAYOUT_2POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> 2

                IjkMediaMeta.AV_CH_LAYOUT_2_1,
                IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> 3

                IjkMediaMeta.AV_CH_LAYOUT_3POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_4POINT0,
                IjkMediaMeta.AV_CH_LAYOUT_2_2,
                IjkMediaMeta.AV_CH_LAYOUT_QUAD -> 4

                IjkMediaMeta.AV_CH_LAYOUT_4POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> 5

                // IjkMediaMeta.AV_CH_LAYOUT_3POINT1POINT2,
                IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL,
                IjkMediaMeta.AV_CH_LAYOUT_5POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> 6

                IjkMediaMeta.AV_CH_LAYOUT_6POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> 7

                IjkMediaMeta.AV_CH_LAYOUT_7POINT1,
                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE,
                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK,
                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1_TOP_BACK,
                // IjkMediaMeta.AV_CH_LAYOUT_CUBE,
                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT2_BACK,
                IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> 8

                // IjkMediaMeta.AV_CH_LAYOUT_9POINT0 -> 9

                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT4_BACK,
                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT2 -> 10

                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT4_BACK,
                // IjkMediaMeta.AV_CH_LAYOUT_10POINT2 -> 12

                // IjkMediaMeta.AV_CH_LAYOUT_HEXADECAGONAL -> 16

                // IjkMediaMeta.AV_CH_LAYOUT_22POINT2 -> 24
                else -> 0
            },
            channelsLabel = when (mChannelLayout) {
                IjkMediaMeta.AV_CH_LAYOUT_MONO -> "单声道"
                IjkMediaMeta.AV_CH_LAYOUT_STEREO -> "立体声"
                IjkMediaMeta.AV_CH_LAYOUT_2POINT1 -> "2.1 声道"
                IjkMediaMeta.AV_CH_LAYOUT_2_1 -> "立体声"
                IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> "环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_3POINT1 -> "3.1 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_3POINT1POINT2 -> "3.1.2 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_4POINT0 -> "4.0 四声道"
                IjkMediaMeta.AV_CH_LAYOUT_4POINT1 -> "4.1 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_2_2 -> "四声道"
                IjkMediaMeta.AV_CH_LAYOUT_QUAD -> "四声道"
                // IjkMediaMeta.AV_CH_LAYOUT_CUBE -> "立方声"
                IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> "5.0 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_5POINT1 -> "5.1 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT2_BACK -> "5.1.2 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> "6.0 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_6POINT1 -> "6.1 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> "7.0 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_7POINT1 -> "7.1 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT2 -> "7.1.2 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT4_BACK -> "后置 7.1.4 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE -> "宽域 7.1 环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK -> "后置 7.1 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1_TOP_BACK -> "上置 7.1 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_HEXADECAGONAL -> "十六角环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL -> "六角环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> "八角环绕声"
                IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> "立体声下混音"
                // IjkMediaMeta.AV_CH_LAYOUT_9POINT0 -> "9.0 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_10POINT2 -> "10.2 环绕声"
                // IjkMediaMeta.AV_CH_LAYOUT_22POINT2 -> "22.2 环绕声"
                else -> null
            },
            sampleRate = mSampleRate,
            bitrate = mBitrate?.toInt(),
            mimeType = mCodecName,
            language = mLanguage,
        )
    }

    private fun IjkMediaMeta.IjkStreamMeta.toSubtitleMetadata(): Metadata.Subtitle {
        return Metadata.Subtitle(
            index = mIndex,
            language = mLanguage ?: "Unknown",
            mimeType = mCodecName,
            bitrate = mBitrate?.toInt(),
            isSelected = false,
        )
    }

    override fun onError(player: IMediaPlayer, what: Int, extra: Int): Boolean {
        triggerError(PlaybackException("IJK_ERROR_WHAT_$what", extra))
        return true
    }

    override fun onTimedText(player: IMediaPlayer, text: IjkTimedText) {
        val bitmap = text.getBitmap() 
        val textContent = text.getText()
        val rect = text.getBounds()
        if(bitmap == null && textContent == null) {
            return
        }
        val cue =  Cue.Builder().apply{
            if(bitmap != null){
                setBitmap(bitmap)
                setBitmapHeight(text.getBitmapHeight())
            }else if(textContent != null){
                setText(textContent)
            }
        }
        .build()
        triggerCues(ImmutableList.of(cue))
    }

    override fun onSeekComplete(player: IMediaPlayer) {
        updateVideoInfo(player)
        updateAudioInfo(player)
        updateSubtitleInfo(player)
        triggerDuration(player.duration)
        triggerCurrentPosition(player.currentPosition)
    }

    private fun updateVideoInfo(player: IMediaPlayer) {

        val tracks = this.player.getTrackInfo()
        val videoSelectedTrack = this.player.getSelectedTrack(
            IjkTrackInfo.MEDIA_TRACK_TYPE_VIDEO
        )
        val videoFormats = tracks
            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_VIDEO }
            .map { 
                val metadata = it.getStreamMeta().toVideoMetadata()
                metadata.copy(
                    isSelected = metadata.index == videoSelectedTrack,
                )
                
            }

        val info = player.mediaInfo
        metadata = metadata.copy(
            videoTracks = videoFormats,
            video = info.mMeta.mVideoStream?.toVideoMetadata()?.copy(
                decoder = info.mVideoDecoderImpl,
            ),
        )
        triggerMetadata(metadata)
    }

    private fun updateAudioInfo(player: IMediaPlayer) {
        val tracks = this.player.getTrackInfo()
        val audioSelectedTrack = this.player.getSelectedTrack(
            IjkTrackInfo.MEDIA_TRACK_TYPE_AUDIO
        )
        val audioFormats = tracks
            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_AUDIO }
            .map { 
                val metadata = it.getStreamMeta().toAudioMetadata()
                metadata.copy(
                    isSelected = metadata.index == audioSelectedTrack,
                )
            }
        val info = player.mediaInfo
        metadata = metadata.copy(
            audioTracks = audioFormats,
            audio = info.mMeta.mAudioStream?.toAudioMetadata()?.copy(
                decoder = info.mAudioDecoderImpl,
            ),
        )
        triggerMetadata(metadata)
    }

    private fun updateSubtitleInfo(player: IMediaPlayer) {
        val tracks = this.player.getTrackInfo()
        val subtitleSelectedTrack = this.player.getSelectedTrack(
            IjkTrackInfo.MEDIA_TRACK_TYPE_TIMEDTEXT
        )
        val subtitleFormats = tracks
            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_TIMEDTEXT }
            .map { 
                val metadata = it.getStreamMeta().toSubtitleMetadata()
                metadata.copy(
                    isSelected = metadata.index == subtitleSelectedTrack,
                )
            }
            .mapNotNull { it }

        metadata = metadata.copy(
            subtitleTracks = subtitleFormats,
            subtitle = subtitleFormats.firstOrNull { it.isSelected == true },
        )
        triggerMetadata(metadata)
    }

    override fun onVideoSizeChanged(
        player: IMediaPlayer,
        width: Int,
        height: Int,
        sarNum: Int,
        sarDen: Int
    ) {
        triggerResolution(width, height)
    }
}