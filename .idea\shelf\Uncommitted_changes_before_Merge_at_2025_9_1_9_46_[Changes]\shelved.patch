Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.main.components\r\n\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.Stable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableIntStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.rememberCoroutineScope\r\nimport androidx.compose.runtime.rememberUpdatedState\r\nimport androidx.compose.runtime.setValue\r\nimport kotlinx.coroutines.CoroutineScope\r\nimport kotlinx.coroutines.Job\r\nimport kotlinx.coroutines.delay\r\nimport kotlinx.coroutines.launch\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelFirstOrNull\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelGroupIdx\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelIdx\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelLastOrNull\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLineList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgramme\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserve\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.utils.ChannelUtil\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Loggable\r\nimport top.yogiczy.mytv.core.util.utils.urlHost\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerState\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.Media3VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.rememberVideoPlayerState\r\nimport java.net.URI\r\nimport java.text.SimpleDateFormat\r\nimport java.util.Locale\r\nimport kotlin.math.max\r\nimport kotlin.math.min\r\n\r\n@Stable\r\nclass MainContentState(\r\n    private val coroutineScope: CoroutineScope,\r\n    private val videoPlayerState: VideoPlayerState,\r\n    private val channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    private val favoriteChannelListProvider: () -> ChannelList = { ChannelList() },\r\n    private val settingsViewModel: SettingsViewModel,\r\n) : Loggable(\"MainContentState\") {\r\n    private var _currentChannel by mutableStateOf(Channel())\r\n    val currentChannel get() = _currentChannel\r\n\r\n    private var _currentChannelLineIdx by mutableIntStateOf(0)\r\n    val currentChannelLineIdx get() = _currentChannelLineIdx\r\n\r\n    val currentChannelLine get() = _currentChannel.lineList[_currentChannelLineIdx]\r\n    private var _currentPlaybackEpgProgramme by mutableStateOf<EpgProgramme?>(null)\r\n    val currentPlaybackEpgProgramme get() = _currentPlaybackEpgProgramme\r\n\r\n    private var _tempChannelScreenHideJob: Job? = null\r\n\r\n    private var _isTempChannelScreenVisible by mutableStateOf(false)\r\n    var isTempChannelScreenVisible\r\n        get() = _isTempChannelScreenVisible\r\n        set(value) {\r\n            _isTempChannelScreenVisible = value\r\n        }\r\n\r\n    private var _isChannelScreenVisible by mutableStateOf(false)\r\n    var isChannelScreenVisible\r\n        get() = _isChannelScreenVisible\r\n        set(value) {\r\n            _isChannelScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoPlayerControllerScreenVisible by mutableStateOf(false)\r\n    var isVideoPlayerControllerScreenVisible\r\n        get() = _isVideoPlayerControllerScreenVisible\r\n        set(value) {\r\n            _isVideoPlayerControllerScreenVisible = value\r\n        }\r\n\r\n    private var _isIptvSourceScreenVisible by mutableStateOf(false)\r\n    var isIptvSourceScreenVisible\r\n        get() = _isIptvSourceScreenVisible\r\n        set(value) {\r\n            _isIptvSourceScreenVisible = value\r\n        }\r\n\r\n    private var _isQuickOpScreenVisible by mutableStateOf(false)\r\n    var isQuickOpScreenVisible\r\n        get() = _isQuickOpScreenVisible\r\n        set(value) {\r\n            _isQuickOpScreenVisible = value\r\n        }\r\n    \r\n    private var _isEpgScreenVisible by mutableStateOf(false)\r\n    var isEpgScreenVisible\r\n        get() = _isEpgScreenVisible\r\n        set(value) {\r\n            _isEpgScreenVisible = value\r\n        }\r\n\r\n    private var _isChannelLineScreenVisible by mutableStateOf(false)\r\n    var isChannelLineScreenVisible\r\n        get() = _isChannelLineScreenVisible\r\n        set(value) {\r\n            _isChannelLineScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoPlayerDisplayModeScreenVisible by mutableStateOf(false)\r\n    var isVideoPlayerDisplayModeScreenVisible\r\n        get() = _isVideoPlayerDisplayModeScreenVisible\r\n        set(value) {\r\n            _isVideoPlayerDisplayModeScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoTracksScreenVisible by mutableStateOf(false)\r\n    var isVideoTracksScreenVisible\r\n        get() = _isVideoTracksScreenVisible\r\n        set(value) {\r\n            _isVideoTracksScreenVisible = value\r\n        }\r\n    \r\n    private var _triggerPlayerReinit by mutableStateOf(false)\r\n    var triggerPlayerReinit\r\n        get() = _triggerPlayerReinit\r\n        set(value) {\r\n            _triggerPlayerReinit = value\r\n        }\r\n\r\n    private fun updatePlayerTrigger(){\r\n        _triggerPlayerReinit = !_triggerPlayerReinit\r\n    } \r\n\r\n    private var _isAudioTracksScreenVisible by mutableStateOf(false)\r\n    var isAudioTracksScreenVisible\r\n        get() = _isAudioTracksScreenVisible\r\n        set(value) {\r\n            _isAudioTracksScreenVisible = value\r\n        }\r\n\r\n    private var _isSubtitleTracksScreenVisible by mutableStateOf(false)\r\n    var isSubtitleTracksScreenVisible\r\n        get() = _isSubtitleTracksScreenVisible\r\n        set(value) {\r\n            _isSubtitleTracksScreenVisible = value\r\n        }\r\n\r\n    init {\r\n        val channelGroupList = channelGroupListProvider()\r\n\r\n        changeCurrentChannel(settingsViewModel.iptvChannelLastPlay.isEmptyOrElse {\r\n            channelGroupList.channelFirstOrNull() ?: Channel.EMPTY\r\n        })\r\n\r\n        videoPlayerState.onReady {\r\n            settingsViewModel.iptvChannelLinePlayableUrlList += currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList += currentChannelLine.url.urlHost()\r\n        }\r\n\r\n        videoPlayerState.onError {\r\n            if (_currentPlaybackEpgProgramme != null) return@onError\r\n\r\n            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()\r\n\r\n            if (_currentChannelLineIdx < _currentChannel.lineList.size - 1) {\r\n                changeCurrentChannel(_currentChannel, _currentChannelLineIdx + 1)\r\n            }\r\n        }\r\n\r\n        videoPlayerState.onInterrupt {\r\n            changeCurrentChannel(\r\n                _currentChannel,\r\n                _currentChannelLineIdx,\r\n                _currentPlaybackEpgProgramme\r\n            )\r\n        }\r\n\r\n        videoPlayerState.onIsBuffering { isBuffering ->\r\n            if (isBuffering) {\r\n                _isTempChannelScreenVisible = true\r\n            } else {\r\n                _tempChannelScreenHideJob?.cancel()\r\n                _tempChannelScreenHideJob = coroutineScope.launch {\r\n                    val name = _currentChannel.name\r\n                    val lineIdx = _currentChannelLineIdx\r\n                    delay(Constants.UI_TEMP_CHANNEL_SCREEN_SHOW_DURATION)\r\n                    if (name == _currentChannel.name && lineIdx == _currentChannelLineIdx) {\r\n                        _isTempChannelScreenVisible = false\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getPrevFavoriteChannel(): Channel? {\r\n        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null\r\n\r\n        val channelGroupList = channelGroupListProvider()\r\n        val favoriteChannelList = favoriteChannelListProvider()\r\n\r\n        if (_currentChannel !in favoriteChannelList) return null\r\n\r\n        val currentIdx = favoriteChannelList.indexOf(_currentChannel)\r\n\r\n        return favoriteChannelList.getOrElse(currentIdx - 1) {\r\n            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.lastOrNull()\r\n            else channelGroupList.channelLastOrNull()\r\n        }\r\n    }\r\n\r\n    private fun getNextFavoriteChannel(): Channel? {\r\n        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null\r\n\r\n        val channelGroupList = channelGroupListProvider()\r\n        val favoriteChannelList = favoriteChannelListProvider()\r\n\r\n        if (_currentChannel !in favoriteChannelList) return null\r\n\r\n        val currentIdx = favoriteChannelList.indexOf(_currentChannel)\r\n\r\n        return favoriteChannelList.getOrElse(currentIdx + 1) {\r\n            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.firstOrNull()\r\n            else channelGroupList.channelFirstOrNull()\r\n        }\r\n    }\r\n\r\n    private fun getPrevChannel(): Channel {\r\n        return getPrevFavoriteChannel() ?: run {\r\n            val channelGroupList = channelGroupListProvider()\r\n            \r\n            if (settingsViewModel.iptvChannelChangeCrossGroup) {\r\n                // 跨分组切换逻辑\r\n                val currentIdx = channelGroupList.channelIdx(_currentChannel)\r\n                val prevIdx = if (currentIdx <= 0) {\r\n                    if (settingsViewModel.iptvChannelChangeListLoop) \r\n                        channelGroupList.channelList.size - 1 \r\n                    else \r\n                        0\r\n                } else {\r\n                    currentIdx - 1\r\n                }\r\n                channelGroupList.channelList.getOrElse(prevIdx) {\r\n                    channelGroupList.channelLastOrNull() ?: Channel()\r\n                }\r\n            } else {\r\n                // 分组内切换逻辑\r\n                val group = channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }\r\n                val currentIdx = group.channelList.indexOf(_currentChannel)\r\n                if (currentIdx <= 0) {\r\n                    // 当前是分组内第一个\r\n                    if (settingsViewModel.iptvChannelChangeListLoop) {\r\n                        // 循环开启时，返回分组内最后一个\r\n                        group.channelList.last()\r\n                    } else {\r\n                        // 循环关闭时，保持当前频道不变\r\n                        _currentChannel\r\n                    }\r\n                } else {\r\n                    // 返回分组内前一个\r\n                    group.channelList[currentIdx - 1]\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getNextChannel(): Channel {\r\n        return getNextFavoriteChannel() ?: run {\r\n            val channelGroupList = channelGroupListProvider()\r\n            \r\n            if (settingsViewModel.iptvChannelChangeCrossGroup) {\r\n                // 跨分组切换逻辑\r\n                val currentIdx = channelGroupList.channelIdx(_currentChannel)\r\n                val nextIdx = if (currentIdx >= channelGroupList.channelList.size - 1) {\r\n                    if (settingsViewModel.iptvChannelChangeListLoop) \r\n                        0 \r\n                    else \r\n                        channelGroupList.channelList.size - 1\r\n                } else {\r\n                    currentIdx + 1\r\n                }\r\n                channelGroupList.channelList.getOrElse(nextIdx) {\r\n                    channelGroupList.channelFirstOrNull() ?: Channel()\r\n                }\r\n            } else {\r\n                // 分组内切换逻辑\r\n                val group = channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }\r\n                val currentIdx = group.channelList.indexOf(_currentChannel)\r\n                if (currentIdx >= group.channelList.size - 1) {\r\n                    // 当前是分组内最后一个\r\n                    if (settingsViewModel.iptvChannelChangeListLoop) {\r\n                        // 循环开启时，返回分组内第一个\r\n                        group.channelList.first()\r\n                    } else {\r\n                        // 循环关闭时，保持当前频道不变\r\n                        _currentChannel\r\n                    }\r\n                } else {\r\n                    // 返回分组内下一个\r\n                    group.channelList[currentIdx + 1]\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getLineIdx(lineList: ChannelLineList, lineIdx: Int? = null): Int {\r\n        val idx = if (lineIdx == null) {\r\n            val idx = lineList.indexOfFirst { line ->\r\n                settingsViewModel.iptvChannelLinePlayableUrlList.contains(line.url)\r\n            }\r\n\r\n            if (idx < 0) {\r\n                lineList.indexOfFirst { line ->\r\n                    settingsViewModel.iptvChannelLinePlayableHostList.contains(line.url.urlHost())\r\n                }\r\n            } else idx\r\n        } else (lineIdx + lineList.size) % lineList.size\r\n\r\n        return max(0, min(idx, lineList.size - 1))\r\n    }\r\n\r\n    fun changeCurrentChannel(\r\n        channel: Channel,\r\n        lineIdx: Int? = null, \r\n        playbackEpgProgramme: EpgProgramme? = null,\r\n    ) {\r\n        settingsViewModel.iptvChannelLastPlay = channel\r\n        val realLineIdx = getLineIdx(channel.lineList, lineIdx)\r\n\r\n        // if (channel == _currentChannel && realLineIdx == _currentChannelLineIdx && playbackEpgProgramme == _currentPlaybackEpgProgramme) return\r\n        \r\n        if (channel == _currentChannel && realLineIdx != _currentChannelLineIdx) {\r\n            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()\r\n        }\r\n\r\n        _isTempChannelScreenVisible = true\r\n\r\n        _currentChannel = channel\r\n        _currentChannelLineIdx = realLineIdx\r\n        _currentPlaybackEpgProgramme = playbackEpgProgramme\r\n        currentChannelLine.playbackUrl = null\r\n        var url = currentChannelLine.url\r\n        if (_currentPlaybackEpgProgramme != null) {\r\n            if(currentChannelLine.playbackType != null){\r\n                var playbackFormat = currentChannelLine.playbackFormat ?: \"\"\r\n                val timeFormat = SimpleDateFormat(\"yyyyMMddHHmmss\", Locale.getDefault())\r\n                val timeFormatUTC = SimpleDateFormat(\"yyyyMMddHHmmss\", Locale.getDefault())\r\n                timeFormatUTC.timeZone = java.util.TimeZone.getTimeZone(\"UTC\")\r\n                val tfY = SimpleDateFormat(\"yyyy\", Locale.getDefault())\r\n                val tfM = SimpleDateFormat(\"MM\", Locale.getDefault())\r\n                val tfD = SimpleDateFormat(\"dd\", Locale.getDefault())\r\n                val tfH = SimpleDateFormat(\"HH\", Locale.getDefault())\r\n                val tfm = SimpleDateFormat(\"mm\", Locale.getDefault())\r\n                val tfS = SimpleDateFormat(\"ss\", Locale.getDefault())\r\n                val nowTime = System.currentTimeMillis()\r\n                playbackFormat.apply{\r\n                        _currentPlaybackEpgProgramme?.let { epgProgramme ->\r\n                            replace(\"{utc}\", timeFormatUTC.format(epgProgramme.startAt))\r\n                            replace(\"\\${start}\", timeFormat.format(epgProgramme.startAt))\r\n                            replace(\"{lutc}\", timeFormatUTC.format(nowTime))\r\n                            replace(\"\\${now}\", tfY.format(nowTime))\r\n                            replace(\"\\${timestamp}\", timeFormat.format(nowTime))\r\n                            replace(\"{utcend}\", timeFormatUTC.format(epgProgramme.endAt))\r\n                            replace(\"\\${end}\", timeFormat.format(epgProgramme.endAt))\r\n                            replace(\"{Y}\", tfY.format(epgProgramme.startAt))\r\n                            replace(\"{m}\", tfM.format(epgProgramme.startAt))\r\n                            replace(\"{d}\", tfD.format(epgProgramme.startAt))\r\n                            replace(\"{H}\", tfH.format(epgProgramme.startAt))\r\n                            replace(\"{M}\", tfm.format(epgProgramme.startAt))\r\n                            replace(\"{S}\", tfS.format(epgProgramme.startAt))\r\n                        }\r\n                }\r\n                playbackFormat = ChannelUtil.replacePlaybackFormat(playbackFormat, _currentPlaybackEpgProgramme!!.startAt, nowTime, _currentPlaybackEpgProgramme!!.endAt)?:\"\"\r\n                url = when (currentChannelLine.playbackType ?: 10) {\r\n                    0 -> playbackFormat\r\n                    1 -> \"$url$playbackFormat\"\r\n                    // 2 -> \r\n                    // 3 -> \r\n                    // 4 -> \r\n                    else -> url\r\n                }\r\n            }else{\r\n                val timeFormat = SimpleDateFormat(\"yyyyMMddHHmmss\", Locale.getDefault())\r\n                val query = listOf(\r\n                    \"playseek=\",\r\n                    timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),\r\n                    \"-\",\r\n                    timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),\r\n                ).joinToString(\"\")\r\n                url = if (URI(url).query.isNullOrBlank()) \"$url?$query\" else \"$url&$query\"\r\n                if (Configs.iptvPLTVToTVOD){\r\n                    url = ChannelUtil.urlToCanPlayback(url)\r\n                }\r\n            }\r\n            currentChannelLine.playbackUrl = url\r\n        }\r\n        \r\n        val line = currentChannelLine.copy(url = url)\r\n\r\n        log.d(\"播放${_currentChannel.name}（${_currentChannelLineIdx + 1}/${_currentChannel.lineList.size}）: $line\")\r\n\r\n        if (line.hybridType == ChannelLine.HybridType.WebView) {\r\n            updatePlayerTrigger()\r\n            log.i(\"检测到WebView类型URL: ${line.url}\")\r\n            log.i(\"正在使用WebView打开而不是视频播放器\")\r\n            videoPlayerState.metadata = VideoPlayer.Metadata()\r\n            videoPlayerState.stop()\r\n        } else {\r\n            currentChannelLine.playbackUrl = null\r\n            log.i(\"检测到普通视频URL: ${line.url}\")\r\n            log.i(\"hybridType: ${line.hybridType}, 使用视频播放器播放\")\r\n            if(line.url.startsWith(\"rtsp://\") && line.url.contains(\"smil\") && (videoPlayerState.instance is Media3VideoPlayer)){\r\n                settingsViewModel.videoPlayerCore = Configs.VideoPlayerCore.IJK // Media3 1.6.0 不支持rtsp有效负载类型33\r\n            }else{\r\n                videoPlayerState.prepare(line)\r\n            }\r\n            \r\n        }\r\n    }\r\n\r\n    fun changeCurrentChannelToPrev() {\r\n        changeCurrentChannel(getPrevChannel())\r\n    }\r\n\r\n    fun changeCurrentChannelToNext() {\r\n        changeCurrentChannel(getNextChannel())\r\n    }\r\n\r\n    fun reverseEpgProgrammeOrNot(channel: Channel, programme: EpgProgramme) {\r\n        val reverse = settingsViewModel.epgChannelReserveList.firstOrNull {\r\n            it.test(channel, programme)\r\n        }\r\n\r\n        if (reverse != null) {\r\n            settingsViewModel.epgChannelReserveList =\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList - reverse)\r\n            Snackbar.show(\"取消预约：${reverse.channel} - ${reverse.programme}\")\r\n        } else {\r\n            val newReserve = EpgProgrammeReserve(\r\n                channel = channel.name,\r\n                programme = programme.title,\r\n                startAt = programme.startAt,\r\n                endAt = programme.endAt,\r\n            )\r\n\r\n            settingsViewModel.epgChannelReserveList =\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList + newReserve)\r\n            Snackbar.show(\"已预约：${channel.name} - ${programme.title}\")\r\n        }\r\n    }\r\n\r\n    fun supportPlayback(\r\n        channel: Channel = _currentChannel,\r\n        lineIdx: Int? = _currentChannelLineIdx,\r\n    ): Boolean {\r\n        val currentLineIdx = getLineIdx(channel.lineList, lineIdx)\r\n        return channel.lineList[currentLineIdx].playbackType != null || \r\n        ChannelUtil.urlSupportPlayback(channel.lineList[currentLineIdx].url)\r\n    }\r\n}\r\n\r\n@Composable\r\nfun rememberMainContentState(\r\n    coroutineScope: CoroutineScope = rememberCoroutineScope(),\r\n    videoPlayerState: VideoPlayerState = rememberVideoPlayerState(),\r\n    channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    favoriteChannelListProvider: () -> ChannelList = { ChannelList() },\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n): MainContentState {\r\n    val favoriteChannelListProviderUpdated by rememberUpdatedState(favoriteChannelListProvider)\r\n\r\n    return remember(settingsVM.videoPlayerCore) {\r\n        MainContentState(\r\n            coroutineScope = coroutineScope,\r\n            videoPlayerState = videoPlayerState,\r\n            channelGroupListProvider = channelGroupListProvider,\r\n            favoriteChannelListProvider = favoriteChannelListProviderUpdated,\r\n            settingsViewModel = settingsViewModel,\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt	(date 1756691200118)
@@ -37,6 +37,7 @@
 import top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel
 import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerState
+import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.Media3VideoPlayer
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.rememberVideoPlayerState
@@ -63,7 +64,18 @@
     val currentChannelLine get() = _currentChannel.lineList[_currentChannelLineIdx]
     private var _currentPlaybackEpgProgramme by mutableStateOf<EpgProgramme?>(null)
     val currentPlaybackEpgProgramme get() = _currentPlaybackEpgProgramme
-
+    
+    // Store the playback seek offset for RELOAD_URL mode without modifying original EPG programme
+    private var _playbackSeekOffset by mutableStateOf(0L)
+    val playbackSeekOffset get() = _playbackSeekOffset
+    
+    // Track if we're currently seeking to prevent UI flickers during reload
+    private var _isSeeking by mutableStateOf(false)
+    var isSeeking
+        get() = _isSeeking
+        set(value) {
+            _isSeeking = value
+        }
     private var _tempChannelScreenHideJob: Job? = null
 
     private var _isTempChannelScreenVisible by mutableStateOf(false)
@@ -164,6 +176,20 @@
         videoPlayerState.onReady {
             settingsViewModel.iptvChannelLinePlayableUrlList += currentChannelLine.url
             settingsViewModel.iptvChannelLinePlayableHostList += currentChannelLine.url.urlHost()
+
+            // Reset seeking state when player is ready
+            _isSeeking = false
+
+            // 如果是seekTo回放模式且有回放节目，执行seekTo跳转
+            if (settingsViewModel.videoPlayerPlaybackMode == VideoPlayerPlaybackMode.SEEK_TO &&
+                _currentPlaybackEpgProgramme != null) {
+                val currentTime = System.currentTimeMillis()
+                val seekPosition = currentTime - _currentPlaybackEpgProgramme!!.startAt
+                if (seekPosition > 0) {
+                    videoPlayerState.seekTo(seekPosition)
+                    log.d("seekTo回放模式: 跳转到位置 ${seekPosition}ms")
+                }
+            }
         }
 
         videoPlayerState.onError {
@@ -328,6 +354,10 @@
         return max(0, min(idx, lineList.size - 1))
     }
 
+    fun setPlaybackSeekOffset(offset: Long) {
+        _playbackSeekOffset = offset
+    }
+    
     fun changeCurrentChannel(
         channel: Channel,
         lineIdx: Int? = null, 
@@ -345,64 +375,85 @@
 
         _isTempChannelScreenVisible = true
 
+        // Reset playback seek offset when changing channel or programme
+        if (channel != _currentChannel || realLineIdx != _currentChannelLineIdx || playbackEpgProgramme != _currentPlaybackEpgProgramme) {
+            _playbackSeekOffset = 0L
+        }
+        
         _currentChannel = channel
         _currentChannelLineIdx = realLineIdx
         _currentPlaybackEpgProgramme = playbackEpgProgramme
         currentChannelLine.playbackUrl = null
         var url = currentChannelLine.url
+
+        // 根据回放方式设置处理回放
         if (_currentPlaybackEpgProgramme != null) {
-            if(currentChannelLine.playbackType != null){
-                var playbackFormat = currentChannelLine.playbackFormat ?: ""
-                val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
-                val timeFormatUTC = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
-                timeFormatUTC.timeZone = java.util.TimeZone.getTimeZone("UTC")
-                val tfY = SimpleDateFormat("yyyy", Locale.getDefault())
-                val tfM = SimpleDateFormat("MM", Locale.getDefault())
-                val tfD = SimpleDateFormat("dd", Locale.getDefault())
-                val tfH = SimpleDateFormat("HH", Locale.getDefault())
-                val tfm = SimpleDateFormat("mm", Locale.getDefault())
-                val tfS = SimpleDateFormat("ss", Locale.getDefault())
-                val nowTime = System.currentTimeMillis()
-                playbackFormat.apply{
-                        _currentPlaybackEpgProgramme?.let { epgProgramme ->
-                            replace("{utc}", timeFormatUTC.format(epgProgramme.startAt))
-                            replace("\${start}", timeFormat.format(epgProgramme.startAt))
-                            replace("{lutc}", timeFormatUTC.format(nowTime))
-                            replace("\${now}", tfY.format(nowTime))
-                            replace("\${timestamp}", timeFormat.format(nowTime))
-                            replace("{utcend}", timeFormatUTC.format(epgProgramme.endAt))
-                            replace("\${end}", timeFormat.format(epgProgramme.endAt))
-                            replace("{Y}", tfY.format(epgProgramme.startAt))
-                            replace("{m}", tfM.format(epgProgramme.startAt))
-                            replace("{d}", tfD.format(epgProgramme.startAt))
-                            replace("{H}", tfH.format(epgProgramme.startAt))
-                            replace("{M}", tfm.format(epgProgramme.startAt))
-                            replace("{S}", tfS.format(epgProgramme.startAt))
-                        }
-                }
-                playbackFormat = ChannelUtil.replacePlaybackFormat(playbackFormat, _currentPlaybackEpgProgramme!!.startAt, nowTime, _currentPlaybackEpgProgramme!!.endAt)?:""
-                url = when (currentChannelLine.playbackType ?: 10) {
-                    0 -> playbackFormat
-                    1 -> "$url$playbackFormat"
-                    // 2 -> 
-                    // 3 -> 
-                    // 4 -> 
-                    else -> url
-                }
-            }else{
-                val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
-                val query = listOf(
-                    "playseek=",
-                    timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),
-                    "-",
-                    timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),
-                ).joinToString("")
-                url = if (URI(url).query.isNullOrBlank()) "$url?$query" else "$url&$query"
-                if (Configs.iptvPLTVToTVOD){
-                    url = ChannelUtil.urlToCanPlayback(url)
-                }
-            }
-            currentChannelLine.playbackUrl = url
+            when (settingsViewModel.videoPlayerPlaybackMode) {
+                VideoPlayerPlaybackMode.RELOAD_URL -> {
+                    // 重载URL跳转方式（原有逻辑）
+                    if(currentChannelLine.playbackType != null){
+                        var playbackFormat = currentChannelLine.playbackFormat ?: ""
+                        val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
+                        val timeFormatUTC = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
+                        timeFormatUTC.timeZone = java.util.TimeZone.getTimeZone("UTC")
+                        val tfY = SimpleDateFormat("yyyy", Locale.getDefault())
+                        val tfM = SimpleDateFormat("MM", Locale.getDefault())
+                        val tfD = SimpleDateFormat("dd", Locale.getDefault())
+                        val tfH = SimpleDateFormat("HH", Locale.getDefault())
+                        val tfm = SimpleDateFormat("mm", Locale.getDefault())
+                        val tfS = SimpleDateFormat("ss", Locale.getDefault())
+                        val nowTime = System.currentTimeMillis()
+                        playbackFormat.apply{
+                                _currentPlaybackEpgProgramme?.let { epgProgramme ->
+                                    // Use adjusted start time (original start + seek offset) for URL generation
+                                    val adjustedStartTime = epgProgramme.startAt + _playbackSeekOffset
+                                    replace("{utc}", timeFormatUTC.format(adjustedStartTime))
+                                    replace("\${start}", timeFormat.format(adjustedStartTime))
+                                    replace("{lutc}", timeFormatUTC.format(nowTime))
+                                    replace("\${now}", tfY.format(nowTime))
+                                    replace("\${timestamp}", timeFormat.format(nowTime))
+                                    replace("{utcend}", timeFormatUTC.format(epgProgramme.endAt))
+                                    replace("\${end}", timeFormat.format(epgProgramme.endAt))
+                                    replace("{Y}", tfY.format(adjustedStartTime))
+                                    replace("{m}", tfM.format(adjustedStartTime))
+                                    replace("{d}", tfD.format(adjustedStartTime))
+                                    replace("{H}", tfH.format(adjustedStartTime))
+                                    replace("{M}", tfm.format(adjustedStartTime))
+                                    replace("{S}", tfS.format(adjustedStartTime))
+                                }
+                        }
+                        playbackFormat = ChannelUtil.replacePlaybackFormat(playbackFormat, _currentPlaybackEpgProgramme!!.startAt + _playbackSeekOffset, nowTime, _currentPlaybackEpgProgramme!!.endAt)?:""
+                        url = when (currentChannelLine.playbackType ?: 10) {
+                            0 -> playbackFormat
+                            1 -> "$url$playbackFormat"
+                            // 2 ->
+                            // 3 ->
+                            // 4 ->
+                            else -> url
+                        }
+                    }else{
+                        val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
+                        // Use adjusted start time for URL generation
+                        val adjustedStartTime = _currentPlaybackEpgProgramme!!.startAt + _playbackSeekOffset
+                        val query = listOf(
+                            "playseek=",
+                            timeFormat.format(adjustedStartTime),
+                            "-",
+                            timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),
+                        ).joinToString("")
+                        url = if (URI(url).query.isNullOrBlank()) "$url?$query" else "$url&$query"
+                        if (Configs.iptvPLTVToTVOD){
+                            url = ChannelUtil.urlToCanPlayback(url)
+                        }
+                    }
+                    currentChannelLine.playbackUrl = url
+                }
+                VideoPlayerPlaybackMode.SEEK_TO -> {
+                    // 播放器seekTo跳转方式
+                    // 不修改URL，保持原始直播流地址
+                    // 回放逻辑将在播放器准备完成后通过seekTo实现
+                }
+            }
         }
         
         val line = currentChannelLine.copy(url = url)
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContent.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.main.components\r\n\r\nimport androidx.compose.foundation.layout.Box\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.LaunchedEffect\r\nimport androidx.compose.runtime.key\r\nimport androidx.compose.runtime.rememberCoroutineScope\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.platform.LocalContext\r\nimport kotlinx.coroutines.launch\r\nimport android.widget.Toast\r\nimport android.content.Context\r\nimport android.content.Intent\r\nimport androidx.compose.runtime.remember\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Logger\r\nimport top.yogiczy.mytv.core.data.entities.actions.KeyDownAction\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.entities.epg.Epg\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgList.Companion.match\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgList.Companion.recentProgramme\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.repositories.epg.EpgRepository\r\nimport top.yogiczy.mytv.core.data.repositories.iptv.IptvRepository\r\nimport top.yogiczy.mytv.tv.ui.material.PopupContent\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.material.Visibility\r\nimport top.yogiczy.mytv.tv.ui.material.popupable\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsSubCategories\r\nimport top.yogiczy.mytv.tv.ui.screen.main.MainViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.main.mainVM\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.screensold.audiotracks.AudioTracksScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.channel.ChannelNumberSelectScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.channel.ChannelScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.channel.ChannelTempScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.channel.rememberChannelNumberSelectState\r\nimport top.yogiczy.mytv.tv.ui.screensold.channelline.ChannelLineScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.classicchannel.ClassicChannelScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.datetime.DatetimeScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.epg.EpgProgrammeProgressScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.epg.EpgScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.epgreverse.EpgReverseScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.quickop.QuickOpScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.subtitletracks.SubtitleTracksScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.iptvsource.IptvSourceScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.rememberVideoPlayerState\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayerdiaplaymode.VideoPlayerDisplayModeScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.videotracks.VideoTracksScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.webview.WebViewScreen\r\nimport top.yogiczy.mytv.tv.ui.screensold.webview.WebViewScreen_X5\r\nimport top.yogiczy.mytv.tv.ui.utils.backHandler\r\nimport top.yogiczy.mytv.tv.ui.utils.handleDragGestures\r\nimport top.yogiczy.mytv.tv.ui.utils.handleKeyEvents\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport top.yogiczy.mytv.tv.X5CorePreLoadService\r\nimport top.yogiczy.mytv.tv.R\r\n\r\n@Composable\r\nfun MainContent(\r\n    modifier: Modifier = Modifier,\r\n    isLoadingProvider: () -> Boolean = { false },\r\n    filteredChannelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    favoriteChannelListProvider: () -> ChannelList = { ChannelList() },\r\n    epgListProvider: () -> EpgList = { EpgList() },\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n    onChannelFavoriteToggle: (Channel) -> Unit = {},\r\n    toSettingsScreen: (SettingsSubCategories?) -> Unit = {},\r\n    toDashboardScreen: () -> Unit = {},\r\n    onReload: () -> Unit = {},\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    val isLoading = isLoadingProvider()\r\n    val coroutineScope = rememberCoroutineScope()\r\n    val log = remember { Logger.create(\"MainContent\")}\r\n    val videoPlayerState =\r\n        rememberVideoPlayerState(defaultDisplayModeProvider = { settingsViewModel.videoPlayerDisplayMode })\r\n    val mainContentState = rememberMainContentState(\r\n        videoPlayerState = videoPlayerState,\r\n        channelGroupListProvider = filteredChannelGroupListProvider,\r\n        favoriteChannelListProvider = favoriteChannelListProvider,\r\n    )\r\n    val channelNumberSelectState = rememberChannelNumberSelectState {\r\n        val idx = it.toInt() - 1\r\n        filteredChannelGroupListProvider().channelList.getOrNull(idx)?.let { channel ->\r\n            mainContentState.changeCurrentChannel(channel)\r\n        }\r\n    }\r\n\r\n    Box(\r\n        modifier = modifier\r\n            .popupable()\r\n            .backHandler { onBackPressed() }\r\n            .handleKeyEvents(\r\n                onUp = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventUp, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onDown = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventDown, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onLeft = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLeft, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onRight = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventRight, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onLongUp = { \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLongUp, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onSelect = { \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventSelect, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onLongSelect = {  \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLongSelect, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onSettings = { mainContentState.isQuickOpScreenVisible = true },\r\n                onLongLeft = {  \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLongLeft, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onLongRight = {  \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLongRight, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onLongDown = {  \r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLongDown, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onNumber = { channelNumberSelectState.input(it) },\r\n            )\r\n            .handleDragGestures(\r\n                onSwipeDown = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventDown, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onSwipeUp = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventUp, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onSwipeRight = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventRight, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n                onSwipeLeft = {\r\n                    getKeyDownEvent(settingsViewModel.keyDownEventLeft, settingsViewModel, mainContentState, isLoading)\r\n                },\r\n            ),\r\n    ) {\r\n        Visibility({ mainContentState.currentChannelLine?.hybridType != ChannelLine.HybridType.WebView }) {\r\n            VideoPlayerScreen(\r\n                state = videoPlayerState,\r\n                showMetadataProvider = { settingsViewModel.debugShowVideoPlayerMetadata },\r\n                forceTextureView = false,\r\n            )\r\n        }\r\n        key(mainContentState.triggerPlayerReinit) {\r\n            Visibility({ mainContentState.currentChannelLine?.hybridType == ChannelLine.HybridType.WebView }) {\r\n                mainContentState.currentChannelLine.let {\r\n                    log.i(\"当前频道$it, 播放链接: ${it.playableUrl}\")\r\n                    val isX5Available = com.tencent.smtt.sdk.QbSdk.canLoadX5(LocalContext.current)\r\n                    if (settingsViewModel.webViewCore == Configs.WebViewCore.X5 && !isX5Available){\r\n                        settingsViewModel.webViewCore = Configs.WebViewCore.SYSTEM\r\n                        Toast.makeText(\r\n                            LocalContext.current,\r\n                            LocalContext.current.getString(R.string.ui_x5_core_preload_not_supported),\r\n                            Toast.LENGTH_LONG\r\n                        ).show()\r\n                        preInitX5Core(LocalContext.current)\r\n                    }\r\n                    when (settingsViewModel.webViewCore) {\r\n                        Configs.WebViewCore.SYSTEM -> {\r\n                            WebViewScreen(\r\n                                urlProvider = {\r\n                                    Pair(\r\n                                        it.playbackUrl ?: it.url,\r\n                                        it.httpUserAgent ?: \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0\"\r\n                                    )\r\n                                },\r\n                                onVideoResolutionChanged = { width, height ->\r\n                                    videoPlayerState.metadata = videoPlayerState.metadata.copy(\r\n                                        video = (videoPlayerState.metadata.video\r\n                                            ?: VideoPlayer.Metadata.Video()).copy(\r\n                                            width = width,\r\n                                            height = height,\r\n                                        ),\r\n                                    )\r\n                                    mainContentState.isTempChannelScreenVisible = false\r\n                                },\r\n                            )\r\n                        }\r\n                        Configs.WebViewCore.X5 -> {\r\n                            WebViewScreen_X5(\r\n                                urlProvider = {\r\n                                    Pair(\r\n                                        it.playbackUrl ?: it.url,\r\n                                        it.httpUserAgent ?: \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0\"\r\n                                    )\r\n                                },\r\n                                onVideoResolutionChanged = { width, height ->\r\n                                    videoPlayerState.metadata = videoPlayerState.metadata.copy(\r\n                                        video = (videoPlayerState.metadata.video\r\n                                            ?: VideoPlayer.Metadata.Video()).copy(\r\n                                            width = width,\r\n                                            height = height,\r\n                                        ),\r\n                                    )\r\n                                    mainContentState.isTempChannelScreenVisible = false\r\n                                },\r\n                                onSelect = { mainContentState.isChannelScreenVisible = true },\r\n                                onLongSelect = { mainContentState.isQuickOpScreenVisible = true },\r\n                            )\r\n                        } \r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    Visibility({ settingsViewModel.uiShowEpgProgrammePermanentProgress }) {\r\n        EpgProgrammeProgressScreen(\r\n            currentEpgProgrammeProvider = {\r\n                mainContentState.currentPlaybackEpgProgramme ?: epgListProvider().recentProgramme(\r\n                    mainContentState.currentChannel\r\n                )?.now\r\n            },\r\n            videoPlayerCurrentPositionProvider = { videoPlayerState.currentPosition },\r\n        )\r\n    }\r\n\r\n    Visibility({\r\n        !mainContentState.isTempChannelScreenVisible\r\n                && !mainContentState.isChannelScreenVisible\r\n                && !mainContentState.isIptvSourceScreenVisible\r\n                && !mainContentState.isQuickOpScreenVisible\r\n                && !mainContentState.isEpgScreenVisible\r\n                && !mainContentState.isChannelLineScreenVisible\r\n                && channelNumberSelectState.channelNumber.isEmpty()\r\n    }) {\r\n        DatetimeScreen(showModeProvider = { settingsViewModel.uiTimeShowMode })\r\n    }\r\n\r\n    ChannelNumberSelectScreen(channelNumberProvider = { channelNumberSelectState.channelNumber })\r\n\r\n    Visibility({\r\n        mainContentState.isTempChannelScreenVisible\r\n                && !mainContentState.isChannelScreenVisible\r\n                && !mainContentState.isIptvSourceScreenVisible\r\n                && !mainContentState.isQuickOpScreenVisible\r\n                && !mainContentState.isEpgScreenVisible\r\n                && !mainContentState.isChannelLineScreenVisible\r\n                && !mainContentState.isVideoPlayerControllerScreenVisible\r\n                && channelNumberSelectState.channelNumber.isEmpty()\r\n    }) {\r\n        ChannelTempScreen(\r\n            channelProvider = { mainContentState.currentChannel },\r\n            channelLineIdxProvider = { mainContentState.currentChannelLineIdx },\r\n            recentEpgProgrammeProvider = {\r\n                epgListProvider().recentProgramme(mainContentState.currentChannel)\r\n            },\r\n            currentPlaybackEpgProgrammeProvider = { mainContentState.currentPlaybackEpgProgramme },\r\n            playerMetadataProvider = { videoPlayerState.metadata },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isEpgScreenVisible },\r\n        onDismissRequest = { mainContentState.isEpgScreenVisible = false },\r\n    ) {\r\n        EpgScreen(\r\n            epgProvider = {\r\n                epgListProvider().match(mainContentState.currentChannel, settingsViewModel.epgSourceFollowIptv) ?: Epg.empty(\r\n                    mainContentState.currentChannel\r\n                )\r\n            },\r\n            epgProgrammeReserveListProvider = {\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList.filter {\r\n                    it.channel == mainContentState.currentChannel.name\r\n                })\r\n            },\r\n            supportPlaybackProvider = { mainContentState.supportPlayback() },\r\n            currentPlaybackEpgProgrammeProvider = { mainContentState.currentPlaybackEpgProgramme },\r\n            onEpgProgrammePlayback = {\r\n                mainContentState.isEpgScreenVisible = false\r\n                mainContentState.changeCurrentChannel(\r\n                    mainContentState.currentChannel,\r\n                    mainContentState.currentChannelLineIdx,\r\n                    it,\r\n                )\r\n            },\r\n            onEpgProgrammeReserve = { programme ->\r\n                mainContentState.reverseEpgProgrammeOrNot(\r\n                    mainContentState.currentChannel, programme\r\n                )\r\n            },\r\n            onEpgProgrammeLive = {\r\n                mainContentState.isEpgScreenVisible = false\r\n                mainContentState.changeCurrentChannel(\r\n                    mainContentState.currentChannel,\r\n                    mainContentState.currentChannelLineIdx,\r\n                    null, // null 表示回到直播\r\n                )\r\n            },\r\n            onClose = { mainContentState.isEpgScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isIptvSourceScreenVisible },\r\n        onDismissRequest = { mainContentState.isIptvSourceScreenVisible = false },\r\n    ) {\r\n        IptvSourceScreen(\r\n            currentIptvSourceProvider = { settingsViewModel.iptvSourceCurrent },\r\n            iptvSourceListProvider = {settingsViewModel.iptvSourceList},\r\n            onIptvSourceChanged = {\r\n                mainContentState.isIptvSourceScreenVisible = false\r\n                settingsViewModel.iptvSourceCurrent = it\r\n                settingsViewModel.iptvChannelGroupHiddenList = emptySet()\r\n                settingsViewModel.iptvChannelLastPlay = Channel.EMPTY\r\n                onReload()\r\n            },\r\n            refresh = {\r\n                settingsViewModel.iptvSourceList = Configs.iptvSourceList\r\n            },\r\n            onClose = { mainContentState.isIptvSourceScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isChannelLineScreenVisible },\r\n        onDismissRequest = { mainContentState.isChannelLineScreenVisible = false },\r\n    ) {\r\n        ChannelLineScreen(\r\n            channelProvider = { mainContentState.currentChannel },\r\n            currentLineProvider = { mainContentState.currentChannelLine },\r\n            onLineSelected = {\r\n                mainContentState.isChannelLineScreenVisible = false\r\n                mainContentState.changeCurrentChannel(\r\n                    mainContentState.currentChannel,\r\n                    mainContentState.currentChannel.lineList.indexOf(it),\r\n                )\r\n            },\r\n            onClose = { mainContentState.isChannelLineScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isVideoPlayerControllerScreenVisible },\r\n        onDismissRequest = { mainContentState.isVideoPlayerControllerScreenVisible = false },\r\n    ) {\r\n        val threshold = 1000L * 60 * 60 * 24 * 365\r\n        val hour0 = -28800000L\r\n\r\n        VideoPlayerControllerScreen(\r\n            isVideoPlayerPlayingProvider = { videoPlayerState.isPlaying },\r\n            isVideoPlayerBufferingProvider = { videoPlayerState.isBuffering },\r\n            videoPlayerCurrentPositionProvider = {\r\n                if (videoPlayerState.currentPosition >= threshold) videoPlayerState.currentPosition\r\n                else hour0 + videoPlayerState.currentPosition\r\n            },\r\n            videoPlayerDurationProvider = {\r\n                if (videoPlayerState.currentPosition >= threshold) {\r\n                    val playback = mainContentState.currentPlaybackEpgProgramme\r\n\r\n                    if (playback != null) {\r\n                        playback.startAt to playback.endAt\r\n                    } else {\r\n                        val programme =\r\n                            epgListProvider().recentProgramme(mainContentState.currentChannel)?.now\r\n                        (programme?.startAt ?: hour0) to (programme?.endAt ?: hour0)\r\n                    }\r\n                } else {\r\n                    hour0 to (hour0 + videoPlayerState.duration)\r\n                }\r\n            },\r\n            onVideoPlayerPlay = { videoPlayerState.play() },\r\n            onVideoPlayerPause = { videoPlayerState.pause() },\r\n            onVideoPlayerSeekTo = { videoPlayerState.seekTo(it) },\r\n            onClose = { mainContentState.isVideoPlayerControllerScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isVideoPlayerDisplayModeScreenVisible },\r\n        onDismissRequest = { mainContentState.isVideoPlayerDisplayModeScreenVisible = false },\r\n    ) {\r\n        VideoPlayerDisplayModeScreen(\r\n            currentDisplayModeProvider = { videoPlayerState.displayMode },\r\n            onDisplayModeChanged = { videoPlayerState.displayMode = it },\r\n            onApplyToGlobal = {\r\n                mainContentState.isVideoPlayerDisplayModeScreenVisible = false\r\n                settingsViewModel.videoPlayerDisplayMode = videoPlayerState.displayMode\r\n                Snackbar.show(\"已应用到全局\")\r\n            },\r\n            onClose = { mainContentState.isVideoPlayerDisplayModeScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isVideoTracksScreenVisible },\r\n        onDismissRequest = { mainContentState.isVideoTracksScreenVisible = false },\r\n    ) {\r\n        VideoTracksScreen(\r\n            trackListProvider = { videoPlayerState.metadata.videoTracks },\r\n            onTrackChanged = {\r\n                videoPlayerState.selectVideoTrack(it)\r\n                mainContentState.isVideoTracksScreenVisible = false\r\n            },\r\n            onClose = { mainContentState.isVideoTracksScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isAudioTracksScreenVisible },\r\n        onDismissRequest = { mainContentState.isAudioTracksScreenVisible = false },\r\n    ) {\r\n        AudioTracksScreen(\r\n            trackListProvider = { videoPlayerState.metadata.audioTracks },\r\n            onTrackChanged = {\r\n                videoPlayerState.selectAudioTrack(it)\r\n                mainContentState.isAudioTracksScreenVisible = false\r\n            },\r\n            onClose = { mainContentState.isAudioTracksScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isSubtitleTracksScreenVisible },\r\n        onDismissRequest = { mainContentState.isSubtitleTracksScreenVisible = false },\r\n    ) {\r\n        SubtitleTracksScreen(\r\n            trackListProvider = { videoPlayerState.metadata.subtitleTracks },\r\n            onTrackChanged = {\r\n                videoPlayerState.selectSubtitleTrack(it)\r\n                mainContentState.isSubtitleTracksScreenVisible = false\r\n            },\r\n            onClose = { mainContentState.isSubtitleTracksScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isQuickOpScreenVisible },\r\n        onDismissRequest = { mainContentState.isQuickOpScreenVisible = false },\r\n    ) {\r\n        QuickOpScreen(\r\n            currentChannelProvider = { mainContentState.currentChannel },\r\n            currentChannelLineIdxProvider = { mainContentState.currentChannelLineIdx },\r\n            currentChannelNumberProvider = {\r\n                (filteredChannelGroupListProvider().channelList.indexOf(mainContentState.currentChannel) + 1).toString()\r\n            },\r\n            epgListProvider = epgListProvider,\r\n            currentPlaybackEpgProgrammeProvider = { mainContentState.currentPlaybackEpgProgramme },\r\n            videoPlayerMetadataProvider = { videoPlayerState.metadata },\r\n            videoPlayerIndicatorProvider = { mainContentState.currentChannelLine?.hybridType != ChannelLine.HybridType.WebView },\r\n            onShowIptvSource ={\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isIptvSourceScreenVisible = true\r\n            },\r\n            onShowEpg = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isEpgScreenVisible = true\r\n            },\r\n            onShowChannelLine = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isChannelLineScreenVisible = true\r\n            },\r\n            onShowVideoPlayerController = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isVideoPlayerControllerScreenVisible = true\r\n            },\r\n            onShowVideoPlayerDisplayMode = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isVideoPlayerDisplayModeScreenVisible = true\r\n            },\r\n            onShowVideoTracks = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isVideoTracksScreenVisible = true\r\n            },\r\n            onShowAudioTracks = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isAudioTracksScreenVisible = true\r\n            },\r\n            onShowSubtitleTracks = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                mainContentState.isSubtitleTracksScreenVisible = true\r\n            },\r\n            toSettingsScreen = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                toSettingsScreen(it)\r\n            },\r\n            toDashboardScreen = {\r\n                mainContentState.isQuickOpScreenVisible = false\r\n                toDashboardScreen()\r\n            },\r\n            onClearCache = {\r\n                settingsViewModel.iptvChannelLinePlayableHostList = emptySet()\r\n                settingsViewModel.iptvChannelLinePlayableUrlList = emptySet()\r\n                coroutineScope.launch {\r\n                    IptvRepository(settingsViewModel.iptvSourceCurrent).clearCache()\r\n                    EpgRepository(settingsViewModel.epgSourceCurrent).clearCache()\r\n                    Snackbar.show(\"缓存已清除，请重启应用\")\r\n                }\r\n            },\r\n            onClose = { mainContentState.isQuickOpScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isChannelScreenVisible && !settingsViewModel.uiUseClassicPanelScreen },\r\n        onDismissRequest = { mainContentState.isChannelScreenVisible = false },\r\n    ) {\r\n        ChannelScreen(\r\n            channelGroupListProvider = filteredChannelGroupListProvider,\r\n            favoriteChannelListProvider = favoriteChannelListProvider,\r\n            currentChannelProvider = { mainContentState.currentChannel },\r\n            currentChannelLineIdxProvider = { mainContentState.currentChannelLineIdx },\r\n            showChannelLogoProvider = { settingsViewModel.uiShowChannelLogo },\r\n            onChannelSelected = {\r\n                mainContentState.isChannelScreenVisible = false\r\n                mainContentState.changeCurrentChannel(it)\r\n            },\r\n            onChannelFavoriteToggle = onChannelFavoriteToggle,\r\n            epgListProvider = epgListProvider,\r\n            showEpgProgrammeProgressProvider = { settingsViewModel.uiShowEpgProgrammeProgress },\r\n            currentPlaybackEpgProgrammeProvider = { mainContentState.currentPlaybackEpgProgramme },\r\n            videoPlayerMetadataProvider = { videoPlayerState.metadata },\r\n            channelFavoriteEnabledProvider = { settingsViewModel.iptvChannelFavoriteEnable },\r\n            channelFavoriteListVisibleProvider = { settingsViewModel.iptvChannelFavoriteListVisible },\r\n            onChannelFavoriteListVisibleChange = {\r\n                settingsViewModel.iptvChannelFavoriteListVisible = it\r\n            },\r\n            onClose = { mainContentState.isChannelScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    PopupContent(\r\n        visibleProvider = { mainContentState.isChannelScreenVisible && settingsViewModel.uiUseClassicPanelScreen },\r\n        onDismissRequest = { mainContentState.isChannelScreenVisible = false },\r\n    ) {\r\n        ClassicChannelScreen(\r\n            channelGroupListProvider = filteredChannelGroupListProvider,\r\n            favoriteChannelListProvider = favoriteChannelListProvider,\r\n            currentChannelProvider = { mainContentState.currentChannel },\r\n            currentChannelLineIdxProvider = { mainContentState.currentChannelLineIdx },\r\n            showChannelLogoProvider = { settingsViewModel.uiShowChannelLogo },\r\n            onChannelSelected = {\r\n                mainContentState.isChannelScreenVisible = false\r\n                mainContentState.changeCurrentChannel(it)\r\n            },\r\n            onChannelFavoriteToggle = onChannelFavoriteToggle,\r\n            epgListProvider = epgListProvider,\r\n            epgProgrammeReserveListProvider = {\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList)\r\n            },\r\n            showEpgProgrammeProgressProvider = { settingsViewModel.uiShowEpgProgrammeProgress },\r\n            supportPlaybackProvider = { mainContentState.supportPlayback(it, null) },\r\n            currentPlaybackEpgProgrammeProvider = { mainContentState.currentPlaybackEpgProgramme },\r\n            onEpgProgrammePlayback = { channel, programme ->\r\n                mainContentState.isChannelScreenVisible = false\r\n                mainContentState.changeCurrentChannel(channel, null, programme)\r\n            },\r\n            onEpgProgrammeReserve = { channel, programme ->\r\n                mainContentState.reverseEpgProgrammeOrNot(channel, programme)\r\n            },\r\n            onEpgProgrammeLive = { channel ->\r\n                mainContentState.isChannelScreenVisible = false\r\n                mainContentState.changeCurrentChannel(channel, null, null) // null 表示回到直播\r\n            },\r\n            videoPlayerMetadataProvider = { videoPlayerState.metadata },\r\n            channelFavoriteEnabledProvider = { settingsViewModel.iptvChannelFavoriteEnable },\r\n            channelFavoriteListVisibleProvider = { settingsViewModel.iptvChannelFavoriteListVisible },\r\n            onChannelFavoriteListVisibleChange = {\r\n                settingsViewModel.iptvChannelFavoriteListVisible = it\r\n            },\r\n            iptvSourceListProvider = { settingsViewModel.iptvSourceList },\r\n            currentIptvSourceProvider = { settingsViewModel.iptvSourceCurrent },\r\n            onIptvSourceChanged = { source ->\r\n                mainContentState.isChannelScreenVisible = false\r\n                settingsViewModel.iptvSourceCurrent = source\r\n                settingsViewModel.iptvChannelGroupHiddenList = emptySet()\r\n                settingsViewModel.iptvChannelLastPlay = Channel.EMPTY\r\n                onReload()\r\n            },\r\n            onClose = { mainContentState.isChannelScreenVisible = false },\r\n        )\r\n    }\r\n\r\n    EpgReverseScreen(\r\n        epgProgrammeReserveListProvider = { settingsViewModel.epgChannelReserveList },\r\n        onConfirmReserve = { reserve ->\r\n            filteredChannelGroupListProvider().channelList.firstOrNull { it.name == reserve.channel }\r\n                ?.let {\r\n                    mainContentState.changeCurrentChannel(it)\r\n                }\r\n        },\r\n        onDeleteReserve = { reserve ->\r\n            settingsViewModel.epgChannelReserveList =\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList - reserve)\r\n        },\r\n    )\r\n}\r\n/**\r\n     * 初始化X5内核\r\n     */\r\nprivate fun preInitX5Core(context: Context) { // Accept context as a parameter\r\n    // 预加载x5内核\r\n    val intent = Intent(context, X5CorePreLoadService::class.java)\r\n    X5CorePreLoadService.enqueueWork(context, intent)\r\n}\r\n\r\nprivate fun getKeyDownEvent(actionEvent: KeyDownAction, \r\n                            settingsViewModel: SettingsViewModel, \r\n                            mainContentState: MainContentState, \r\n                            isLoading: Boolean) {\r\n    when (actionEvent) {\r\n        KeyDownAction.ChangeCurrentChannelToNext -> {\r\n            mainContentState.changeCurrentChannelToNext()\r\n        }\r\n        KeyDownAction.ChangeCurrentChannelToPrev -> {\r\n            mainContentState.changeCurrentChannelToPrev()\r\n        }\r\n        KeyDownAction.ChangeCurrentChannelLineIdxToPrev -> {\r\n            if (mainContentState.currentChannel.lineList.size > 1) {\r\n                mainContentState.changeCurrentChannel(\r\n                    mainContentState.currentChannel,\r\n                    mainContentState.currentChannelLineIdx - 1,\r\n                )\r\n            }\r\n        }\r\n        KeyDownAction.ChangeCurrentChannelLineIdxToNext -> {\r\n            if (mainContentState.currentChannel.lineList.size > 1) {\r\n                mainContentState.changeCurrentChannel(\r\n                    mainContentState.currentChannel,\r\n                    mainContentState.currentChannelLineIdx + 1,\r\n                )\r\n            }\r\n        }\r\n        KeyDownAction.ToIptvSourceScreen -> { mainContentState.isIptvSourceScreenVisible = true }\r\n        KeyDownAction.ToChannelScreen -> { if (!isLoading) mainContentState.isChannelScreenVisible = true }\r\n        KeyDownAction.ToQuickOpScreen -> { mainContentState.isQuickOpScreenVisible = true }\r\n        KeyDownAction.ToEpgScreen -> { mainContentState.isEpgScreenVisible = true }\r\n        KeyDownAction.ToChannelLineScreen -> { mainContentState.isChannelLineScreenVisible = true }\r\n        KeyDownAction.ToVideoPlayerControllerScreen -> { mainContentState.isVideoPlayerControllerScreenVisible = true }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContent.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContent.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContent.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContent.kt	(date 1756691200116)
@@ -12,6 +12,10 @@
 import android.content.Context
 import android.content.Intent
 import androidx.compose.runtime.remember
+import androidx.compose.runtime.getValue
+import androidx.compose.runtime.setValue
+import androidx.compose.runtime.mutableStateOf
+import kotlinx.coroutines.delay
 import top.yogiczy.mytv.core.data.utils.Constants
 import top.yogiczy.mytv.core.data.utils.Logger
 import top.yogiczy.mytv.core.data.entities.actions.KeyDownAction
@@ -24,6 +28,7 @@
 import top.yogiczy.mytv.core.data.entities.epg.EpgList
 import top.yogiczy.mytv.core.data.entities.epg.EpgList.Companion.match
 import top.yogiczy.mytv.core.data.entities.epg.EpgList.Companion.recentProgramme
+import top.yogiczy.mytv.core.data.entities.epg.EpgProgramme
 import top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList
 import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList
 import top.yogiczy.mytv.core.data.repositories.epg.EpgRepository
@@ -65,6 +70,7 @@
 import top.yogiczy.mytv.tv.ui.utils.Configs
 import top.yogiczy.mytv.tv.X5CorePreLoadService
 import top.yogiczy.mytv.tv.R
+import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
 
 @Composable
 fun MainContent(
@@ -351,34 +357,115 @@
         visibleProvider = { mainContentState.isVideoPlayerControllerScreenVisible },
         onDismissRequest = { mainContentState.isVideoPlayerControllerScreenVisible = false },
     ) {
-        val threshold = 1000L * 60 * 60 * 24 * 365
-        val hour0 = -28800000L
+        // Ticking clock while controller is visible to refresh time-based UI
+        var now by remember { mutableStateOf(System.currentTimeMillis()) }
+        LaunchedEffect(Unit) {
+            while (true) {
+                now = System.currentTimeMillis()
+                delay(1000L)
+            }
+        }
+
+        // Anchor the live window start when controller opens; end follows current time
+        val openedAt = remember { now }
+        var liveOffsetMs by remember { mutableStateOf(0L) } // distance from live edge
+        
+        // Store previous states to avoid flickers during seeking
+        var lastKnownPlayingState by remember { mutableStateOf(videoPlayerState.isPlaying) }
+        var lastKnownCurrentPosition by remember { mutableStateOf(videoPlayerState.currentPosition) }
+        
+        if (!mainContentState.isSeeking) {
+            lastKnownPlayingState = videoPlayerState.isPlaying
+            lastKnownCurrentPosition = videoPlayerState.currentPosition
+        }
 
         VideoPlayerControllerScreen(
-            isVideoPlayerPlayingProvider = { videoPlayerState.isPlaying },
-            isVideoPlayerBufferingProvider = { videoPlayerState.isBuffering },
-            videoPlayerCurrentPositionProvider = {
-                if (videoPlayerState.currentPosition >= threshold) videoPlayerState.currentPosition
-                else hour0 + videoPlayerState.currentPosition
+            isVideoPlayerPlayingProvider = { 
+                if (mainContentState.isSeeking) lastKnownPlayingState else videoPlayerState.isPlaying 
             },
-            videoPlayerDurationProvider = {
-                if (videoPlayerState.currentPosition >= threshold) {
-                    val playback = mainContentState.currentPlaybackEpgProgramme
-
-                    if (playback != null) {
-                        playback.startAt to playback.endAt
-                    } else {
-                        val programme =
-                            epgListProvider().recentProgramme(mainContentState.currentChannel)?.now
-                        (programme?.startAt ?: hour0) to (programme?.endAt ?: hour0)
-                    }
-                } else {
-                    hour0 to (hour0 + videoPlayerState.duration)
+            isVideoPlayerBufferingProvider = { 
+                videoPlayerState.isBuffering || mainContentState.isSeeking 
+            },
+            videoTimelineProvider = {
+                val playback = mainContentState.currentPlaybackEpgProgramme
+                if (playback == null) {
+                    // Pure live
+                    val start = now - (48L * 60 * 60 * 1000)
+                    val end = now
+                    val current = now - liveOffsetMs
+                    Triple(start, end, current)
+                } else if (settingsViewModel.videoPlayerPlaybackMode == VideoPlayerPlaybackMode.RELOAD_URL && playback.title == "Timeshift") {
+                    // Live timeshift via reload URL should still display a 48h live window
+                    val start = now - (48L * 60 * 60 * 1000)
+                    val end = now
+                    val current = now - liveOffsetMs
+                    Triple(start, end, current)
+                } else {
+                    // EPG playback window - limit end time to current time to prevent seeking beyond live edge
+                    val currentPlayerPosition = if (mainContentState.isSeeking) {
+                        lastKnownCurrentPosition
+                    } else {
+                        videoPlayerState.currentPosition
+                    }
+                    Triple(
+                        playback.startAt,
+                        kotlin.math.min(playback.endAt, now),
+                        playback.startAt + mainContentState.playbackSeekOffset + currentPlayerPosition,
+                    )
                 }
             },
             onVideoPlayerPlay = { videoPlayerState.play() },
             onVideoPlayerPause = { videoPlayerState.pause() },
-            onVideoPlayerSeekTo = { videoPlayerState.seekTo(it) },
+            onVideoPlayerSeekTo = { offsetFromStart ->
+                val playback = mainContentState.currentPlaybackEpgProgramme
+                val isLiveWindow = playback == null || (
+                    settingsViewModel.videoPlayerPlaybackMode == VideoPlayerPlaybackMode.RELOAD_URL &&
+                        playback.title == "Timeshift"
+                    )
+                val start = if (isLiveWindow) now - (48L * 60 * 60 * 1000) else playback!!.startAt
+                val end = if (isLiveWindow) now else playback!!.endAt
+                val desiredAbsolute = (start + offsetFromStart).coerceIn(start, end)
+
+                when (settingsViewModel.videoPlayerPlaybackMode) {
+                    VideoPlayerPlaybackMode.RELOAD_URL -> {
+                        val playback = mainContentState.currentPlaybackEpgProgramme
+                        if (isLiveWindow) {
+                            // For live window, create timeshift from desired time to current time
+                            val newPlayback = EpgProgramme(
+                                startAt = desiredAbsolute,
+                                endAt = now,
+                                title = "Timeshift",
+                            )
+                            liveOffsetMs = now - desiredAbsolute
+                            mainContentState.isSeeking = true
+                            mainContentState.changeCurrentChannel(
+                                mainContentState.currentChannel,
+                                mainContentState.currentChannelLineIdx,
+                                newPlayback,
+                            )
+                        } else {
+                            // For EPG programs, keep original programme but set playback seek offset
+                            // This will preserve the timeline display while generating correct URL
+                            val seekOffset = desiredAbsolute - playback!!.startAt
+                            mainContentState.setPlaybackSeekOffset(seekOffset)
+                            mainContentState.isSeeking = true
+                            mainContentState.changeCurrentChannel(
+                                mainContentState.currentChannel,
+                                mainContentState.currentChannelLineIdx,
+                                playback,
+                            )
+                        }
+                    }
+                    VideoPlayerPlaybackMode.SEEK_TO -> {
+                        val playback = mainContentState.currentPlaybackEpgProgramme
+                        if (playback == null) {
+                            // Live: update offset so absolute time reflects the seek target and keeps moving with time
+                            liveOffsetMs = now - desiredAbsolute
+                        }
+                        videoPlayerState.seekTo(offsetFromStart)
+                    }
+                }
+            },
             onClose = { mainContentState.isVideoPlayerControllerScreenVisible = false },
         )
     }
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.utils\r\n\r\nimport kotlinx.serialization.Serializable\r\nimport kotlinx.serialization.Contextual\r\nimport kotlinx.serialization.encodeToString\r\nimport top.yogiczy.mytv.core.data.entities.actions.KeyDownAction\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSource\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.entities.subtitle.VideoPlayerSubtitleStyle\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport top.yogiczy.mytv.core.data.utils.SP\r\nimport top.yogiczy.mytv.tv.sync.CloudSyncProvider\r\nimport top.yogiczy.mytv.tv.ui.screen.Screens\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode\r\n\r\n/**\r\n * 应用配置\r\n */\r\nobject Configs {\r\n    enum class KEY {\r\n        /** ==================== 应用 ==================== */\r\n        /** 开机自启 */\r\n        APP_BOOT_LAUNCH,\r\n\r\n        /** 画中画启用 */\r\n        APP_PIP_ENABLE,\r\n\r\n        /** 上一次最新版本 */\r\n        APP_LAST_LATEST_VERSION,\r\n\r\n        /** 协议已同意 */\r\n        APP_AGREEMENT_AGREED,\r\n\r\n        /** 打开直接进入直播 */\r\n        APP_STARTUP_SCREEN,\r\n\r\n        /** ==================== 调试 ==================== */\r\n        /** 开发者模式 */\r\n        DEBUG_DEVELOPER_MODE,\r\n\r\n        /** 显示fps */\r\n        DEBUG_SHOW_FPS,\r\n\r\n        /** 播放器详细信息 */\r\n        DEBUG_SHOW_VIDEO_PLAYER_METADATA,\r\n\r\n        /** 显示布局网格 */\r\n        DEBUG_SHOW_LAYOUT_GRIDS,\r\n\r\n        /** ==================== 订阅源 ==================== */\r\n        /** 当前订阅源 */\r\n        IPTV_SOURCE_CURRENT_IDX,\r\n\r\n        /** 订阅源列表 */\r\n        IPTV_SOURCE_LIST,\r\n\r\n        /** 订阅源缓存时间（毫秒） */\r\n        IPTV_SOURCE_CACHE_TIME,\r\n\r\n        /** 订阅源分组隐藏列表 */\r\n        IPTV_CHANNEL_GROUP_HIDDEN_LIST,\r\n\r\n        /** 网页源 */\r\n        IPTV_HYBRID_MODE,\r\n\r\n        /** 网页源央视频Cookie */\r\n        IPTV_HYBRID_YANGSHIPIN_COOKIE,\r\n\r\n        /** 相似频道合并 */\r\n        IPTV_SIMILAR_CHANNEL_MERGE,\r\n\r\n        /** 频道图标提供 */\r\n        IPTV_CHANNEL_LOGO_PROVIDER,\r\n\r\n        /** 频道图标覆盖 */\r\n        IPTV_CHANNEL_LOGO_OVERRIDE,\r\n\r\n        /** PLTV转换至TVOD */\r\n        IPTV_PLTV_TO_TVOD,\r\n\r\n        /** 是否启用订阅源频道收藏 */\r\n        IPTV_CHANNEL_FAVORITE_ENABLE,\r\n\r\n        /** 是否启用订阅源频道最近观看 */\r\n        IPTV_CHANNEL_HISTORY_ENABLE,\r\n\r\n        /** 显示订阅源频道收藏列表 */\r\n        IPTV_CHANNEL_FAVORITE_LIST_VISIBLE,\r\n\r\n        /** 订阅源频道收藏列表 */\r\n        IPTV_CHANNEL_FAVORITE_LIST,\r\n\r\n        /** 频道历史记录 */\r\n        IPTV_CHANNEL_HISTORY_LIST,\r\n\r\n        /** 上一次播放频道 */\r\n        IPTV_CHANNEL_LAST_PLAY,\r\n\r\n        /** 订阅源线路可播放host列表 */\r\n        IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST,\r\n\r\n        /** 订阅源线路可播放地址列表 */\r\n        IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST,\r\n\r\n        /** 是否启用数字选台 */\r\n        IPTV_CHANNEL_NO_SELECT_ENABLE,\r\n\r\n        /** 换台列表首尾循环 **/\r\n        IPTV_CHANNEL_CHANGE_LIST_LOOP,\r\n\r\n        /** 换台跨分组切换 **/\r\n        IPTV_CHANNEL_CHANGE_CROSS_GROUP,\r\n\r\n        /** ==================== 按键行为控制 ==================== */\r\n\r\n        /** 按键行为上键 */\r\n        KEYDOWN_EVENT_UP,\r\n\r\n        /** 按键行为下键 */\r\n        KEYDOWN_EVENT_DOWN,\r\n\r\n        /** 按键行为左键 */\r\n        KEYDOWN_EVENT_LEFT,\r\n\r\n        /** 按键行为右键 */\r\n        KEYDOWN_EVENT_RIGHT,\r\n\r\n        /** 按键行为选择键 */\r\n        KEYDOWN_EVENT_SELECT,\r\n\r\n        /** 按键行为长按上键 */\r\n        KEYDOWN_EVENT_LONG_UP,\r\n\r\n        /** 按键行为长按下键 */\r\n        KEYDOWN_EVENT_LONG_DOWN,\r\n\r\n        /** 按键行为长按左键 */\r\n        KEYDOWN_EVENT_LONG_LEFT,\r\n\r\n        /** 按键行为长按右键 */\r\n        KEYDOWN_EVENT_LONG_RIGHT,\r\n\r\n        /** 按键行为长按选择键 */\r\n        KEYDOWN_EVENT_LONG_SELECT,\r\n\r\n        /** ==================== 节目单 ==================== */\r\n        /** 启用节目单 */\r\n        EPG_ENABLE,\r\n\r\n        /** 当前节目单来源 */\r\n        EPG_SOURCE_CURRENT,\r\n\r\n        /** 节目单来源列表 */\r\n        EPG_SOURCE_LIST,\r\n\r\n        /** 节目单刷新时间阈值（小时） */\r\n        EPG_REFRESH_TIME_THRESHOLD,\r\n\r\n        /** 节目单跟随订阅源 */\r\n        EPG_SOURCE_FOLLOW_IPTV,\r\n\r\n        /** 节目预约列表 */\r\n        EPG_CHANNEL_RESERVE_LIST,\r\n\r\n        /** ==================== 界面 ==================== */\r\n        /** 显示节目进度 */\r\n        UI_SHOW_EPG_PROGRAMME_PROGRESS,\r\n\r\n        /** 显示常驻节目进度 */\r\n        UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS,\r\n\r\n        /** 显示台标 */\r\n        UI_SHOW_CHANNEL_LOGO,\r\n\r\n        /** 显示频道预览 */\r\n        UI_SHOW_CHANNEL_PREVIEW,\r\n\r\n        /** 使用经典选台界面 */\r\n        UI_USE_CLASSIC_PANEL_SCREEN,\r\n\r\n        /** 界面密度缩放比例 */\r\n        UI_DENSITY_SCALE_RATIO,\r\n\r\n        /** 界面字体缩放比例 */\r\n        UI_FONT_SCALE_RATIO,\r\n\r\n        /** 播放器字幕样式 */\r\n        UI_VIDEO_PLAYER_SUBTITLE,\r\n\r\n        /** 时间显示模式 */\r\n        UI_TIME_SHOW_MODE,\r\n\r\n        /** 焦点优化 */\r\n        UI_FOCUS_OPTIMIZE,\r\n\r\n        /** 自动关闭界面延时 */\r\n        UI_SCREEN_AUTO_CLOSE_DELAY,\r\n        \r\n        /** 经典选台界面显示订阅源列表 */\r\n        UI_CLASSIC_SHOW_SOURCE_LIST,\r\n        \r\n        /** 经典选台界面显示全部频道 */\r\n        UI_CLASSIC_SHOW_ALL_CHANNELS,\r\n        \r\n        /** 经典选台界面显示频道信息 */\r\n        UI_CLASSIC_SHOW_CHANNEL_INFO,\r\n\r\n        /** ==================== 更新 ==================== */\r\n        /** 更新强提醒 */\r\n        UPDATE_FORCE_REMIND,\r\n\r\n        /** 更新通道 */\r\n        UPDATE_CHANNEL,\r\n\r\n        /** ==================== 播放器 ==================== */\r\n        /** 播放器 内核 */\r\n        VIDEO_PLAYER_CORE,\r\n        \r\n        /** WebView 内核 */\r\n        WEBVIEW_CORE,\r\n\r\n        /** 替换系统WebView 内核 */\r\n        REPLACE_SYSTEM_WEBVIEW,\r\n        \r\n        /** 播放器 渲染方式 */\r\n        VIDEO_PLAYER_RENDER_MODE,\r\n\r\n        /** 播放器 RTSP 传输方式 */\r\n        VIDEO_PLAYER_RTSP_TRANSPORT,\r\n\r\n        /** 播放器 自定义ua */\r\n        VIDEO_PLAYER_USER_AGENT,\r\n\r\n        /** 播放器 自定义headers */\r\n        VIDEO_PLAYER_HEADERS,\r\n\r\n        /** 播放器 加载超时 */\r\n        VIDEO_PLAYER_LOAD_TIMEOUT,\r\n\r\n        /** WebView 加载超时 */\r\n        WEBVIEW_LOAD_TIMEOUT,\r\n\r\n        /** 播放器 HLS 允许无块准备 */\r\n        VIDEO_PLAYER_HLS_ALLOW_CHUNKLESS_PREPARATION,\r\n\r\n        /** 播放器 缓存加载时间 */\r\n        VIDEO_PLAYER_BUFFER_TIME,\r\n\r\n        /** 播放器 显示模式 */\r\n        VIDEO_PLAYER_DISPLAY_MODE,\r\n\r\n        /** 播放器 强制音频软解 */\r\n        VIDEO_PLAYER_FORCE_SOFT_DECODE,\r\n\r\n        /** 播放器 停止上一媒体项 */\r\n        VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM,\r\n\r\n        /** 播放器 跳过同一VSync渲染多帧 */\r\n        VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC,\r\n\r\n        /** 播放器 匹配内容的帧率 */\r\n        VIDEO_PLAYER_FIT_FRAME_RATE,\r\n\r\n        /** 播放器 支持 TS 高级特性 */\r\n        VIDEO_PLAYER_SUPPORT_TS_HIGH_PROFILE,\r\n        \r\n        /** 播放器 支持在链接中提取 Header 信息 */\r\n        VIDEO_PLAYER_EXTRACT_HEADER_FROM_LINK,\r\n        \r\n        /** 播放器音量均衡 */\r\n        VIDEO_PLAYER_VOLUME_NORMALIZATION,\r\n\r\n        /** ==================== 主题 ==================== */\r\n        /** 当前应用主题 */\r\n        THEME_APP_CURRENT,\r\n\r\n        /** ==================== 云同步 ==================== */\r\n        /** 云同步 自动拉取 */\r\n        CLOUD_SYNC_AUTO_PULL,\r\n\r\n        /** 云同步 提供商 */\r\n        CLOUD_SYNC_PROVIDER,\r\n\r\n        /** 云同步 github gist id */\r\n        CLOUD_SYNC_GITHUB_GIST_ID,\r\n\r\n        /** 云同步 github gist token */\r\n        CLOUD_SYNC_GITHUB_GIST_TOKEN,\r\n\r\n        /** 云同步 gitee gist id */\r\n        CLOUD_SYNC_GITEE_GIST_ID,\r\n\r\n        /** 云同步 gitee gist token */\r\n        CLOUD_SYNC_GITEE_GIST_TOKEN,\r\n\r\n        /** 云同步 网络链接 */\r\n        CLOUD_SYNC_NETWORK_URL,\r\n\r\n        /** 云同步 本地文件 */\r\n        CLOUD_SYNC_LOCAL_FILE,\r\n\r\n        /** 云同步 webdav url */\r\n        CLOUD_SYNC_WEBDAV_URL,\r\n\r\n        /** 云同步 webdav 用户名 */\r\n        CLOUD_SYNC_WEBDAV_USERNAME,\r\n\r\n        /** 云同步 webdav 密码 */\r\n        CLOUD_SYNC_WEBDAV_PASSWORD,\r\n    }\r\n\r\n    /** ==================== 应用 ==================== */\r\n    /** 开机自启 */\r\n    var appBootLaunch: Boolean\r\n        get() = SP.getBoolean(KEY.APP_BOOT_LAUNCH.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_BOOT_LAUNCH.name, value)\r\n\r\n    /** 画中画启用 */\r\n    var appPipEnable: Boolean\r\n        get() = SP.getBoolean(KEY.APP_PIP_ENABLE.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_PIP_ENABLE.name, value)\r\n\r\n    /** 上一次最新版本 */\r\n    var appLastLatestVersion: String\r\n        get() = SP.getString(KEY.APP_LAST_LATEST_VERSION.name, \"\")\r\n        set(value) = SP.putString(KEY.APP_LAST_LATEST_VERSION.name, value)\r\n\r\n    /** 协议已同意 */\r\n    var appAgreementAgreed: Boolean\r\n        get() = SP.getBoolean(KEY.APP_AGREEMENT_AGREED.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_AGREEMENT_AGREED.name, value)\r\n\r\n    /** 起始界面 */\r\n    var appStartupScreen: String\r\n        get() = SP.getString(KEY.APP_STARTUP_SCREEN.name, Screens.Live.name)\r\n        set(value) = SP.putString(KEY.APP_STARTUP_SCREEN.name, value)\r\n\r\n    /** ==================== 调式 ==================== */\r\n    /** 开发者模式 */\r\n    var debugDeveloperMode: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_DEVELOPER_MODE.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_DEVELOPER_MODE.name, value)\r\n\r\n    /** 显示fps */\r\n    var debugShowFps: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_FPS.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_FPS.name, value)\r\n\r\n    /** 播放器详细信息 */\r\n    var debugShowVideoPlayerMetadata: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_VIDEO_PLAYER_METADATA.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_VIDEO_PLAYER_METADATA.name, value)\r\n\r\n    /** 显示布局网格 */\r\n    var debugShowLayoutGrids: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_LAYOUT_GRIDS.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_LAYOUT_GRIDS.name, value)\r\n\r\n    /** ==================== 订阅源 ==================== */\r\n    /** 当前订阅源 */\r\n    var iptvSourceCurrentIdx: Int\r\n        get() = SP.getInt(KEY.IPTV_SOURCE_CURRENT_IDX.name, -1)\r\n        set(value) = SP.putInt(KEY.IPTV_SOURCE_CURRENT_IDX.name, value)\r\n\r\n    /** 当前订阅源 */\r\n    var iptvSourceCurrent: IptvSource\r\n        get() = iptvSourceList.getOrNull(iptvSourceCurrentIdx) ?: IptvSource.EMPTY\r\n        set(value) {\r\n            iptvSourceCurrentIdx = iptvSourceList.indexOf(value)\r\n        }\r\n\r\n    /** 订阅源列表 */\r\n    var iptvSourceList: IptvSourceList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(KEY.IPTV_SOURCE_LIST.name, Globals.json.encodeToString(Constants.IPTV_SOURCE_LIST))\r\n        )\r\n        set(value) {\r\n            val iptvCurrent = iptvSourceCurrent\r\n            iptvSourceCurrentIdx = value.indexOf(iptvCurrent)\r\n            SP.putString(KEY.IPTV_SOURCE_LIST.name, Globals.json.encodeToString(value))\r\n        }\r\n\r\n    /** 订阅源缓存时间（毫秒） */\r\n    var iptvSourceCacheTime: Long\r\n        get() = SP.getLong(KEY.IPTV_SOURCE_CACHE_TIME.name, Constants.IPTV_SOURCE_CACHE_TIME)\r\n        set(value) = SP.putLong(KEY.IPTV_SOURCE_CACHE_TIME.name, value)\r\n\r\n    /** 订阅源分组隐藏列表 */\r\n    var iptvChannelGroupHiddenList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_GROUP_HIDDEN_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_GROUP_HIDDEN_LIST.name, value)\r\n\r\n    /** 网页源 */\r\n    var iptvHybridMode: IptvHybridMode\r\n        get() = IptvHybridMode.fromValue(\r\n            SP.getInt(KEY.IPTV_HYBRID_MODE.name, IptvHybridMode.IPTV_FIRST.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.IPTV_HYBRID_MODE.name, value.value)\r\n    \r\n    /** 网页源央视频Cookie */\r\n    var iptvHybridYangshipinCookie: String\r\n        get() = SP.getString(KEY.IPTV_HYBRID_YANGSHIPIN_COOKIE.name, Constants.HYBRID_YANGSHIPIN_COOKIE)\r\n        set(value) = SP.putString(KEY.IPTV_HYBRID_YANGSHIPIN_COOKIE.name, value)\r\n\r\n    /** 相似频道合并 */\r\n    var iptvSimilarChannelMerge: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_SIMILAR_CHANNEL_MERGE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_SIMILAR_CHANNEL_MERGE.name, value)\r\n\r\n    /** 频道图标提供 */\r\n    var iptvChannelLogoProvider: String\r\n        get() = SP.getString(KEY.IPTV_CHANNEL_LOGO_PROVIDER.name, Constants.CHANNEL_LOGO_PROVIDER)\r\n        set(value) = SP.putString(KEY.IPTV_CHANNEL_LOGO_PROVIDER.name, value)\r\n\r\n    /** 频道图标覆盖 */\r\n    var iptvChannelLogoOverride: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_LOGO_OVERRIDE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_LOGO_OVERRIDE.name, value)\r\n\r\n    /** PLTV转换至TVOD */\r\n    var iptvPLTVToTVOD: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_PLTV_TO_TVOD.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_PLTV_TO_TVOD.name, value)\r\n\r\n    /** 是否启用订阅源频道收藏 */\r\n    var iptvChannelFavoriteEnable: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_FAVORITE_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_FAVORITE_ENABLE.name, value)\r\n\r\n    /** 是否启用订阅源频道最近观看 */\r\n    var iptvChannelHistoryEnable: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_HISTORY_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_HISTORY_ENABLE.name, value)\r\n\r\n    /** 显示订阅源频道收藏列表 */\r\n    var iptvChannelFavoriteListVisible: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_FAVORITE_LIST_VISIBLE.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_FAVORITE_LIST_VISIBLE.name, value)\r\n\r\n    /** 订阅源频道收藏列表 */\r\n    var iptvChannelFavoriteList: ChannelFavoriteList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.IPTV_CHANNEL_FAVORITE_LIST.name,\r\n                Globals.json.encodeToString(ChannelFavoriteList())\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.IPTV_CHANNEL_FAVORITE_LIST.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** 频道历史记录 */\r\n    var iptvChannelHistoryList: ChannelList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.IPTV_CHANNEL_HISTORY_LIST.name,\r\n                Globals.json.encodeToString(ChannelList())\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.IPTV_CHANNEL_HISTORY_LIST.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** 上一次播放频道 */\r\n    var iptvChannelLastPlay: Channel\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.IPTV_CHANNEL_LAST_PLAY.name,\r\n                Globals.json.encodeToString(Channel.EMPTY)\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.IPTV_CHANNEL_LAST_PLAY.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** 订阅源线路可播放host列表 */\r\n    var iptvChannelLinePlayableHostList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST.name, value)\r\n\r\n    /** 订阅源线路可播放地址列表 */\r\n    // IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST,\r\n    var iptvChannelLinePlayableUrlList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST.name, value)\r\n\r\n    /** 是否启用数字选台 */\r\n    var iptvChannelNoSelectEnable: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_NO_SELECT_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_NO_SELECT_ENABLE.name, value)\r\n\r\n    /** 换台列表首尾循环 **/\r\n    var iptvChannelChangeListLoop: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_CHANGE_LIST_LOOP.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_CHANGE_LIST_LOOP.name, value)\r\n\r\n    /** 换台跨分组切换 **/\r\n    var iptvChannelChangeCrossGroup: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_CHANGE_CROSS_GROUP.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_CHANGE_CROSS_GROUP.name, value)\r\n\r\n    /** ==================== 节目单 ==================== */\r\n    /** 启用节目单 */\r\n    var epgEnable: Boolean\r\n        get() = SP.getBoolean(KEY.EPG_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.EPG_ENABLE.name, value)\r\n\r\n    /** 当前节目单来源 */\r\n    var epgSourceCurrent: EpgSource\r\n        get() = Globals.json.decodeFromString(SP.getString(KEY.EPG_SOURCE_CURRENT.name, \"\")\r\n            .ifBlank {\r\n                if (Constants.EPG_SOURCE_LIST.isEmpty()) {\r\n                    Globals.json.encodeToString(EpgSource)\r\n                } else {\r\n                    Globals.json.encodeToString(Constants.EPG_SOURCE_LIST.first())\r\n                }\r\n            })\r\n        set(value) = SP.putString(KEY.EPG_SOURCE_CURRENT.name, Globals.json.encodeToString(value))\r\n\r\n    /** 节目单来源列表 */\r\n    var epgSourceList: EpgSourceList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(KEY.EPG_SOURCE_LIST.name, Globals.json.encodeToString(EpgSourceList()))\r\n        )\r\n        set(value) = SP.putString(KEY.EPG_SOURCE_LIST.name, Globals.json.encodeToString(value))\r\n\r\n    /** 节目单刷新时间阈值（小时） */\r\n    var epgRefreshTimeThreshold: Int\r\n        get() = SP.getInt(KEY.EPG_REFRESH_TIME_THRESHOLD.name, Constants.EPG_REFRESH_TIME_THRESHOLD)\r\n        set(value) = SP.putInt(KEY.EPG_REFRESH_TIME_THRESHOLD.name, value)\r\n\r\n    /** 节目单跟随订阅源 */\r\n    var epgSourceFollowIptv: Boolean\r\n        get() = SP.getBoolean(KEY.EPG_SOURCE_FOLLOW_IPTV.name, false)\r\n        set(value) = SP.putBoolean(KEY.EPG_SOURCE_FOLLOW_IPTV.name, value)\r\n\r\n    /** 节目预约列表 */\r\n    var epgChannelReserveList: EpgProgrammeReserveList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.EPG_CHANNEL_RESERVE_LIST.name,\r\n                Globals.json.encodeToString(EpgProgrammeReserveList())\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.EPG_CHANNEL_RESERVE_LIST.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** ==================== 界面 ==================== */\r\n    /** 显示节目进度 */\r\n    var uiShowEpgProgrammeProgress: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PROGRESS.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PROGRESS.name, value)\r\n\r\n    /** 显示常驻节目进度 */\r\n    var uiShowEpgProgrammePermanentProgress: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS.name, value)\r\n\r\n    /** 显示台标 */\r\n    var uiShowChannelLogo: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_CHANNEL_LOGO.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_CHANNEL_LOGO.name, value)\r\n\r\n    /** 显示频道预览 */\r\n    var uiShowChannelPreview: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_CHANNEL_PREVIEW.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_CHANNEL_PREVIEW.name, value)\r\n\r\n    /** 使用经典选台界面 */\r\n    var uiUseClassicPanelScreen: Boolean\r\n        get() = SP.getBoolean(KEY.UI_USE_CLASSIC_PANEL_SCREEN.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_USE_CLASSIC_PANEL_SCREEN.name, value)\r\n\r\n    /** 经典选台界面显示订阅源列表 */\r\n    var uiClassicShowSourceList: Boolean\r\n        get() = SP.getBoolean(KEY.UI_CLASSIC_SHOW_SOURCE_LIST.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_CLASSIC_SHOW_SOURCE_LIST.name, value)\r\n\r\n    /** 经典选台界面显示全部频道 */\r\n    var uiClassicShowAllChannels: Boolean\r\n        get() = SP.getBoolean(KEY.UI_CLASSIC_SHOW_ALL_CHANNELS.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_CLASSIC_SHOW_ALL_CHANNELS.name, value)\r\n\r\n    /** 经典选台界面显示频道信息 */\r\n    var uiClassicShowChannelInfo: Boolean\r\n        get() = SP.getBoolean(KEY.UI_CLASSIC_SHOW_CHANNEL_INFO.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_CLASSIC_SHOW_CHANNEL_INFO.name, value)\r\n\r\n    /** 界面密度缩放比例 */\r\n    var uiDensityScaleRatio: Float\r\n        get() = SP.getFloat(KEY.UI_DENSITY_SCALE_RATIO.name, 0f)\r\n        set(value) = SP.putFloat(KEY.UI_DENSITY_SCALE_RATIO.name, value)\r\n\r\n    /** 界面字体缩放比例 */\r\n    var uiFontScaleRatio: Float\r\n        get() = SP.getFloat(KEY.UI_FONT_SCALE_RATIO.name, 1f)\r\n        set(value) = SP.putFloat(KEY.UI_FONT_SCALE_RATIO.name, value)\r\n\r\n    /** 播放器字幕样式 */\r\n    var uiVideoPlayerSubtitle: VideoPlayerSubtitleStyle\r\n        get() = Globals.json.decodeFromString(\r\n                SP.getString(\r\n                    KEY.UI_VIDEO_PLAYER_SUBTITLE.name,\r\n                    Globals.json.encodeToString(VideoPlayerSubtitleStyle())\r\n                )\r\n            )\r\n        set(value) = SP.putString(\r\n            KEY.UI_VIDEO_PLAYER_SUBTITLE.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n        \r\n    /** 时间显示模式 */\r\n    var uiTimeShowMode: UiTimeShowMode\r\n        get() = UiTimeShowMode.fromValue(\r\n            SP.getInt(KEY.UI_TIME_SHOW_MODE.name, UiTimeShowMode.EVERY_HOUR.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.UI_TIME_SHOW_MODE.name, value.value)\r\n\r\n    /** 焦点优化 */\r\n    var uiFocusOptimize: Boolean\r\n        get() = SP.getBoolean(KEY.UI_FOCUS_OPTIMIZE.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_FOCUS_OPTIMIZE.name, value)\r\n\r\n    /** 自动关闭界面延时 */\r\n    var uiScreenAutoCloseDelay: Long\r\n        get() = SP.getLong(KEY.UI_SCREEN_AUTO_CLOSE_DELAY.name, Constants.UI_SCREEN_AUTO_CLOSE_DELAY)\r\n        set(value) = SP.putLong(KEY.UI_SCREEN_AUTO_CLOSE_DELAY.name, value)\r\n\r\n    /** ==================== 按键行为控制 ==================== */\r\n    /** 按键行为上键 */\r\n    var keyDownEventUp: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_UP.name, KeyDownAction.ChangeCurrentChannelToPrev.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_UP.name, value.value)\r\n\r\n    /** 按键行为下键 */\r\n    var keyDownEventDown: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_DOWN.name, KeyDownAction.ChangeCurrentChannelToNext.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_DOWN.name, value.value)\r\n\r\n    /** 按键行为左键 */\r\n    var keyDownEventLeft: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LEFT.name, KeyDownAction.ChangeCurrentChannelLineIdxToPrev.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LEFT.name, value.value)\r\n\r\n    /** 按键行为右键 */\r\n    var keyDownEventRight: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_RIGHT.name, KeyDownAction.ChangeCurrentChannelLineIdxToNext.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_RIGHT.name, value.value)\r\n\r\n    /** 按键行为选择键 */\r\n    var keyDownEventSelect: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_SELECT.name, KeyDownAction.ToChannelScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_SELECT.name, value.value)\r\n\r\n    /** 按键行为长按上键 */\r\n    var keyDownEventLongUp: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LONG_UP.name, KeyDownAction.ToIptvSourceScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LONG_UP.name, value.value)\r\n\r\n    /** 按键行为长按下键 */\r\n    var keyDownEventLongDown: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LONG_DOWN.name, KeyDownAction.ToVideoPlayerControllerScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LONG_DOWN.name, value.value)\r\n\r\n    /** 按键行为长按左键 */\r\n    var keyDownEventLongLeft: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LONG_LEFT.name, KeyDownAction.ToEpgScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LONG_LEFT.name, value.value)\r\n\r\n    /** 按键行为长按右键 */\r\n    var keyDownEventLongRight: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LONG_RIGHT.name, KeyDownAction.ToChannelLineScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LONG_RIGHT.name, value.value)\r\n\r\n    /** 按键行为长按选择键 */\r\n    var keyDownEventLongSelect: KeyDownAction\r\n        get() = KeyDownAction.fromValue(SP.getInt(KEY.KEYDOWN_EVENT_LONG_SELECT.name, KeyDownAction.ToQuickOpScreen.value))\r\n        set(value) = SP.putInt(KEY.KEYDOWN_EVENT_LONG_SELECT.name, value.value)\r\n\r\n    /** ==================== 更新 ==================== */\r\n    /** 更新强提醒 */\r\n    var updateForceRemind: Boolean\r\n        get() = SP.getBoolean(KEY.UPDATE_FORCE_REMIND.name, false)\r\n        set(value) = SP.putBoolean(KEY.UPDATE_FORCE_REMIND.name, value)\r\n\r\n    /** 更新通道 */\r\n    var updateChannel: String\r\n        get() = SP.getString(KEY.UPDATE_CHANNEL.name, \"stable\")\r\n        set(value) = SP.putString(KEY.UPDATE_CHANNEL.name, value)\r\n\r\n    /** ==================== 播放器 ==================== */\r\n    /** 播放器 内核 */\r\n    var videoPlayerCore: VideoPlayerCore\r\n        get() = VideoPlayerCore.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_CORE.name, VideoPlayerCore.MEDIA3.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_CORE.name, value.value)\r\n\r\n    /** WebView 内核 */\r\n    var webViewCore: WebViewCore\r\n        get() = WebViewCore.fromValue(\r\n            SP.getInt(KEY.WEBVIEW_CORE.name, WebViewCore.SYSTEM.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.WEBVIEW_CORE.name, value.value)\r\n\r\n    /** 替换系统WebView 内核 */\r\n    var replaceSystemWebView: Boolean\r\n        get() = SP.getBoolean(KEY.REPLACE_SYSTEM_WEBVIEW.name, false)\r\n        set(value) = SP.putBoolean(KEY.REPLACE_SYSTEM_WEBVIEW.name, value)\r\n\r\n    /** 播放器 渲染方式 */\r\n    var videoPlayerRenderMode: VideoPlayerRenderMode\r\n        get() = VideoPlayerRenderMode.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_RENDER_MODE.name, VideoPlayerRenderMode.SURFACE_VIEW.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_RENDER_MODE.name, value.value)\r\n\r\n    /** 播放器 RTSP 传输方式 */\r\n    var videoPlayerRtspTransport: RtspTransport\r\n        get() = RtspTransport.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_RTSP_TRANSPORT.name, RtspTransport.TCP.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_RTSP_TRANSPORT.name, value.value)\r\n\r\n    /** 播放器 HLS 允许无块准备 */\r\n    var videoPlayerHlsAllowChunklessPreparation: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_HLS_ALLOW_CHUNKLESS_PREPARATION.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_HLS_ALLOW_CHUNKLESS_PREPARATION.name, value)\r\n\r\n    /** 播放器 自定义ua */\r\n    var videoPlayerUserAgent: String\r\n        get() = SP.getString(KEY.VIDEO_PLAYER_USER_AGENT.name, \"\").ifBlank {\r\n            Constants.VIDEO_PLAYER_USER_AGENT\r\n        }\r\n        set(value) = SP.putString(KEY.VIDEO_PLAYER_USER_AGENT.name, value)\r\n\r\n    /** 播放器 自定义headers */\r\n    var videoPlayerHeaders: String\r\n        get() = SP.getString(KEY.VIDEO_PLAYER_HEADERS.name, \"\")\r\n        set(value) = SP.putString(KEY.VIDEO_PLAYER_HEADERS.name, value)\r\n\r\n    /** 播放器 加载超时 */\r\n    var videoPlayerLoadTimeout: Long\r\n        get() = SP.getLong(KEY.VIDEO_PLAYER_LOAD_TIMEOUT.name, Constants.VIDEO_PLAYER_LOAD_TIMEOUT)\r\n        set(value) = SP.putLong(KEY.VIDEO_PLAYER_LOAD_TIMEOUT.name, value)\r\n\r\n    /** WebView 加载超时 */\r\n    var webViewLoadTimeout: Long\r\n        get() = SP.getLong(KEY.WEBVIEW_LOAD_TIMEOUT.name, Constants.WEBVIEW_LOAD_TIMEOUT)\r\n        set(value) = SP.putLong(KEY.WEBVIEW_LOAD_TIMEOUT.name, value)\r\n\r\n    /** 播放器 缓存加载时间 */\r\n    var videoPlayerBufferTime: Long\r\n        get() = SP.getLong(KEY.VIDEO_PLAYER_BUFFER_TIME.name, Constants.VIDEO_PLAYER_BUFFER_TIME)\r\n        set(value) = SP.putLong(KEY.VIDEO_PLAYER_BUFFER_TIME.name, value)\r\n\r\n    /** 播放器 显示模式 */\r\n    var videoPlayerDisplayMode: VideoPlayerDisplayMode\r\n        get() = VideoPlayerDisplayMode.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_DISPLAY_MODE.name, VideoPlayerDisplayMode.SIXTEEN_NINE.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_DISPLAY_MODE.name, value.value)\r\n\r\n    /** 播放器 强制音频软解 */\r\n    var videoPlayerForceSoftDecode: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_FORCE_SOFT_DECODE.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_FORCE_SOFT_DECODE.name, value)\r\n\r\n    /** 播放器 停止上一媒体项 */\r\n    var videoPlayerStopPreviousMediaItem: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, value)\r\n\r\n    /** 播放器 跳过同一VSync渲染多帧 */\r\n    var videoPlayerSkipMultipleFramesOnSameVSync: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC.name, true)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC.name, value)\r\n\r\n    /** 播放器 匹配内容的帧率 */\r\n    var videoPlayerFitFrameRate: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_FIT_FRAME_RATE.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_FIT_FRAME_RATE.name, value)\r\n\r\n    /** 播放器 支持 TS 高级特性 */\r\n    var videoPlayerSupportTSHighProfile: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_SUPPORT_TS_HIGH_PROFILE.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_SUPPORT_TS_HIGH_PROFILE.name, value)\r\n\r\n    /** 播放器 支持在链接中提取 Header 信息 */\r\n    var videoPlayerExtractHeaderFromLink: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_EXTRACT_HEADER_FROM_LINK.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_EXTRACT_HEADER_FROM_LINK.name, value)\r\n\r\n    /** 播放器音量均衡 */\r\n    var videoPlayerVolumeNormalization: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_VOLUME_NORMALIZATION.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_VOLUME_NORMALIZATION.name, value)\r\n\r\n    /** ==================== 主题 ==================== */\r\n    /** 当前应用主题 */\r\n    var themeAppCurrent: AppThemeDef?\r\n        get() = SP.getString(KEY.THEME_APP_CURRENT.name, \"\").let {\r\n            if (it.isBlank()) null else Globals.json.decodeFromString(it)\r\n        }\r\n        set(value) = SP.putString(\r\n            KEY.THEME_APP_CURRENT.name,\r\n            value?.let { Globals.json.encodeToString(value) } ?: \"\")\r\n\r\n    /** ==================== 云同步 ==================== */\r\n    /** 云同步 自动拉取 */\r\n    var cloudSyncAutoPull: Boolean\r\n        get() = SP.getBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, false)\r\n        set(value) = SP.putBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, value)\r\n\r\n    /** 云同步 提供商 */\r\n    var cloudSyncProvider: CloudSyncProvider\r\n        get() = CloudSyncProvider.fromValue(\r\n            SP.getInt(KEY.CLOUD_SYNC_PROVIDER.name, CloudSyncProvider.GITHUB_GIST.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.CLOUD_SYNC_PROVIDER.name, value.value)\r\n\r\n    /** 云同步 github gist id */\r\n    var cloudSyncGithubGistId: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITHUB_GIST_ID.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITHUB_GIST_ID.name, value)\r\n\r\n    /** 云同步 github gist token */\r\n    var cloudSyncGithubGistToken: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITHUB_GIST_TOKEN.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITHUB_GIST_TOKEN.name, value)\r\n\r\n    /** 云同步 gitee gist id */\r\n    var cloudSyncGiteeGistId: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITEE_GIST_ID.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITEE_GIST_ID.name, value)\r\n\r\n    /** 云同步 gitee gist token */\r\n    var cloudSyncGiteeGistToken: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITEE_GIST_TOKEN.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITEE_GIST_TOKEN.name, value)\r\n\r\n    /** 云同步 网络链接 */\r\n    var cloudSyncNetworkUrl: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_NETWORK_URL.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_NETWORK_URL.name, value)\r\n\r\n    /** 云同步 本地文件 */\r\n    var cloudSyncLocalFilePath: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_LOCAL_FILE.name, Constants.DEFAULT_LOCAL_SYNC_FILE_PATH)\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_LOCAL_FILE.name, value)\r\n\r\n    /** 云同步 webdav url */\r\n    var cloudSyncWebDavUrl: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_URL.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_URL.name, value)\r\n\r\n    /** 云同步 webdav 用户名 */\r\n    // CLOUD_SYNC_WEBDAV_USERNAME,\r\n    var cloudSyncWebDavUsername: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_USERNAME.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_USERNAME.name, value)\r\n\r\n    /** 云同步 webdav 密码 */\r\n    var cloudSyncWebDavPassword: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, value)\r\n\r\n    // /** 肥羊 AllInOne 文件路径 */\r\n    // var feiyangAllInOneFilePath: String\r\n    //     get() = SP.getString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, \"\")\r\n    //     set(value) = SP.putString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, value)\r\n\r\n    // 添加网络重试次数和间隔时间\r\n    var networkRetryCount: Long\r\n        get() = SP.getLong(\"network_retry_count\", Constants.NETWORK_RETRY_COUNT)\r\n        set(value) = SP.putLong(\"network_retry_count\", value)\r\n\r\n    var networkRetryInterval: Long\r\n        get() = SP.getLong(\"network_retry_interval\", Constants.NETWORK_RETRY_INTERVAL)\r\n        set(value) = SP.putLong(\"network_retry_interval\", value)\r\n\r\n    /** 经典选台界面上次选中分组名 */\r\n    var classicPanelLastSelectedGroupName: String?\r\n        get() = SP.getString(\"classicPanelLastSelectedGroupName\", \"\").ifBlank { null }\r\n        set(value) = SP.putString(\"classicPanelLastSelectedGroupName\", value ?: \"\")\r\n\r\n    enum class UiTimeShowMode(val value: Int) {\r\n        /** 隐藏 */\r\n        HIDDEN(0),\r\n\r\n        /** 常显 */\r\n        ALWAYS(1),\r\n\r\n        /** 整点 */\r\n        EVERY_HOUR(2),\r\n\r\n        /** 半点 */\r\n        HALF_HOUR(3);\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): UiTimeShowMode {\r\n                return entries.firstOrNull { it.value == value } ?: ALWAYS\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class IptvHybridMode(val value: Int) {\r\n        /** 禁用 */\r\n        DISABLE(0),\r\n\r\n        /** 订阅源优先 */\r\n        IPTV_FIRST(1),\r\n\r\n        /** 网页源优先 */\r\n        HYBRID_FIRST(2);\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): IptvHybridMode {\r\n                return entries.firstOrNull { it.value == value } ?: DISABLE\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class VideoPlayerCore(val value: Int, val label: String) {\r\n        /** Media3 */\r\n        MEDIA3(0, \"Media3\"),\r\n\r\n        /** IJK */\r\n        IJK(1, \"IjkPlayer\"),\r\n\r\n        /** VLC */\r\n        VLC(2, \"VLC\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): VideoPlayerCore {\r\n                return entries.firstOrNull { it.value == value } ?: MEDIA3\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class WebViewCore(val value: Int, val label: String) {\r\n        /** 系统内核 */\r\n        SYSTEM(0, \"Android\"),\r\n\r\n        /** X5内核 */\r\n        X5(1, \"TBS X5\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): WebViewCore {\r\n                return entries.firstOrNull { it.value == value } ?: SYSTEM\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class VideoPlayerRenderMode(val value: Int, val label: String) {\r\n        /** SurfaceView */\r\n        SURFACE_VIEW(0, \"SurfaceView\"),\r\n\r\n        /** TextureView */\r\n        TEXTURE_VIEW(1, \"TextureView\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): VideoPlayerRenderMode {\r\n                return entries.firstOrNull { it.value == value } ?: SURFACE_VIEW\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class RtspTransport(val value: Int, val label: String) {\r\n        /** TCP */\r\n        TCP(0, \"TCP\"),\r\n\r\n        /** UDP */\r\n        UDP(1, \"UDP\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): RtspTransport {\r\n                return entries.firstOrNull { it.value == value } ?: TCP\r\n            }\r\n        }\r\n    }\r\n\r\n    fun toPartial(): Partial {\r\n        return Partial(\r\n            appBootLaunch = appBootLaunch,\r\n            appPipEnable = appPipEnable,\r\n            appLastLatestVersion = appLastLatestVersion,\r\n            appAgreementAgreed = appAgreementAgreed,\r\n            appStartupScreen = appStartupScreen,\r\n            debugDeveloperMode = debugDeveloperMode,\r\n            debugShowFps = debugShowFps,\r\n            debugShowVideoPlayerMetadata = debugShowVideoPlayerMetadata,\r\n            debugShowLayoutGrids = debugShowLayoutGrids,\r\n            iptvSourceCacheTime = iptvSourceCacheTime,\r\n            iptvSourceCurrentIdx = iptvSourceCurrentIdx,\r\n            iptvSourceList = iptvSourceList,\r\n            iptvChannelGroupHiddenList = iptvChannelGroupHiddenList,\r\n            iptvHybridMode = iptvHybridMode,\r\n            iptvHybridYangshipinCookie = iptvHybridYangshipinCookie,\r\n            iptvSimilarChannelMerge = iptvSimilarChannelMerge,\r\n            iptvChannelLogoProvider = iptvChannelLogoProvider,\r\n            iptvChannelLogoOverride = iptvChannelLogoOverride,\r\n            iptvPLTVToTVOD = iptvPLTVToTVOD,\r\n            iptvChannelFavoriteEnable = iptvChannelFavoriteEnable,\r\n            iptvChannelHistoryEnable = iptvChannelHistoryEnable,\r\n            iptvChannelFavoriteListVisible = iptvChannelFavoriteListVisible,\r\n            iptvChannelFavoriteList = iptvChannelFavoriteList,\r\n            iptvChannelHistoryList = iptvChannelHistoryList,\r\n            iptvChannelLastPlay = iptvChannelLastPlay,\r\n            iptvChannelLinePlayableHostList = iptvChannelLinePlayableHostList,\r\n            iptvChannelLinePlayableUrlList = iptvChannelLinePlayableUrlList,\r\n            iptvChannelNoSelectEnable = iptvChannelNoSelectEnable,\r\n            iptvChannelChangeListLoop = iptvChannelChangeListLoop,\r\n            iptvChannelChangeCrossGroup = iptvChannelChangeCrossGroup,\r\n            epgEnable = epgEnable,\r\n            epgSourceCurrent = epgSourceCurrent,\r\n            epgSourceList = epgSourceList,\r\n            epgRefreshTimeThreshold = epgRefreshTimeThreshold,\r\n            epgSourceFollowIptv = epgSourceFollowIptv,\r\n            epgChannelReserveList = epgChannelReserveList,\r\n            uiShowEpgProgrammeProgress = uiShowEpgProgrammeProgress,\r\n            uiShowEpgProgrammePermanentProgress = uiShowEpgProgrammePermanentProgress,\r\n            uiShowChannelLogo = uiShowChannelLogo,\r\n            uiShowChannelPreview = uiShowChannelPreview,\r\n            uiUseClassicPanelScreen = uiUseClassicPanelScreen,\r\n            uiDensityScaleRatio = uiDensityScaleRatio,\r\n            uiFontScaleRatio = uiFontScaleRatio,\r\n            uiVideoPlayerSubtitle = uiVideoPlayerSubtitle,\r\n            uiTimeShowMode = uiTimeShowMode,\r\n            uiFocusOptimize = uiFocusOptimize,\r\n            uiScreenAutoCloseDelay = uiScreenAutoCloseDelay,\r\n            keyDownEventUp = keyDownEventUp,\r\n            keyDownEventDown = keyDownEventDown,\r\n            keyDownEventLeft = keyDownEventLeft,\r\n            keyDownEventRight = keyDownEventRight,\r\n            keyDownEventSelect = keyDownEventSelect,\r\n            keyDownEventLongUp = keyDownEventLongUp,\r\n            keyDownEventLongDown = keyDownEventLongDown,\r\n            keyDownEventLongLeft = keyDownEventLongLeft,\r\n            keyDownEventLongRight = keyDownEventLongRight,\r\n            keyDownEventLongSelect = keyDownEventLongSelect,\r\n            updateForceRemind = updateForceRemind,\r\n            updateChannel = updateChannel,\r\n            videoPlayerCore = videoPlayerCore,\r\n            webViewCore = webViewCore,\r\n            replaceSystemWebView = replaceSystemWebView,\r\n            videoPlayerRenderMode = videoPlayerRenderMode,\r\n            videoPlayerRtspTransport = videoPlayerRtspTransport,\r\n            videoPlayerUserAgent = videoPlayerUserAgent,\r\n            videoPlayerHeaders = videoPlayerHeaders,\r\n            videoPlayerLoadTimeout = videoPlayerLoadTimeout,\r\n            webViewLoadTimeout = webViewLoadTimeout,\r\n            videoPlayerHlsAllowChunklessPreparation = videoPlayerHlsAllowChunklessPreparation,\r\n            videoPlayerBufferTime = videoPlayerBufferTime,\r\n            videoPlayerDisplayMode = videoPlayerDisplayMode,\r\n            videoPlayerForceSoftDecode = videoPlayerForceSoftDecode,\r\n            videoPlayerStopPreviousMediaItem = videoPlayerStopPreviousMediaItem,\r\n            videoPlayerSkipMultipleFramesOnSameVSync = videoPlayerSkipMultipleFramesOnSameVSync,\r\n            videoPlayerFitFrameRate = videoPlayerFitFrameRate,\r\n            videoPlayerSupportTSHighProfile = videoPlayerSupportTSHighProfile,\r\n            videoPlayerExtractHeaderFromLink = videoPlayerExtractHeaderFromLink,\r\n            videoPlayerVolumeNormalization = videoPlayerVolumeNormalization,\r\n            themeAppCurrent = themeAppCurrent,\r\n            cloudSyncAutoPull = cloudSyncAutoPull,\r\n            cloudSyncProvider = cloudSyncProvider,\r\n            cloudSyncGithubGistId = cloudSyncGithubGistId,\r\n            cloudSyncGithubGistToken = cloudSyncGithubGistToken,\r\n            cloudSyncGiteeGistId = cloudSyncGiteeGistId,\r\n            cloudSyncGiteeGistToken = cloudSyncGiteeGistToken,\r\n            cloudSyncNetworkUrl = cloudSyncNetworkUrl,\r\n            cloudSyncLocalFilePath = cloudSyncLocalFilePath,\r\n            cloudSyncWebDavUrl = cloudSyncWebDavUrl,\r\n            cloudSyncWebDavUsername = cloudSyncWebDavUsername,\r\n            cloudSyncWebDavPassword = cloudSyncWebDavPassword,\r\n            // feiyangAllInOneFilePath = feiyangAllInOneFilePath,\r\n            networkRetryCount = networkRetryCount,\r\n            networkRetryInterval = networkRetryInterval,\r\n            classicPanelLastSelectedGroupName = classicPanelLastSelectedGroupName,\r\n        )\r\n    }\r\n\r\n    fun fromPartial(configs: Partial) {\r\n        configs.appBootLaunch?.let { appBootLaunch = it }\r\n        configs.appPipEnable?.let { appPipEnable = it }\r\n        configs.appLastLatestVersion?.let { appLastLatestVersion = it }\r\n        configs.appAgreementAgreed?.let { appAgreementAgreed = it }\r\n        configs.appStartupScreen?.let { appStartupScreen = it }\r\n        configs.debugDeveloperMode?.let { debugDeveloperMode = it }\r\n        configs.debugShowFps?.let { debugShowFps = it }\r\n        configs.debugShowVideoPlayerMetadata?.let { debugShowVideoPlayerMetadata = it }\r\n        configs.debugShowLayoutGrids?.let { debugShowLayoutGrids = it }\r\n        configs.iptvSourceCacheTime?.let { iptvSourceCacheTime = it }\r\n        configs.iptvSourceCurrentIdx?.let { iptvSourceCurrentIdx = it }\r\n        configs.iptvSourceList?.let { iptvSourceList = it }\r\n        configs.iptvChannelGroupHiddenList?.let { iptvChannelGroupHiddenList = it }\r\n        configs.iptvHybridMode?.let { iptvHybridMode = it }\r\n        configs.iptvHybridYangshipinCookie?.let { iptvHybridYangshipinCookie = it }\r\n        configs.iptvSimilarChannelMerge?.let { iptvSimilarChannelMerge = it }\r\n        configs.iptvChannelLogoProvider?.let { iptvChannelLogoProvider = it }\r\n        configs.iptvChannelLogoOverride?.let { iptvChannelLogoOverride = it }\r\n        configs.iptvPLTVToTVOD?.let { iptvPLTVToTVOD = it }\r\n        configs.iptvChannelFavoriteEnable?.let { iptvChannelFavoriteEnable = it }\r\n        configs.iptvChannelHistoryEnable?.let { iptvChannelHistoryEnable = it }\r\n        configs.iptvChannelFavoriteListVisible?.let { iptvChannelFavoriteListVisible = it }\r\n        configs.iptvChannelFavoriteList?.let { iptvChannelFavoriteList = it }\r\n        configs.iptvChannelHistoryList?.let { iptvChannelHistoryList = it }\r\n        configs.iptvChannelLastPlay?.let { iptvChannelLastPlay = it }\r\n        configs.iptvChannelLinePlayableHostList?.let { iptvChannelLinePlayableHostList = it }\r\n        configs.iptvChannelLinePlayableUrlList?.let { iptvChannelLinePlayableUrlList = it }\r\n        configs.iptvChannelNoSelectEnable?.let { iptvChannelNoSelectEnable = it }\r\n        configs.iptvChannelChangeListLoop?.let { iptvChannelChangeListLoop = it }\r\n        configs.iptvChannelChangeCrossGroup?.let { iptvChannelChangeCrossGroup = it }\r\n        configs.epgEnable?.let { epgEnable = it }\r\n        configs.epgSourceCurrent?.let { epgSourceCurrent = it }\r\n        configs.epgSourceList?.let { epgSourceList = it }\r\n        configs.epgRefreshTimeThreshold?.let { epgRefreshTimeThreshold = it }\r\n        configs.epgSourceFollowIptv?.let { epgSourceFollowIptv = it }\r\n        configs.epgChannelReserveList?.let { epgChannelReserveList = it }\r\n        configs.uiShowEpgProgrammeProgress?.let { uiShowEpgProgrammeProgress = it }\r\n        configs.uiShowEpgProgrammePermanentProgress?.let {\r\n            uiShowEpgProgrammePermanentProgress = it\r\n        }\r\n        configs.uiShowChannelLogo?.let { uiShowChannelLogo = it }\r\n        configs.uiShowChannelPreview?.let { uiShowChannelPreview = it }\r\n        configs.uiUseClassicPanelScreen?.let { uiUseClassicPanelScreen = it }\r\n        configs.uiDensityScaleRatio?.let { uiDensityScaleRatio = it }\r\n        configs.uiFontScaleRatio?.let { uiFontScaleRatio = it }\r\n        configs.uiVideoPlayerSubtitle?.let { uiVideoPlayerSubtitle = it }\r\n        configs.uiTimeShowMode?.let { uiTimeShowMode = it }\r\n        configs.uiFocusOptimize?.let { uiFocusOptimize = it }\r\n        configs.uiScreenAutoCloseDelay?.let { uiScreenAutoCloseDelay = it }\r\n        configs.keyDownEventUp?.let { keyDownEventUp = it }\r\n        configs.keyDownEventDown?.let { keyDownEventDown = it }\r\n        configs.keyDownEventLeft?.let { keyDownEventLeft = it }\r\n        configs.keyDownEventRight?.let { keyDownEventRight = it }\r\n        configs.keyDownEventSelect?.let { keyDownEventSelect = it }\r\n        configs.keyDownEventLongUp?.let { keyDownEventLongUp = it }\r\n        configs.keyDownEventLongDown?.let { keyDownEventLongDown = it }\r\n        configs.keyDownEventLongLeft?.let { keyDownEventLongLeft = it }\r\n        configs.keyDownEventLongRight?.let { keyDownEventLongRight = it }\r\n        configs.keyDownEventLongSelect?.let { keyDownEventLongSelect = it }\r\n        configs.updateForceRemind?.let { updateForceRemind = it }\r\n        configs.updateChannel?.let { updateChannel = it }\r\n        configs.videoPlayerCore?.let { videoPlayerCore = it }\r\n        configs.webViewCore?.let { webViewCore = it }\r\n        configs.replaceSystemWebView?.let { replaceSystemWebView = it }\r\n        configs.videoPlayerRenderMode?.let { videoPlayerRenderMode = it }\r\n        configs.videoPlayerRtspTransport?.let { videoPlayerRtspTransport = it }\r\n        configs.videoPlayerUserAgent?.let { videoPlayerUserAgent = it }\r\n        configs.videoPlayerHeaders?.let { videoPlayerHeaders = it }\r\n        configs.videoPlayerLoadTimeout?.let { videoPlayerLoadTimeout = it }\r\n        configs.webViewLoadTimeout?.let { webViewLoadTimeout = it }\r\n        configs.videoPlayerHlsAllowChunklessPreparation?.let { videoPlayerHlsAllowChunklessPreparation = it }\r\n        configs.videoPlayerBufferTime?.let { videoPlayerBufferTime = it }\r\n        configs.videoPlayerDisplayMode?.let { videoPlayerDisplayMode = it }\r\n        configs.videoPlayerForceSoftDecode?.let { videoPlayerForceSoftDecode = it }\r\n        configs.videoPlayerStopPreviousMediaItem?.let { videoPlayerStopPreviousMediaItem = it }\r\n        configs.videoPlayerSkipMultipleFramesOnSameVSync?.let { videoPlayerSkipMultipleFramesOnSameVSync = it }\r\n        configs.videoPlayerFitFrameRate?.let { videoPlayerFitFrameRate = it }\r\n        configs.videoPlayerSupportTSHighProfile?.let { videoPlayerSupportTSHighProfile = it }\r\n        configs.videoPlayerExtractHeaderFromLink?.let { videoPlayerExtractHeaderFromLink = it }\r\n        configs.videoPlayerVolumeNormalization?.let { videoPlayerVolumeNormalization = it }\r\n        configs.themeAppCurrent?.let { themeAppCurrent = it }\r\n        configs.cloudSyncAutoPull?.let { cloudSyncAutoPull = it }\r\n        configs.cloudSyncProvider?.let { cloudSyncProvider = it }\r\n        configs.cloudSyncGithubGistId?.let { cloudSyncGithubGistId = it }\r\n        configs.cloudSyncGithubGistToken?.let { cloudSyncGithubGistToken = it }\r\n        configs.cloudSyncGiteeGistId?.let { cloudSyncGiteeGistId = it }\r\n        configs.cloudSyncGiteeGistToken?.let { cloudSyncGiteeGistToken = it }\r\n        configs.cloudSyncNetworkUrl?.let { cloudSyncNetworkUrl = it }\r\n        configs.cloudSyncLocalFilePath?.let { cloudSyncLocalFilePath = it }\r\n        configs.cloudSyncWebDavUrl?.let { cloudSyncWebDavUrl = it }\r\n        configs.cloudSyncWebDavUsername?.let { cloudSyncWebDavUsername = it }\r\n        configs.cloudSyncWebDavPassword?.let { cloudSyncWebDavPassword = it }\r\n        // configs.feiyangAllInOneFilePath?.let { feiyangAllInOneFilePath = it }\r\n        configs.networkRetryCount?.let { networkRetryCount = it }\r\n        configs.networkRetryInterval?.let { networkRetryInterval = it }\r\n        configs.classicPanelLastSelectedGroupName?.let { classicPanelLastSelectedGroupName = it }\r\n    }\r\n\r\n    @Serializable\r\n    data class Partial(\r\n        val appBootLaunch: Boolean? = null,\r\n        val appPipEnable: Boolean? = null,\r\n        val appLastLatestVersion: String? = null,\r\n        val appAgreementAgreed: Boolean? = null,\r\n        val appStartupScreen: String? = null,\r\n        val debugDeveloperMode: Boolean? = null,\r\n        val debugShowFps: Boolean? = null,\r\n        val debugShowVideoPlayerMetadata: Boolean? = null,\r\n        val debugShowLayoutGrids: Boolean? = null,\r\n        val iptvSourceCacheTime: Long? = null,\r\n        val iptvSourceCurrentIdx: Int? = null,\r\n        val iptvSourceList: IptvSourceList? = null,\r\n        val iptvChannelGroupHiddenList: Set<String>? = null,\r\n        val iptvHybridMode: IptvHybridMode? = null,\r\n        val iptvHybridYangshipinCookie: String? = null,\r\n        val iptvSimilarChannelMerge: Boolean? = null,\r\n        val iptvChannelLogoProvider: String? = null,\r\n        val iptvChannelLogoOverride: Boolean? = null,\r\n        val iptvPLTVToTVOD: Boolean? = null,\r\n        val iptvChannelFavoriteEnable: Boolean? = null,\r\n        val iptvChannelHistoryEnable: Boolean? = null,\r\n        val iptvChannelFavoriteListVisible: Boolean? = null,\r\n        val iptvChannelFavoriteList: ChannelFavoriteList? = null,\r\n        val iptvChannelHistoryList: ChannelList? = null,\r\n        val iptvChannelLastPlay: Channel? = null,\r\n        val iptvChannelLinePlayableHostList: Set<String>? = null,\r\n        val iptvChannelLinePlayableUrlList: Set<String>? = null,\r\n        val iptvChannelNoSelectEnable: Boolean? = null,\r\n        val iptvChannelChangeListLoop: Boolean? = null,\r\n        val iptvChannelChangeCrossGroup: Boolean? = null,\r\n        val epgEnable: Boolean? = null,\r\n        val epgSourceCurrent: EpgSource? = null,\r\n        val epgSourceList: EpgSourceList? = null,\r\n        val epgRefreshTimeThreshold: Int? = null,\r\n        val epgSourceFollowIptv: Boolean? = null,\r\n        val epgChannelReserveList: EpgProgrammeReserveList? = null,\r\n        val uiShowEpgProgrammeProgress: Boolean? = null,\r\n        val uiShowEpgProgrammePermanentProgress: Boolean? = null,\r\n        val uiShowChannelLogo: Boolean? = null,\r\n        val uiShowChannelPreview: Boolean? = null,\r\n        val uiUseClassicPanelScreen: Boolean? = null,\r\n        val uiDensityScaleRatio: Float? = null,\r\n        val uiFontScaleRatio: Float? = null,\r\n        val uiVideoPlayerSubtitle: @Contextual VideoPlayerSubtitleStyle? = null,\r\n        val uiTimeShowMode: UiTimeShowMode? = null,\r\n        val uiFocusOptimize: Boolean? = null,\r\n        val uiScreenAutoCloseDelay: Long? = null,\r\n        val keyDownEventUp: KeyDownAction? = null,\r\n        val keyDownEventDown: KeyDownAction? = null,\r\n        val keyDownEventLeft: KeyDownAction? = null,\r\n        val keyDownEventRight: KeyDownAction? = null,\r\n        val keyDownEventSelect: KeyDownAction? = null,\r\n        val keyDownEventLongUp: KeyDownAction? = null,\r\n        val keyDownEventLongDown: KeyDownAction? = null,\r\n        val keyDownEventLongLeft: KeyDownAction? = null,\r\n        val keyDownEventLongRight: KeyDownAction? = null,\r\n        val keyDownEventLongSelect: KeyDownAction? = null,\r\n        val updateForceRemind: Boolean? = null,\r\n        val updateChannel: String? = null,\r\n        val videoPlayerCore: VideoPlayerCore? = null,\r\n        val webViewCore: WebViewCore? = null,\r\n        val replaceSystemWebView: Boolean? = null,\r\n        val videoPlayerRenderMode: VideoPlayerRenderMode? = null,\r\n        val videoPlayerRtspTransport: RtspTransport? = null,\r\n        val videoPlayerUserAgent: String? = null,\r\n        val videoPlayerHeaders: String? = null,\r\n        val videoPlayerLoadTimeout: Long? = null,\r\n        val webViewLoadTimeout: Long? = null,\r\n        val videoPlayerHlsAllowChunklessPreparation: Boolean? = null,\r\n        val videoPlayerBufferTime: Long? = null,\r\n        val videoPlayerDisplayMode: VideoPlayerDisplayMode? = null,\r\n        val videoPlayerForceSoftDecode: Boolean? = null,\r\n        val videoPlayerStopPreviousMediaItem: Boolean? = null,\r\n        val videoPlayerSkipMultipleFramesOnSameVSync: Boolean? = null,\r\n        val videoPlayerFitFrameRate: Boolean? = null,\r\n        val videoPlayerSupportTSHighProfile: Boolean? = null,\r\n        val videoPlayerExtractHeaderFromLink: Boolean? = null,\r\n        val videoPlayerVolumeNormalization : Boolean? = null,\r\n        val themeAppCurrent: AppThemeDef? = null,\r\n        val cloudSyncAutoPull: Boolean? = null,\r\n        val cloudSyncProvider: CloudSyncProvider? = null,\r\n        val cloudSyncGithubGistId: String? = null,\r\n        val cloudSyncGithubGistToken: String? = null,\r\n        val cloudSyncGiteeGistId: String? = null,\r\n        val cloudSyncGiteeGistToken: String? = null,\r\n        val cloudSyncNetworkUrl: String? = null,\r\n        val cloudSyncLocalFilePath: String? = null,\r\n        val cloudSyncWebDavUrl: String? = null,\r\n        val cloudSyncWebDavUsername: String? = null,\r\n        val cloudSyncWebDavPassword: String? = null,\r\n        // val feiyangAllInOneFilePath: String? = null,\r\n        val networkRetryCount: Long? = null,\r\n        val networkRetryInterval: Long? = null,\r\n        val classicPanelLastSelectedGroupName: String? = null,\r\n    ) {\r\n        fun desensitized() = copy(\r\n            cloudSyncAutoPull = null,\r\n            cloudSyncProvider = null,\r\n            uiFocusOptimize = null,\r\n            videoPlayerCore = null,\r\n            webViewCore = null,\r\n            replaceSystemWebView = null,\r\n            iptvChannelHistoryList = null,\r\n            // iptvChannelFavoriteList = null,\r\n            videoPlayerForceSoftDecode = null,\r\n            // cloudSyncGithubGistId = null,\r\n            // cloudSyncGithubGistToken = null,\r\n            // cloudSyncGiteeGistId = null,\r\n            // cloudSyncGiteeGistToken = null,\r\n            // cloudSyncNetworkUrl = null,\r\n            // cloudSyncLocalFilePath = null,\r\n            // cloudSyncWebDavUrl = null,\r\n            // cloudSyncWebDavUsername = null,\r\n            // cloudSyncWebDavPassword = null,\r\n            iptvSourceCurrentIdx = null,\r\n            iptvChannelLastPlay = null,\r\n            iptvChannelLinePlayableHostList = null,\r\n            iptvChannelLinePlayableUrlList = null,\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt	(date 1756691200125)
@@ -20,6 +20,7 @@
 import top.yogiczy.mytv.tv.ui.screen.Screens
 import top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode
+import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
 
 /**
  * 应用配置
@@ -263,6 +264,9 @@
         /** 播放器 停止上一媒体项 */
         VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM,
 
+        /** 播放器 回放方式 */
+        VIDEO_PLAYER_PLAYBACK_MODE,
+
         /** 播放器 跳过同一VSync渲染多帧 */
         VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC,
 
@@ -784,6 +788,13 @@
         get() = SP.getBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, false)
         set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, value)
 
+    /** 播放器 回放方式 */
+    var videoPlayerPlaybackMode: VideoPlayerPlaybackMode
+        get() = VideoPlayerPlaybackMode.fromValue(
+            SP.getInt(KEY.VIDEO_PLAYER_PLAYBACK_MODE.name, VideoPlayerPlaybackMode.RELOAD_URL.value)
+        )
+        set(value) = SP.putInt(KEY.VIDEO_PLAYER_PLAYBACK_MODE.name, value.value)
+
     /** 播放器 跳过同一VSync渲染多帧 */
     var videoPlayerSkipMultipleFramesOnSameVSync: Boolean
         get() = SP.getBoolean(KEY.VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC.name, true)
@@ -1068,6 +1079,7 @@
             videoPlayerDisplayMode = videoPlayerDisplayMode,
             videoPlayerForceSoftDecode = videoPlayerForceSoftDecode,
             videoPlayerStopPreviousMediaItem = videoPlayerStopPreviousMediaItem,
+            videoPlayerPlaybackMode = videoPlayerPlaybackMode,
             videoPlayerSkipMultipleFramesOnSameVSync = videoPlayerSkipMultipleFramesOnSameVSync,
             videoPlayerFitFrameRate = videoPlayerFitFrameRate,
             videoPlayerSupportTSHighProfile = videoPlayerSupportTSHighProfile,
@@ -1168,6 +1180,7 @@
         configs.videoPlayerDisplayMode?.let { videoPlayerDisplayMode = it }
         configs.videoPlayerForceSoftDecode?.let { videoPlayerForceSoftDecode = it }
         configs.videoPlayerStopPreviousMediaItem?.let { videoPlayerStopPreviousMediaItem = it }
+        configs.videoPlayerPlaybackMode?.let { videoPlayerPlaybackMode = it }
         configs.videoPlayerSkipMultipleFramesOnSameVSync?.let { videoPlayerSkipMultipleFramesOnSameVSync = it }
         configs.videoPlayerFitFrameRate?.let { videoPlayerFitFrameRate = it }
         configs.videoPlayerSupportTSHighProfile?.let { videoPlayerSupportTSHighProfile = it }
@@ -1266,6 +1279,7 @@
         val videoPlayerDisplayMode: VideoPlayerDisplayMode? = null,
         val videoPlayerForceSoftDecode: Boolean? = null,
         val videoPlayerStopPreviousMediaItem: Boolean? = null,
+        val videoPlayerPlaybackMode: VideoPlayerPlaybackMode? = null,
         val videoPlayerSkipMultipleFramesOnSameVSync: Boolean? = null,
         val videoPlayerFitFrameRate: Boolean? = null,
         val videoPlayerSupportTSHighProfile: Boolean? = null,
@@ -1292,7 +1306,7 @@
             cloudSyncAutoPull = null,
             cloudSyncProvider = null,
             uiFocusOptimize = null,
-            videoPlayerCore = null,
+            // videoPlayerCore = null,
             webViewCore = null,
             replaceSystemWebView = null,
             iptvChannelHistoryList = null,
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.settings\r\n\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableFloatStateOf\r\nimport androidx.compose.runtime.mutableIntStateOf\r\nimport androidx.compose.runtime.mutableLongStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.lifecycle.ViewModel\r\nimport androidx.lifecycle.viewmodel.compose.viewModel\r\nimport top.yogiczy.mytv.core.data.entities.actions.KeyDownAction\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSource\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.entities.subtitle.VideoPlayerSubtitleStyle\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.tv.sync.CloudSyncProvider\r\nimport top.yogiczy.mytv.tv.ui.screen.Screens\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\n\r\nclass SettingsViewModel : ViewModel() {\r\n    private var _appBootLaunch by mutableStateOf(false)\r\n    var appBootLaunch: Boolean\r\n        get() = _appBootLaunch\r\n        set(value) {\r\n            _appBootLaunch = value\r\n            Configs.appBootLaunch = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appPipEnable by mutableStateOf(false)\r\n    var appPipEnable: Boolean\r\n        get() = _appPipEnable\r\n        set(value) {\r\n            _appPipEnable = value\r\n            Configs.appPipEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appLastLatestVersion by mutableStateOf(\"\")\r\n    var appLastLatestVersion: String\r\n        get() = _appLastLatestVersion\r\n        set(value) {\r\n            _appLastLatestVersion = value\r\n            Configs.appLastLatestVersion = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appAgreementAgreed by mutableStateOf(false)\r\n    var appAgreementAgreed: Boolean\r\n        get() = _appAgreementAgreed\r\n        set(value) {\r\n            _appAgreementAgreed = value\r\n            Configs.appAgreementAgreed = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appStartupScreen by mutableStateOf(Screens.Dashboard.name)\r\n    var appStartupScreen: String\r\n        get() = _appStartupScreen\r\n        set(value) {\r\n            _appStartupScreen = value\r\n            Configs.appStartupScreen = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugDeveloperMode by mutableStateOf(false)\r\n    var debugDeveloperMode: Boolean\r\n        get() = _debugDeveloperMode\r\n        set(value) {\r\n            _debugDeveloperMode = value\r\n            Configs.debugDeveloperMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowFps by mutableStateOf(false)\r\n    var debugShowFps: Boolean\r\n        get() = _debugShowFps\r\n        set(value) {\r\n            _debugShowFps = value\r\n            Configs.debugShowFps = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowVideoPlayerMetadata by mutableStateOf(false)\r\n    var debugShowVideoPlayerMetadata: Boolean\r\n        get() = _debugShowVideoPlayerMetadata\r\n        set(value) {\r\n            _debugShowVideoPlayerMetadata = value\r\n            Configs.debugShowVideoPlayerMetadata = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowLayoutGrids by mutableStateOf(false)\r\n    var debugShowLayoutGrids: Boolean\r\n        get() = _debugShowLayoutGrids\r\n        set(value) {\r\n            _debugShowLayoutGrids = value\r\n            Configs.debugShowLayoutGrids = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceCacheTime by mutableLongStateOf(0)\r\n    var iptvSourceCacheTime: Long\r\n        get() = _iptvSourceCacheTime\r\n        set(value) {\r\n            _iptvSourceCacheTime = value\r\n            Configs.iptvSourceCacheTime = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceCurrentIdx by mutableStateOf(-1)\r\n    var iptvSourceCurrent: IptvSource\r\n        get() = _iptvSourceList.getOrNull(_iptvSourceCurrentIdx) ?: IptvSource.EMPTY\r\n        set(value) {\r\n            _iptvSourceCurrentIdx = _iptvSourceList.indexOf(value)\r\n            Configs.iptvSourceCurrentIdx = _iptvSourceCurrentIdx\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceList by mutableStateOf(Constants.IPTV_SOURCE_LIST)\r\n    var iptvSourceList: IptvSourceList\r\n        get() = if (_iptvSourceList.isEmpty()) {\r\n            Constants.IPTV_SOURCE_LIST\r\n        } else {\r\n            _iptvSourceList\r\n        }\r\n        set(value) {\r\n            val iptvCurrent = _iptvSourceList.getOrNull(_iptvSourceCurrentIdx) ?: IptvSource.EMPTY\r\n            _iptvSourceList = value\r\n            iptvSourceCurrent = iptvCurrent\r\n            Configs.iptvSourceList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelGroupHiddenList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelGroupHiddenList: Set<String>\r\n        get() = _iptvChannelGroupHiddenList\r\n        set(value) {\r\n            _iptvChannelGroupHiddenList = value\r\n            Configs.iptvChannelGroupHiddenList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvHybridMode by mutableStateOf(Configs.IptvHybridMode.DISABLE)\r\n    var iptvHybridMode: Configs.IptvHybridMode\r\n        get() = _iptvHybridMode\r\n        set(value) {\r\n            _iptvHybridMode = value\r\n            Configs.iptvHybridMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    \r\n    private var _iptvHybridYangshipinCookie by mutableStateOf(\"\")\r\n    var iptvHybridYangshipinCookie: String\r\n        get() = _iptvHybridYangshipinCookie\r\n        set(value) {\r\n            _iptvHybridYangshipinCookie = value\r\n            Configs.iptvHybridYangshipinCookie = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    private var _iptvSimilarChannelMerge by mutableStateOf(false)\r\n    var iptvSimilarChannelMerge: Boolean\r\n        get() = _iptvSimilarChannelMerge\r\n        set(value) {\r\n            _iptvSimilarChannelMerge = value\r\n            Configs.iptvSimilarChannelMerge = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLogoProvider by mutableStateOf(\"\")\r\n    var iptvChannelLogoProvider: String\r\n        get() = _iptvChannelLogoProvider\r\n        set(value) {\r\n            _iptvChannelLogoProvider = value\r\n            Configs.iptvChannelLogoProvider = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLogoOverride by mutableStateOf(false)\r\n    var iptvChannelLogoOverride: Boolean\r\n        get() = _iptvChannelLogoOverride\r\n        set(value) {\r\n            _iptvChannelLogoOverride = value\r\n            Configs.iptvChannelLogoOverride = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvPLTVToTVOD by mutableStateOf(true)\r\n    var iptvPLTVToTVOD: Boolean\r\n        get() = _iptvPLTVToTVOD\r\n        set(value) {\r\n            _iptvPLTVToTVOD = value\r\n            Configs.iptvPLTVToTVOD = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    \r\n    private var _iptvChannelFavoriteEnable by mutableStateOf(true)\r\n    var iptvChannelFavoriteEnable: Boolean\r\n        get() = _iptvChannelFavoriteEnable\r\n        set(value) {\r\n            _iptvChannelFavoriteEnable = value\r\n            Configs.iptvChannelFavoriteEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelHistoryEnable by mutableStateOf(true)\r\n    var iptvChannelHistoryEnable: Boolean\r\n        get() = _iptvChannelHistoryEnable\r\n        set(value) {\r\n            _iptvChannelHistoryEnable = value\r\n            Configs.iptvChannelHistoryEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n            if(!value) {\r\n                iptvChannelHistoryList = ChannelList()\r\n            }\r\n        }\r\n\r\n    private var _iptvChannelFavoriteListVisible by mutableStateOf(false)\r\n    var iptvChannelFavoriteListVisible: Boolean\r\n        get() = _iptvChannelFavoriteListVisible\r\n        set(value) {\r\n            _iptvChannelFavoriteListVisible = value\r\n            Configs.iptvChannelFavoriteListVisible = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelFavoriteList by mutableStateOf(ChannelFavoriteList())\r\n    var iptvChannelFavoriteList: ChannelFavoriteList\r\n        get() = _iptvChannelFavoriteList\r\n        set(value) {\r\n            _iptvChannelFavoriteList = value\r\n            Configs.iptvChannelFavoriteList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelHistoryList by mutableStateOf(ChannelList())\r\n    var iptvChannelHistoryList: ChannelList\r\n        get() = _iptvChannelHistoryList\r\n        set(value) {\r\n            _iptvChannelHistoryList = value\r\n            Configs.iptvChannelHistoryList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLastPlay by mutableStateOf(Channel.EMPTY)\r\n    var iptvChannelLastPlay: Channel\r\n        get() = _iptvChannelLastPlay\r\n        set(value) {\r\n            _iptvChannelLastPlay = value\r\n            if (_iptvChannelHistoryEnable && !_iptvChannelHistoryList.contains(value)) {\r\n                val newList = _iptvChannelHistoryList.toMutableList().apply {\r\n                    if (size >= Constants.MAX_CHANNEL_HISTORY_SIZE) {\r\n                        removeAt(size - 1)\r\n                    }\r\n                }\r\n                _iptvChannelHistoryList = ChannelList(listOf(value) + newList)\r\n                Configs.iptvChannelHistoryList = _iptvChannelHistoryList\r\n            }\r\n            Configs.iptvChannelLastPlay = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLinePlayableHostList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelLinePlayableHostList: Set<String>\r\n        get() = _iptvChannelLinePlayableHostList\r\n        set(value) {\r\n            _iptvChannelLinePlayableHostList = value\r\n            Configs.iptvChannelLinePlayableHostList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLinePlayableUrlList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelLinePlayableUrlList: Set<String>\r\n        get() = _iptvChannelLinePlayableUrlList\r\n        set(value) {\r\n            _iptvChannelLinePlayableUrlList = value\r\n            Configs.iptvChannelLinePlayableUrlList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelNoSelectEnable by mutableStateOf(false)\r\n    var iptvChannelNoSelectEnable: Boolean\r\n        get() = _iptvChannelNoSelectEnable\r\n        set(value) {\r\n            _iptvChannelNoSelectEnable = value\r\n            Configs.iptvChannelNoSelectEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelChangeListLoop by mutableStateOf(false)\r\n    var iptvChannelChangeListLoop: Boolean\r\n        get() = _iptvChannelChangeListLoop\r\n        set(value) {\r\n            _iptvChannelChangeListLoop = value\r\n            Configs.iptvChannelChangeListLoop = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelChangeCrossGroup by mutableStateOf(false)\r\n    var iptvChannelChangeCrossGroup: Boolean\r\n        get() = _iptvChannelChangeCrossGroup\r\n        set(value) {\r\n            _iptvChannelChangeCrossGroup = value\r\n            Configs.iptvChannelChangeCrossGroup = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n\r\n    private var _epgEnable by mutableStateOf(false)\r\n    var epgEnable: Boolean\r\n        get() = _epgEnable\r\n        set(value) {\r\n            _epgEnable = value\r\n            Configs.epgEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceCurrent by mutableStateOf(EpgSource())\r\n    var epgSourceCurrent: EpgSource\r\n        get() = _epgSourceCurrent\r\n        set(value) {\r\n            _epgSourceCurrent = value\r\n            Configs.epgSourceCurrent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceList by mutableStateOf(EpgSourceList())\r\n    var epgSourceList: EpgSourceList\r\n        get() = _epgSourceList\r\n        set(value) {\r\n            _epgSourceList = value\r\n            Configs.epgSourceList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgRefreshTimeThreshold by mutableIntStateOf(0)\r\n    var epgRefreshTimeThreshold: Int\r\n        get() = _epgRefreshTimeThreshold\r\n        set(value) {\r\n            _epgRefreshTimeThreshold = value\r\n            Configs.epgRefreshTimeThreshold = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceFollowIptv by mutableStateOf(false)\r\n    var epgSourceFollowIptv: Boolean\r\n        get() = _epgSourceFollowIptv\r\n        set(value) {\r\n            _epgSourceFollowIptv = value\r\n            Configs.epgSourceFollowIptv = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgChannelReserveList by mutableStateOf(EpgProgrammeReserveList())\r\n    var epgChannelReserveList: EpgProgrammeReserveList\r\n        get() = _epgChannelReserveList\r\n        set(value) {\r\n            _epgChannelReserveList = value\r\n            Configs.epgChannelReserveList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowEpgProgrammeProgress by mutableStateOf(false)\r\n    var uiShowEpgProgrammeProgress: Boolean\r\n        get() = _uiShowEpgProgrammeProgress\r\n        set(value) {\r\n            _uiShowEpgProgrammeProgress = value\r\n            Configs.uiShowEpgProgrammeProgress = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowEpgProgrammePermanentProgress by mutableStateOf(false)\r\n    var uiShowEpgProgrammePermanentProgress: Boolean\r\n        get() = _uiShowEpgProgrammePermanentProgress\r\n        set(value) {\r\n            _uiShowEpgProgrammePermanentProgress = value\r\n            Configs.uiShowEpgProgrammePermanentProgress = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowChannelLogo by mutableStateOf(true)\r\n    var uiShowChannelLogo: Boolean\r\n        get() = _uiShowChannelLogo\r\n        set(value) {\r\n            _uiShowChannelLogo = value\r\n            Configs.uiShowChannelLogo = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowChannelPreview by mutableStateOf(false)\r\n    var uiShowChannelPreview: Boolean\r\n        get() = _uiShowChannelPreview\r\n        set(value) {\r\n            _uiShowChannelPreview = value\r\n            Configs.uiShowChannelPreview = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiUseClassicPanelScreen by mutableStateOf(false)\r\n    var uiUseClassicPanelScreen: Boolean\r\n        get() = _uiUseClassicPanelScreen\r\n        set(value) {\r\n            _uiUseClassicPanelScreen = value\r\n            Configs.uiUseClassicPanelScreen = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiClassicShowSourceList by mutableStateOf(Configs.uiClassicShowSourceList)\r\n    var uiClassicShowSourceList: Boolean\r\n        get() = _uiClassicShowSourceList\r\n        set(value) {\r\n            _uiClassicShowSourceList = value\r\n            Configs.uiClassicShowSourceList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiClassicShowAllChannels by mutableStateOf(Configs.uiClassicShowAllChannels)\r\n    var uiClassicShowAllChannels: Boolean\r\n        get() = _uiClassicShowAllChannels\r\n        set(value) {\r\n            _uiClassicShowAllChannels = value\r\n            Configs.uiClassicShowAllChannels = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiClassicShowChannelInfo by mutableStateOf(Configs.uiClassicShowChannelInfo)\r\n    var uiClassicShowChannelInfo: Boolean\r\n        get() = _uiClassicShowChannelInfo\r\n        set(value) {\r\n            _uiClassicShowChannelInfo = value\r\n            Configs.uiClassicShowChannelInfo = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiDensityScaleRatio by mutableFloatStateOf(0f)\r\n    var uiDensityScaleRatio: Float\r\n        get() = _uiDensityScaleRatio\r\n        set(value) {\r\n            _uiDensityScaleRatio = value\r\n            Configs.uiDensityScaleRatio = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiFontScaleRatio by mutableFloatStateOf(1f)\r\n    var uiFontScaleRatio: Float\r\n        get() = _uiFontScaleRatio\r\n        set(value) {\r\n            _uiFontScaleRatio = value\r\n            Configs.uiFontScaleRatio = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiVideoPlayerSubtitle by mutableStateOf(VideoPlayerSubtitleStyle())\r\n    var uiVideoPlayerSubtitle: VideoPlayerSubtitleStyle\r\n        get() = _uiVideoPlayerSubtitle\r\n        set(value) {\r\n            _uiVideoPlayerSubtitle = value\r\n            Configs.uiVideoPlayerSubtitle = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiTimeShowMode by mutableStateOf(Configs.UiTimeShowMode.HIDDEN)\r\n    var uiTimeShowMode: Configs.UiTimeShowMode\r\n        get() = _uiTimeShowMode\r\n        set(value) {\r\n            _uiTimeShowMode = value\r\n            Configs.uiTimeShowMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiFocusOptimize by mutableStateOf(false)\r\n    var uiFocusOptimize: Boolean\r\n        get() = _uiFocusOptimize\r\n        set(value) {\r\n            _uiFocusOptimize = value\r\n            Configs.uiFocusOptimize = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiScreenAutoCloseDelay by mutableLongStateOf(0)\r\n    var uiScreenAutoCloseDelay: Long\r\n        get() = _uiScreenAutoCloseDelay\r\n        set(value) {\r\n            _uiScreenAutoCloseDelay = value\r\n            Configs.uiScreenAutoCloseDelay = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _updateForceRemind by mutableStateOf(false)\r\n    var updateForceRemind: Boolean\r\n        get() = _updateForceRemind\r\n        set(value) {\r\n            _updateForceRemind = value\r\n            Configs.updateForceRemind = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _updateChannel by mutableStateOf(\"\")\r\n    var updateChannel: String\r\n        get() = _updateChannel\r\n        set(value) {\r\n            _updateChannel = value\r\n            Configs.updateChannel = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerCore by mutableStateOf(Configs.VideoPlayerCore.MEDIA3)\r\n    var videoPlayerCore: Configs.VideoPlayerCore\r\n        get() = _videoPlayerCore\r\n        set(value) {\r\n            _videoPlayerCore = value\r\n            Configs.videoPlayerCore = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _webViewCore by mutableStateOf(Configs.WebViewCore.SYSTEM)\r\n    var webViewCore: Configs.WebViewCore\r\n        get() = _webViewCore\r\n        set(value) {\r\n            _webViewCore = value\r\n            Configs.webViewCore = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    \r\n    private var _replaceSystemWebView by mutableStateOf(Configs.replaceSystemWebView)\r\n    var replaceSystemWebView: Boolean\r\n        get() = _replaceSystemWebView\r\n        set(value) {\r\n            _replaceSystemWebView = value\r\n            Configs.replaceSystemWebView = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerRenderMode by mutableStateOf(Configs.VideoPlayerRenderMode.SURFACE_VIEW)\r\n    var videoPlayerRenderMode: Configs.VideoPlayerRenderMode\r\n        get() = _videoPlayerRenderMode\r\n        set(value) {\r\n            _videoPlayerRenderMode = value\r\n            Configs.videoPlayerRenderMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerRtspTransport by mutableStateOf(Configs.RtspTransport.TCP)\r\n    var videoPlayerRtspTransport: Configs.RtspTransport\r\n        get() = _videoPlayerRtspTransport\r\n        set(value) {\r\n            _videoPlayerRtspTransport = value\r\n            Configs.videoPlayerRtspTransport = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerUserAgent by mutableStateOf(\"\")\r\n    var videoPlayerUserAgent: String\r\n        get() = _videoPlayerUserAgent\r\n        set(value) {\r\n            _videoPlayerUserAgent = value\r\n            Configs.videoPlayerUserAgent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerHeaders by mutableStateOf(\"\")\r\n    var videoPlayerHeaders: String\r\n        get() = _videoPlayerHeaders\r\n        set(value) {\r\n            _videoPlayerHeaders = value\r\n            Configs.videoPlayerHeaders = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerLoadTimeout by mutableLongStateOf(10000)\r\n    var videoPlayerLoadTimeout: Long\r\n        get() = _videoPlayerLoadTimeout\r\n        set(value) {\r\n            _videoPlayerLoadTimeout = value\r\n            Configs.videoPlayerLoadTimeout = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    \r\n    private var _webViewLoadTimeout by mutableLongStateOf(10000)\r\n    var webViewLoadTimeout: Long\r\n        get() = _webViewLoadTimeout\r\n        set(value) {\r\n            _webViewLoadTimeout = value\r\n            Configs.webViewLoadTimeout = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerBufferTime by mutableLongStateOf(0)\r\n    var videoPlayerBufferTime: Long\r\n        get() = _videoPlayerBufferTime\r\n        set(value) {\r\n            _videoPlayerBufferTime = value\r\n            Configs.videoPlayerBufferTime = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerDisplayMode by mutableStateOf(VideoPlayerDisplayMode.ORIGINAL)\r\n    var videoPlayerDisplayMode: VideoPlayerDisplayMode\r\n        get() = _videoPlayerDisplayMode\r\n        set(value) {\r\n            _videoPlayerDisplayMode = value\r\n            Configs.videoPlayerDisplayMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerForceSoftDecode by mutableStateOf(false)\r\n    var videoPlayerForceSoftDecode: Boolean\r\n        get() = _videoPlayerForceSoftDecode\r\n        set(value) {\r\n            _videoPlayerForceSoftDecode = value\r\n            Configs.videoPlayerForceSoftDecode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerHlsAllowChunklessPreparation by mutableStateOf(false)\r\n    var videoPlayerHlsAllowChunklessPreparation: Boolean\r\n        get() = _videoPlayerHlsAllowChunklessPreparation\r\n        set(value) {\r\n            _videoPlayerHlsAllowChunklessPreparation = value\r\n            Configs.videoPlayerHlsAllowChunklessPreparation = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerStopPreviousMediaItem by mutableStateOf(false)\r\n    var videoPlayerStopPreviousMediaItem: Boolean\r\n        get() = _videoPlayerStopPreviousMediaItem\r\n        set(value) {\r\n            _videoPlayerStopPreviousMediaItem = value\r\n            Configs.videoPlayerStopPreviousMediaItem = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerSkipMultipleFramesOnSameVSync by mutableStateOf(false)\r\n    var videoPlayerSkipMultipleFramesOnSameVSync: Boolean\r\n        get() = _videoPlayerSkipMultipleFramesOnSameVSync\r\n        set(value) {\r\n            _videoPlayerSkipMultipleFramesOnSameVSync = value\r\n            Configs.videoPlayerSkipMultipleFramesOnSameVSync = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n    \r\n    private var _videoPlayerFitFrameRate by mutableStateOf(false)\r\n    var videoPlayerFitFrameRate: Boolean    \r\n        get() = _videoPlayerFitFrameRate\r\n        set(value) {\r\n            _videoPlayerFitFrameRate = value\r\n            Configs.videoPlayerFitFrameRate = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerSupportTSHighProfile by mutableStateOf(false)\r\n    var videoPlayerSupportTSHighProfile: Boolean\r\n        get() = _videoPlayerSupportTSHighProfile\r\n        set(value) {\r\n            _videoPlayerSupportTSHighProfile = value\r\n            Configs.videoPlayerSupportTSHighProfile = value\r\n        }\r\n    \r\n    private var _videoPlayerExtractHeaderFromLink by mutableStateOf(false)\r\n    var videoPlayerExtractHeaderFromLink: Boolean\r\n        get() = _videoPlayerExtractHeaderFromLink\r\n        set(value) {\r\n            _videoPlayerExtractHeaderFromLink = value\r\n            Configs.videoPlayerExtractHeaderFromLink = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerVolumeNormalization by mutableStateOf(false)\r\n    var videoPlayerVolumeNormalization: Boolean\r\n        get() = _videoPlayerVolumeNormalization\r\n        set(value) {\r\n            _videoPlayerVolumeNormalization = value\r\n            Configs.videoPlayerVolumeNormalization = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _networkRetryCount by mutableLongStateOf(Constants.NETWORK_RETRY_COUNT)\r\n    var networkRetryCount: Long\r\n        get() = _networkRetryCount\r\n        set(value) {\r\n            _networkRetryCount = value\r\n            Configs.networkRetryCount = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _networkRetryInterval by mutableLongStateOf(Constants.NETWORK_RETRY_INTERVAL)\r\n    var networkRetryInterval: Long\r\n        get() = _networkRetryInterval\r\n        set(value) {\r\n            _networkRetryInterval = value\r\n            Configs.networkRetryInterval = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _themeAppCurrent by mutableStateOf<AppThemeDef?>(null)\r\n    var themeAppCurrent: AppThemeDef?\r\n        get() = _themeAppCurrent\r\n        set(value) {\r\n            _themeAppCurrent = value\r\n            Configs.themeAppCurrent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _cloudSyncAutoPull by mutableStateOf(false)\r\n    var cloudSyncAutoPull: Boolean\r\n        get() = _cloudSyncAutoPull\r\n        set(value) {\r\n            _cloudSyncAutoPull = value\r\n            Configs.cloudSyncAutoPull = value\r\n        }\r\n\r\n    private var _cloudSyncProvider by mutableStateOf(CloudSyncProvider.GITHUB_GIST)\r\n    var cloudSyncProvider: CloudSyncProvider\r\n        get() = _cloudSyncProvider\r\n        set(value) {\r\n            _cloudSyncProvider = value\r\n            Configs.cloudSyncProvider = value\r\n        }\r\n\r\n    private var _cloudSyncGithubGistId by mutableStateOf(\"\")\r\n    var cloudSyncGithubGistId: String\r\n        get() = _cloudSyncGithubGistId\r\n        set(value) {\r\n            _cloudSyncGithubGistId = value\r\n            Configs.cloudSyncGithubGistId = value\r\n        }\r\n\r\n    private var _cloudSyncGithubGistToken by mutableStateOf(\"\")\r\n    var cloudSyncGithubGistToken: String\r\n        get() = _cloudSyncGithubGistToken\r\n        set(value) {\r\n            _cloudSyncGithubGistToken = value\r\n            Configs.cloudSyncGithubGistToken = value\r\n        }\r\n\r\n    private var _cloudSyncGiteeGistId by mutableStateOf(\"\")\r\n    var cloudSyncGiteeGistId: String\r\n        get() = _cloudSyncGiteeGistId\r\n        set(value) {\r\n            _cloudSyncGiteeGistId = value\r\n            Configs.cloudSyncGiteeGistId = value\r\n        }\r\n\r\n    private var _cloudSyncGiteeGistToken by mutableStateOf(\"\")\r\n    var cloudSyncGiteeGistToken: String\r\n        get() = _cloudSyncGiteeGistToken\r\n        set(value) {\r\n            _cloudSyncGiteeGistToken = value\r\n            Configs.cloudSyncGiteeGistToken = value\r\n        }\r\n\r\n    private var _cloudSyncNetworkUrl by mutableStateOf(\"\")\r\n    var cloudSyncNetworkUrl: String\r\n        get() = _cloudSyncNetworkUrl\r\n        set(value) {\r\n            _cloudSyncNetworkUrl = value\r\n            Configs.cloudSyncNetworkUrl = value\r\n        }\r\n\r\n    private var _cloudSyncLocalFilePath by mutableStateOf(Constants.DEFAULT_LOCAL_SYNC_FILE_PATH)\r\n    var cloudSyncLocalFilePath: String\r\n        get() = _cloudSyncLocalFilePath\r\n        set(value) {\r\n            _cloudSyncLocalFilePath = value\r\n            Configs.cloudSyncLocalFilePath = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavUrl by mutableStateOf(\"\")\r\n    var cloudSyncWebDavUrl: String\r\n        get() = _cloudSyncWebDavUrl\r\n        set(value) {\r\n            _cloudSyncWebDavUrl = value\r\n            Configs.cloudSyncWebDavUrl = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavUsername by mutableStateOf(\"\")\r\n    var cloudSyncWebDavUsername: String\r\n        get() = _cloudSyncWebDavUsername\r\n        set(value) {\r\n            _cloudSyncWebDavUsername = value\r\n            Configs.cloudSyncWebDavUsername = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavPassword by mutableStateOf(\"\")\r\n    var cloudSyncWebDavPassword: String\r\n        get() = _cloudSyncWebDavPassword\r\n        set(value) {\r\n            _cloudSyncWebDavPassword = value\r\n            Configs.cloudSyncWebDavPassword = value\r\n        }\r\n\r\n    private var _keyDownEventUp by mutableStateOf(KeyDownAction.ChangeCurrentChannelToPrev)\r\n    var keyDownEventUp: KeyDownAction\r\n        get() = _keyDownEventUp\r\n        set(value) {\r\n            _keyDownEventUp = value\r\n            Configs.keyDownEventUp = value\r\n        }\r\n\r\n    private var _keyDownEventDown by mutableStateOf(KeyDownAction.ChangeCurrentChannelToNext)\r\n    var keyDownEventDown: KeyDownAction\r\n        get() = _keyDownEventDown\r\n        set(value) {\r\n            _keyDownEventDown = value\r\n            Configs.keyDownEventDown = value\r\n        }\r\n\r\n    private var _keyDownEventLeft by mutableStateOf(KeyDownAction.ChangeCurrentChannelLineIdxToPrev)\r\n    var keyDownEventLeft: KeyDownAction\r\n        get() = _keyDownEventLeft\r\n        set(value) {\r\n            _keyDownEventLeft = value\r\n            Configs.keyDownEventLeft = value\r\n        }\r\n    \r\n    private var _keyDownEventRight by mutableStateOf(KeyDownAction.ChangeCurrentChannelLineIdxToNext)\r\n    var keyDownEventRight: KeyDownAction\r\n        get() = _keyDownEventRight\r\n        set(value) {\r\n            _keyDownEventRight = value\r\n            Configs.keyDownEventRight = value\r\n        }\r\n\r\n    private var _keyDownEventSelect by mutableStateOf(KeyDownAction.ToChannelScreen)\r\n    var keyDownEventSelect: KeyDownAction\r\n        get() = _keyDownEventSelect\r\n        set(value) {\r\n            _keyDownEventSelect = value\r\n            Configs.keyDownEventSelect = value\r\n        }\r\n\r\n    private var _keyDownEventLongUp by mutableStateOf(KeyDownAction.ToIptvSourceScreen)\r\n    var keyDownEventLongUp: KeyDownAction\r\n        get() = _keyDownEventLongUp\r\n        set(value) {\r\n            _keyDownEventLongUp = value\r\n            Configs.keyDownEventLongUp = value\r\n        }\r\n    \r\n    private var _keyDownEventLongDown by mutableStateOf(KeyDownAction.ToVideoPlayerControllerScreen)\r\n    var keyDownEventLongDown: KeyDownAction\r\n        get() = _keyDownEventLongDown\r\n        set(value) {\r\n            _keyDownEventLongDown = value\r\n            Configs.keyDownEventLongDown = value\r\n        }\r\n    \r\n    private var _keyDownEventLongLeft by mutableStateOf(KeyDownAction.ToEpgScreen)\r\n    var keyDownEventLongLeft: KeyDownAction\r\n        get() = _keyDownEventLongLeft\r\n        set(value) {\r\n            _keyDownEventLongLeft = value\r\n            Configs.keyDownEventLongLeft = value\r\n        }\r\n    \r\n    private var _keyDownEventLongRight by mutableStateOf(KeyDownAction.ToChannelLineScreen)\r\n    var keyDownEventLongRight: KeyDownAction\r\n        get() = _keyDownEventLongRight\r\n        set(value) {\r\n            _keyDownEventLongRight = value\r\n            Configs.keyDownEventLongRight = value\r\n        }\r\n    \r\n    private var _keyDownEventLongSelect by mutableStateOf(KeyDownAction.ToQuickOpScreen)\r\n    var keyDownEventLongSelect: KeyDownAction\r\n        get() = _keyDownEventLongSelect\r\n        set(value) {\r\n            _keyDownEventLongSelect = value\r\n            Configs.keyDownEventLongSelect = value\r\n        }\r\n\r\n    private fun afterSetWhenCloudSyncAutoPull() {\r\n        // if (_cloudSyncAutoPull) Snackbar.show(\"云同步：自动拉取已启用\")\r\n    }\r\n\r\n    init {\r\n        runCatching { refresh() }\r\n\r\n        // 删除过期的预约\r\n        _epgChannelReserveList = EpgProgrammeReserveList(\r\n            _epgChannelReserveList.filter {\r\n                System.currentTimeMillis() < it.startAt + 60 * 1000\r\n            }\r\n        )\r\n\r\n        _iptvChannelChangeListLoop = Configs.iptvChannelChangeListLoop\r\n        _iptvChannelChangeCrossGroup = Configs.iptvChannelChangeCrossGroup\r\n        _epgEnable = Configs.epgEnable\r\n    }\r\n\r\n    fun refresh() {\r\n        _appBootLaunch = Configs.appBootLaunch\r\n        _appPipEnable = Configs.appPipEnable\r\n        _appLastLatestVersion = Configs.appLastLatestVersion\r\n        _appAgreementAgreed = Configs.appAgreementAgreed\r\n        _appStartupScreen = Configs.appStartupScreen\r\n        _debugDeveloperMode = Configs.debugDeveloperMode\r\n        _debugShowFps = Configs.debugShowFps\r\n        _debugShowVideoPlayerMetadata = Configs.debugShowVideoPlayerMetadata\r\n        _debugShowLayoutGrids = Configs.debugShowLayoutGrids\r\n        _iptvSourceCacheTime = Configs.iptvSourceCacheTime\r\n        _iptvSourceCurrentIdx = Configs.iptvSourceCurrentIdx\r\n        _iptvSourceList = Configs.iptvSourceList\r\n        _iptvChannelGroupHiddenList = Configs.iptvChannelGroupHiddenList\r\n        _iptvHybridMode = Configs.iptvHybridMode\r\n        _iptvHybridYangshipinCookie = Configs.iptvHybridYangshipinCookie\r\n        _iptvSimilarChannelMerge = Configs.iptvSimilarChannelMerge\r\n        _iptvChannelLogoProvider = Configs.iptvChannelLogoProvider\r\n        _iptvChannelLogoOverride = Configs.iptvChannelLogoOverride\r\n        _iptvPLTVToTVOD = Configs.iptvPLTVToTVOD\r\n        _iptvChannelFavoriteEnable = Configs.iptvChannelFavoriteEnable\r\n        _iptvChannelHistoryEnable = Configs.iptvChannelHistoryEnable\r\n        _iptvChannelFavoriteListVisible = Configs.iptvChannelFavoriteListVisible\r\n        _iptvChannelFavoriteList = Configs.iptvChannelFavoriteList\r\n        _iptvChannelHistoryList = Configs.iptvChannelHistoryList\r\n        _iptvChannelLastPlay = Configs.iptvChannelLastPlay\r\n        _iptvChannelLinePlayableHostList = Configs.iptvChannelLinePlayableHostList\r\n        _iptvChannelLinePlayableUrlList = Configs.iptvChannelLinePlayableUrlList\r\n        _iptvChannelNoSelectEnable = Configs.iptvChannelNoSelectEnable\r\n        _epgEnable = Configs.epgEnable\r\n        _epgSourceCurrent = Configs.epgSourceCurrent\r\n        _epgSourceList = Configs.epgSourceList\r\n        _epgRefreshTimeThreshold = Configs.epgRefreshTimeThreshold\r\n        _epgSourceFollowIptv = Configs.epgSourceFollowIptv\r\n        _epgChannelReserveList = Configs.epgChannelReserveList\r\n        _uiShowEpgProgrammeProgress = Configs.uiShowEpgProgrammeProgress\r\n        _uiShowEpgProgrammePermanentProgress = Configs.uiShowEpgProgrammePermanentProgress\r\n        _uiShowChannelLogo = Configs.uiShowChannelLogo\r\n        _uiShowChannelPreview = Configs.uiShowChannelPreview\r\n        _uiUseClassicPanelScreen = Configs.uiUseClassicPanelScreen\r\n        _uiClassicShowSourceList = Configs.uiClassicShowSourceList\r\n        _uiClassicShowAllChannels = Configs.uiClassicShowAllChannels\r\n        _uiClassicShowChannelInfo = Configs.uiClassicShowChannelInfo\r\n        _uiDensityScaleRatio = Configs.uiDensityScaleRatio\r\n        _uiFontScaleRatio = Configs.uiFontScaleRatio\r\n        _uiVideoPlayerSubtitle = Configs.uiVideoPlayerSubtitle\r\n        _uiTimeShowMode = Configs.uiTimeShowMode\r\n        _uiFocusOptimize = Configs.uiFocusOptimize\r\n        _uiScreenAutoCloseDelay = Configs.uiScreenAutoCloseDelay\r\n        _keyDownEventUp = Configs.keyDownEventUp\r\n        _keyDownEventDown = Configs.keyDownEventDown\r\n        _keyDownEventLeft = Configs.keyDownEventLeft\r\n        _keyDownEventRight = Configs.keyDownEventRight\r\n        _keyDownEventSelect = Configs.keyDownEventSelect\r\n        _keyDownEventLongUp = Configs.keyDownEventLongUp\r\n        _keyDownEventLongDown = Configs.keyDownEventLongDown\r\n        _keyDownEventLongLeft = Configs.keyDownEventLongLeft\r\n        _keyDownEventLongRight = Configs.keyDownEventLongRight\r\n        _keyDownEventLongSelect = Configs.keyDownEventLongSelect\r\n        _updateForceRemind = Configs.updateForceRemind\r\n        _updateChannel = Configs.updateChannel\r\n        _videoPlayerCore = Configs.videoPlayerCore\r\n        _webViewCore = Configs.webViewCore\r\n        _replaceSystemWebView = Configs.replaceSystemWebView\r\n        _videoPlayerRenderMode = Configs.videoPlayerRenderMode\r\n        _videoPlayerRtspTransport = Configs.videoPlayerRtspTransport\r\n        _videoPlayerUserAgent = Configs.videoPlayerUserAgent\r\n        _videoPlayerHeaders = Configs.videoPlayerHeaders\r\n        _videoPlayerLoadTimeout = Configs.videoPlayerLoadTimeout\r\n        _webViewLoadTimeout = Configs.webViewLoadTimeout\r\n        _videoPlayerBufferTime = Configs.videoPlayerBufferTime\r\n        _videoPlayerDisplayMode = Configs.videoPlayerDisplayMode\r\n        _videoPlayerForceSoftDecode = Configs.videoPlayerForceSoftDecode\r\n        _videoPlayerHlsAllowChunklessPreparation = Configs.videoPlayerHlsAllowChunklessPreparation\r\n        _videoPlayerStopPreviousMediaItem = Configs.videoPlayerStopPreviousMediaItem\r\n        _videoPlayerSkipMultipleFramesOnSameVSync = Configs.videoPlayerSkipMultipleFramesOnSameVSync\r\n        _videoPlayerFitFrameRate = Configs.videoPlayerFitFrameRate\r\n        _videoPlayerSupportTSHighProfile = Configs.videoPlayerSupportTSHighProfile\r\n        _videoPlayerExtractHeaderFromLink = Configs.videoPlayerExtractHeaderFromLink\r\n        _networkRetryCount = Configs.networkRetryCount\r\n        _networkRetryInterval = Configs.networkRetryInterval\r\n        _themeAppCurrent = Configs.themeAppCurrent\r\n        _cloudSyncAutoPull = Configs.cloudSyncAutoPull\r\n        _cloudSyncProvider = Configs.cloudSyncProvider\r\n        _cloudSyncGithubGistId = Configs.cloudSyncGithubGistId\r\n        _cloudSyncGithubGistToken = Configs.cloudSyncGithubGistToken\r\n        _cloudSyncGiteeGistId = Configs.cloudSyncGiteeGistId\r\n        _cloudSyncGiteeGistToken = Configs.cloudSyncGiteeGistToken\r\n        _cloudSyncNetworkUrl = Configs.cloudSyncNetworkUrl\r\n        _cloudSyncLocalFilePath = Configs.cloudSyncLocalFilePath\r\n        _cloudSyncWebDavUrl = Configs.cloudSyncWebDavUrl\r\n        _cloudSyncWebDavUsername = Configs.cloudSyncWebDavUsername\r\n        _cloudSyncWebDavPassword = Configs.cloudSyncWebDavPassword\r\n        _videoPlayerVolumeNormalization = Configs.videoPlayerVolumeNormalization\r\n    }\r\n\r\n    companion object {\r\n        var instance: SettingsViewModel? = null\r\n    }\r\n}\r\n\r\nval settingsVM: SettingsViewModel\r\n    @Composable get() = SettingsViewModel.instance ?: viewModel<SettingsViewModel>().also {\r\n        SettingsViewModel.instance = it\r\n    }
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt	(date 1756691200107)
@@ -24,6 +24,7 @@
 import top.yogiczy.mytv.tv.ui.screen.Screens
 import top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef
 import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode
+import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
 import top.yogiczy.mytv.tv.ui.utils.Configs
 
 class SettingsViewModel : ViewModel() {
@@ -611,6 +612,15 @@
             Configs.videoPlayerDisplayMode = value
             afterSetWhenCloudSyncAutoPull()
         }
+
+    private var _videoPlayerPlaybackMode by mutableStateOf(VideoPlayerPlaybackMode.RELOAD_URL)
+    var videoPlayerPlaybackMode: VideoPlayerPlaybackMode
+        get() = _videoPlayerPlaybackMode
+        set(value) {
+            _videoPlayerPlaybackMode = value
+            Configs.videoPlayerPlaybackMode = value
+            afterSetWhenCloudSyncAutoPull()
+        }
 
     private var _videoPlayerForceSoftDecode by mutableStateOf(false)
     var videoPlayerForceSoftDecode: Boolean
@@ -969,6 +979,7 @@
         _webViewLoadTimeout = Configs.webViewLoadTimeout
         _videoPlayerBufferTime = Configs.videoPlayerBufferTime
         _videoPlayerDisplayMode = Configs.videoPlayerDisplayMode
+        _videoPlayerPlaybackMode = Configs.videoPlayerPlaybackMode
         _videoPlayerForceSoftDecode = Configs.videoPlayerForceSoftDecode
         _videoPlayerHlsAllowChunklessPreparation = Configs.videoPlayerHlsAllowChunklessPreparation
         _videoPlayerStopPreviousMediaItem = Configs.videoPlayerStopPreviousMediaItem
Index: gradle.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># Project-wide Gradle settings.\r\n# IDE (e.g. Android Studio) users:\r\n# Gradle settings configured through the IDE *will override*\r\n# any settings specified in this file.\r\n# For more details on how to configure your build environment visit\r\n# http://www.gradle.org/docs/current/userguide/build_environment.html\r\n# Specifies the JVM arguments used for the daemon process.\r\n# The setting is particularly useful for tweaking memory settings.\r\norg.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8\r\n# When configured, Gradle will run in incubating parallel mode.\r\n# This option should only be used with decoupled projects. For more details, visit\r\n# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects\r\n# org.gradle.parallel=true\r\n# AndroidX package structure to make it clearer which packages are bundled with the\r\n# Android operating system, and which are packaged with your app's APK\r\n# https://developer.android.com/topic/libraries/support-library/androidx-rn\r\nandroid.useAndroidX=true\r\n# Kotlin code style for this project: \"official\" or \"obsolete\":\r\nkotlin.code.style=official\r\n# Enables namespacing of each library's R class so that its R class includes only the\r\n# resources declared in the library itself and none from the library's dependencies,\r\n# thereby reducing the size of the R class for that library\r\nandroid.nonTransitiveRClass=true\r\n\r\n# Turn on parallel compilation, caching and on-demand configurationv\r\norg.gradle.configureondemand=true\r\norg.gradle.caching=true\r\norg.gradle.parallel=true\r\n\r\n# Enable R8 full mode.\r\nandroid.enableR8.fullMode=true\r\nandroid.enableR8.debug=true\r\nandroid.disableResourceValidation=true\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/gradle.properties b/gradle.properties
--- a/gradle.properties	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/gradle.properties	(date 1756691200094)
@@ -6,7 +6,7 @@
 # http://www.gradle.org/docs/current/userguide/build_environment.html
 # Specifies the JVM arguments used for the daemon process.
 # The setting is particularly useful for tweaking memory settings.
-org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
+org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8
 # When configured, Gradle will run in incubating parallel mode.
 # This option should only be used with decoupled projects. For more details, visit
 # https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
Index: .idea/vcs.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"VcsDirectoryMappings\">\r\n    <mapping directory=\"\" vcs=\"Git\" />\r\n    <mapping directory=\"$PROJECT_DIR$/../YYKM\" vcs=\"Git\" />\r\n    <mapping directory=\"$PROJECT_DIR$/../media\" vcs=\"Git\" />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/vcs.xml b/.idea/vcs.xml
--- a/.idea/vcs.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/.idea/vcs.xml	(date 1756691200054)
@@ -2,7 +2,5 @@
 <project version="4">
   <component name="VcsDirectoryMappings">
     <mapping directory="" vcs="Git" />
-    <mapping directory="$PROJECT_DIR$/../YYKM" vcs="Git" />
-    <mapping directory="$PROJECT_DIR$/../media" vcs="Git" />
   </component>
 </project>
\ No newline at end of file
Index: core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.core.data.utils\r\n\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLineList\r\nimport java.net.URL\r\nimport java.text.SimpleDateFormat\r\nimport java.util.Locale\r\n\r\n\r\nobject ChannelUtil {\r\n    private val hybridWebViewUrl by lazy {\r\n        mapOf(\r\n            ChannelAlias.standardChannelName(\"cctv-1\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv1/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001859\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-2\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv2/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001800\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-3\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv3/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001801\")\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-4\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv4/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001814\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-5\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv5/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001818\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-5+\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv5plus/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001817\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv6\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv6/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600108442\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-7\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv7/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600004092\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-8\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv8/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001803\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-9\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctvjilu/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600004078\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-10\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv10/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001805\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-11\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv11/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001806\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-12\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv12/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001807\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-13\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv13/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001811\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-14\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctvchild/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001809\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-15\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv15/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001815\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-16\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv16/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600098637\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-17\") to listOf(\r\n                ChannelLine(url = \"https://tv.cctv.com/live/cctv17/\", playbackType = 1, playbackFormat = \"?stime=\\${(b)yyyyMMddHHmmss}&etime=\\${(e)yyyyMMddHHmmss}&type=lbacks\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600001810\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-4k\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002264\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600014550\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn法语\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600084704\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn俄语\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600084758\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn阿语\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600084782\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn西语\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600084744\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cgtn纪录\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600084781\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"风云剧场\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099658\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"第一剧场\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099655\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"怀旧剧场\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099620\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"世界地理\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099637\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"风云音乐\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099660\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"兵器科技\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099649\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"风云足球\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099636\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"高尔夫网球\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099659\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"女性时尚\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099650\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"央视文化精品\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099653\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"央视台球\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099652\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"电视指南\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099656\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"卫生健康\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600099651\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"北京卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.brtn.cn/btv/btvsy_index\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002309\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"江苏卫视\") to listOf(\r\n                ChannelLine(url = \"https://live.jstv.com/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002521\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"东方卫视\") to listOf(\r\n                ChannelLine(url = \"https://live.kankanews.com/huikan/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002483\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"浙江卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.cztv.com/liveTV\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002520\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"湖南卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002475\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"湖北卫视\") to listOf(\r\n                ChannelLine(url = \"https://news.hbtv.com.cn/app/tv/431\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002508\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"广东卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.gdtv.cn/tvChannelDetail/43\",\r\n                httpUserAgent = \"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002485\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"广西卫视\") to listOf(\r\n                ChannelLine(url = \"https://tv.gxtv.cn/channel/channelivePlay_e7a7ab7df9fe11e88bcfe41f13b60c62.html\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002509\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"黑龙江卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.hljtv.com/live/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002498\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"海南卫视\") to listOf(\r\n                ChannelLine(url = \"http://tc.hnntv.cn/zb/28666112.shtml\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002506\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"重庆卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002531\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"四川卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002516\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"河南卫视\") to listOf(\r\n                ChannelLine(url = \"https://static.hntv.tv/kds/#/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002525\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"东南卫视\") to listOf(\r\n                ChannelLine(url = \"http://www.setv.fjtv.net/live/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002484\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"贵州卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.gzstv.com/tv/ch01\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002490\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"江西卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.jxntv.cn/live/#/jxtv1\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002503\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"辽宁卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002505\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"安徽卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.ahtv.cn/folder9000/folder20193?channelIndex=0\",\r\n                    httpUserAgent = \"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002532\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"河北卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.hebtv.com/19/19js/st/xdszb/index.shtml?index=0\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002493\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"山东卫视\") to listOf(\r\n                ChannelLine(url = \"https://v.iqilu.com/live/sdtv/index.html\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600002513\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"天津卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600152137\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"吉林卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.jlntv.cn/tv?id=104\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190405\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"陕西卫视\") to listOf(\r\n                ChannelLine(url = \"http://www.sxtvs.com/sxtvs_sxws/index.html\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190400\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"甘肃卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.gstv.com.cn/zxc.jhtml\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190408\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"宁夏卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.nxtv.com.cn/tv/ws/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190737\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"内蒙古卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.nmtv.cn/liveTv\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190401\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"云南卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.yntv.cn/live.html\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190402\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"山西卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.sxrtv.com/tv/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190407\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"青海卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.qhbtv.com/new_index/live/folder2646/\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190406\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"西藏卫视\") to listOf(\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600190403\"),\r\n            ),\r\n            ChannelAlias.standardChannelName(\"新疆卫视\") to listOf(\r\n                ChannelLine(url = \"https://www.xjtvs.com.cn/column/tv/434\"),\r\n                ChannelLine(url = \"https://yangshipin.cn/tv/home?pid=600152138\"),\r\n            ),\r\n        )\r\n    }\r\n\r\n    fun getHybridWebViewLines(channelName: String): ChannelLineList {\r\n        return ChannelLineList(hybridWebViewUrl[ChannelAlias.standardChannelName(channelName)]\r\n            ?.map { ChannelLine(url = it.url, \r\n                                playbackType = it.playbackType, \r\n                                playbackFormat = it.playbackFormat, \r\n                                httpUserAgent = it.httpUserAgent,\r\n                                playbackUrl = null,\r\n                                hybridType = ChannelLine.HybridType.WebView) }\r\n            ?: emptyList())\r\n    }\r\n\r\n    fun getHybridWebViewUrlProvider(url: String): Int {\r\n        val specificUrls = arrayOf(\r\n            \"brtn.cn\",\r\n            \"jstv.com\",\r\n            \"kankanews.com\",\r\n            \"cztv.com\",\r\n            \"hbtv.com.cn\",\r\n            \"gdtv.cn\",\r\n            \"gxtv.cn\",\r\n            \"hljtv.com\",\r\n            \"hnntv.cn\",\r\n            \"hntv.tv\",\r\n            \"fjtv.net\",\r\n            \"gzstv.com\",\r\n            \"jxntv.cn\",\r\n            \"ahtv.cn\",\r\n            \"hebtv.com\",\r\n            \"iqilu.com\",\r\n            \"jlntv.cn\",\r\n            \"sxtvs.com\",\r\n            \"gstv.com.cn\",\r\n            \"nxtv.com.cn\",\r\n            \"nmtv.cn\",\r\n            \"yntv.cn\",\r\n            \"sxrtv.com\",\r\n            \"qhbtv.com\",\r\n            \"vtibet.cn\",\r\n            \"xjtvs.com.cn\"\r\n        )\r\n        \r\n        try {\r\n            // 处理webview://前缀\r\n            val processedUrl = if (url.startsWith(\"webview://\")) {\r\n                url.substring(\"webview://\".length)\r\n            } else {\r\n                url\r\n            }\r\n            \r\n            val host = URL(processedUrl).host\r\n            return when {\r\n                host.contains(\"cctv.com\") -> 1\r\n                host.contains(\"yangshipin.cn\") -> 2\r\n                specificUrls.any { host.contains(it) } -> 3\r\n                else -> 0\r\n            }\r\n        } catch (e: Exception) {\r\n            Logger.create(\"ChannelUtil\").e(\"解析URL失败: $url, ${e.message}\")\r\n            return -1\r\n        }\r\n    }\r\n\r\n    fun urlSupportPlayback(url: String): Boolean {\r\n        return url.startsWith(\"rtsp://\") && listOf(\"pltv\", \"tvod\").any { url.contains(it, ignoreCase = true) }\r\n    }\r\n\r\n    fun urlToCanPlayback(url: String): String {\r\n        return url.replace(\"pltv\", \"tvod\", ignoreCase = true)\r\n    }\r\n\r\n    fun replacePlaybackFormat(playbackFormat: String?, starttime: Long?, nowtime:Long?, endtime:Long?): String? {\r\n        if (playbackFormat.isNullOrEmpty()) return null // 如果格式为空，直接返回 null\r\n        var regex = Regex(\"\\\\$?\\\\{\\\\(?([a-zA-Z]+)\\\\)?:?([^}]+)\\\\}\") // 匹配 {key:格式} 的正则表达式\r\n        val defaultDateFormat = SimpleDateFormat(\"yyyyMMddHHmmss\", Locale.getDefault()) // 默认时间格式\r\n\r\n        return regex.replace(playbackFormat) { matchResult ->\r\n            val key = matchResult.groupValues[1] // 提取 key，例如 utc 或 end\r\n            val format = matchResult.groupValues[2] // 提取格式字符串\r\n            val time = when (key) {\r\n                \"utc\" -> starttime // 使用开始时间\r\n                \"start\" -> starttime // 使用开始时间\r\n                \"utcend\" -> endtime // 使用结束时间\r\n                \"end\" -> endtime // 使用结束时间\r\n                \"now\" -> nowtime // 使用当前时间\r\n                \"timestamp\" -> nowtime // 使用当前时间戳\r\n                \"lutc\" -> nowtime // 使用当前时间戳\r\n                \"b\" -> starttime // 使用开始时间\r\n                \"e\" -> endtime // 使用结束时间\r\n                else -> return@replace \"\" // 如果 key 不匹配，返回空字符串\r\n            }\r\n            try {\r\n                val customDateFormat = SimpleDateFormat(format, Locale.getDefault())\r\n                if (key  == \"utc\" || key == \"utcend\" || key == \"lutc\") {\r\n                    customDateFormat.timeZone = java.util.TimeZone.getTimeZone(\"UTC\") // 设置时区为 UTC\r\n                }\r\n                customDateFormat.format(time) // 使用自定义格式替换占位符\r\n            } catch (e: IllegalArgumentException) {\r\n                defaultDateFormat.format(time) // 如果格式无效，使用默认格式\r\n            }\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt b/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt
--- a/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt	(date 1756691200093)
@@ -356,7 +356,9 @@
                 else -> return@replace "" // 如果 key 不匹配，返回空字符串
             }
             try {
-                val customDateFormat = SimpleDateFormat(format, Locale.getDefault())
+                // 转换自定义格式为标准SimpleDateFormat格式
+                val standardFormat = convertToStandardDateFormat(format)
+                val customDateFormat = SimpleDateFormat(standardFormat, Locale.getDefault())
                 if (key  == "utc" || key == "utcend" || key == "lutc") {
                     customDateFormat.timeZone = java.util.TimeZone.getTimeZone("UTC") // 设置时区为 UTC
                 }
@@ -366,4 +368,12 @@
             }
         }
     }
+
+    /**
+     * 将自定义时间格式转换为标准的SimpleDateFormat格式
+     * 例如：YmdHMS -> yyyyMMddHHmmss
+     */
+    private fun convertToStandardDateFormat(customFormat: String): String {
+        return if (customFormat == "YmdHMS") "yyyyMMddHHmmss" else customFormat;
+    }
 }
\ No newline at end of file
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/components/VideoPlayerControllerPositionCtrl.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components\r\n\r\nimport androidx.compose.foundation.layout.Arrangement\r\nimport androidx.compose.foundation.layout.Box\r\nimport androidx.compose.foundation.layout.Row\r\nimport androidx.compose.foundation.layout.height\r\nimport androidx.compose.foundation.layout.padding\r\nimport androidx.compose.foundation.layout.width\r\nimport androidx.compose.material.icons.Icons\r\nimport androidx.compose.material.icons.filled.ChevronLeft\r\nimport androidx.compose.material.icons.filled.ChevronRight\r\nimport androidx.compose.material.icons.filled.KeyboardDoubleArrowLeft\r\nimport androidx.compose.material.icons.filled.KeyboardDoubleArrowRight\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.Stable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Alignment\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.compose.ui.unit.dp\r\nimport androidx.tv.material3.Text\r\nimport top.yogiczy.mytv.tv.ui.material.ProgressBar\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport java.text.SimpleDateFormat\r\nimport java.util.Locale\r\nimport kotlin.math.max\r\nimport kotlin.math.min\r\n\r\n@Composable\r\nfun VideoPlayerControllerPositionCtrl(\r\n    modifier: Modifier = Modifier,\r\n    currentPositionProvider: () -> Long = { 0L },\r\n    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },\r\n    seekToPositionProvider: () -> Long? = { null },\r\n    seekForward: (Long) -> Unit = {},\r\n    seekNext: (Long) -> Unit = {},\r\n) {\r\n\r\n    Row(\r\n        modifier = modifier,\r\n        horizontalArrangement = Arrangement.spacedBy(8.dp),\r\n        verticalAlignment = Alignment.CenterVertically,\r\n    ) {\r\n        VideoPlayerControllerBtn(\r\n            imageVector = Icons.Default.KeyboardDoubleArrowLeft,\r\n            onSelect = { seekForward(1000L * 60) },\r\n        )\r\n        VideoPlayerControllerBtn(\r\n            imageVector = Icons.Default.ChevronLeft,\r\n            onSelect = { seekForward(1000L * 10) },\r\n        )\r\n\r\n        VideoPlayerControllerBtn(\r\n            imageVector = Icons.Default.ChevronRight,\r\n            onSelect = { seekNext(1000L * 10) },\r\n        )\r\n        VideoPlayerControllerBtn(\r\n            imageVector = Icons.Default.KeyboardDoubleArrowRight,\r\n            onSelect = { seekNext(1000L * 60) },\r\n        )\r\n\r\n        VideoPlayerControllerPositionProgress(\r\n            modifier = Modifier.padding(start = 10.dp),\r\n            currentPositionProvider = { seekToPositionProvider() ?: currentPositionProvider() },\r\n            durationProvider = durationProvider,\r\n        )\r\n    }\r\n}\r\n\r\n@Composable\r\nprivate fun VideoPlayerControllerPositionProgress(\r\n    modifier: Modifier = Modifier,\r\n    currentPositionProvider: () -> Long = { 0L },\r\n    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },\r\n) {\r\n    val currentPosition = currentPositionProvider()\r\n    val duration = durationProvider()\r\n    val timeFormat = SimpleDateFormat(\"HH:mm:ss\", Locale.getDefault())\r\n\r\n    Row(\r\n        modifier = modifier,\r\n        horizontalArrangement = Arrangement.spacedBy(8.dp),\r\n        verticalAlignment = Alignment.CenterVertically,\r\n    ) {\r\n        Text(\r\n            text = timeFormat.format(duration.first),\r\n        )\r\n\r\n        ProgressBar(\r\n            process = (currentPosition - duration.first) / (duration.second - duration.first).toFloat(),\r\n            modifier = Modifier\r\n                .weight(1f)\r\n                .height(6.dp),\r\n        )\r\n\r\n        Text(\r\n            text = \"${timeFormat.format(currentPosition)} / ${timeFormat.format(duration.second)}\",\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/components/VideoPlayerControllerPositionCtrl.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/components/VideoPlayerControllerPositionCtrl.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/components/VideoPlayerControllerPositionCtrl.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/components/VideoPlayerControllerPositionCtrl.kt	(date 1756691200123)
@@ -30,8 +30,7 @@
 @Composable
 fun VideoPlayerControllerPositionCtrl(
     modifier: Modifier = Modifier,
-    currentPositionProvider: () -> Long = { 0L },
-    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
+    timelineProvider: () -> Triple<Long, Long, Long> = { Triple(0L, 0L, 0L) }, // (start, end, current)
     seekToPositionProvider: () -> Long? = { null },
     seekForward: (Long) -> Unit = {},
     seekNext: (Long) -> Unit = {},
@@ -62,8 +61,11 @@
 
         VideoPlayerControllerPositionProgress(
             modifier = Modifier.padding(start = 10.dp),
-            currentPositionProvider = { seekToPositionProvider() ?: currentPositionProvider() },
-            durationProvider = durationProvider,
+            timelineProvider = {
+                val (start, end, current) = timelineProvider()
+                val seek = seekToPositionProvider()
+                Triple(start, end, seek ?: current)
+            },
         )
     }
 }
@@ -71,11 +73,9 @@
 @Composable
 private fun VideoPlayerControllerPositionProgress(
     modifier: Modifier = Modifier,
-    currentPositionProvider: () -> Long = { 0L },
-    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
+    timelineProvider: () -> Triple<Long, Long, Long> = { Triple(0L, 0L, 0L) },
 ) {
-    val currentPosition = currentPositionProvider()
-    val duration = durationProvider()
+    val (start, end, currentPosition) = timelineProvider()
     val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
 
     Row(
@@ -83,19 +83,15 @@
         horizontalArrangement = Arrangement.spacedBy(8.dp),
         verticalAlignment = Alignment.CenterVertically,
     ) {
-        Text(
-            text = timeFormat.format(duration.first),
-        )
-
         ProgressBar(
-            process = (currentPosition - duration.first) / (duration.second - duration.first).toFloat(),
+            process = (currentPosition - start) / (end - start).toFloat(),
             modifier = Modifier
                 .weight(1f)
                 .height(6.dp),
         )
 
         Text(
-            text = "${timeFormat.format(currentPosition)} / ${timeFormat.format(duration.second)}",
+            text = "${timeFormat.format(currentPosition)} / ${timeFormat.format(end)}",
         )
     }
 }
\ No newline at end of file
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/categories/SettingsVideoPlayerScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.settings.categories\r\n\r\nimport androidx.compose.material.icons.Icons\r\nimport androidx.compose.material.icons.filled.ErrorOutline\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.focus.focusRequester\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.tv.material3.Switch\r\nimport androidx.tv.material3.Text\r\nimport top.yogiczy.mytv.core.util.utils.headersValid\r\nimport top.yogiczy.mytv.core.util.utils.humanizeMs\r\nimport top.yogiczy.mytv.core.util.utils.humanizeBufferNum\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.components.SettingsCategoryScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.components.SettingsListItem\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport top.yogiczy.mytv.tv.R\r\nimport androidx.compose.ui.res.stringResource\r\n\r\n@Composable\r\nfun SettingsVideoPlayerScreen(\r\n    modifier: Modifier = Modifier,\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n    toVideoPlayerCoreScreen: () -> Unit = {},\r\n    toVideoPlayerRenderModeScreen: () -> Unit = {},\r\n    toVideoPlayerRtspTransportScreen: () -> Unit = {},\r\n    toVideoPlayerDisplayModeScreen: () -> Unit = {},\r\n    toVideoPlayerLoadTimeoutScreen: () -> Unit = {},\r\n    toVideoPlayerBufferTimeScreen: () -> Unit = {},\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    SettingsCategoryScreen(\r\n        modifier = modifier,\r\n        header = { Text(\"${stringResource(R.string.ui_dashboard_module_settings)} / ${stringResource(R.string.ui_channel_view_player)}\") },\r\n        onBackPressed = onBackPressed,\r\n    ) { firstItemFocusRequester ->\r\n        item {\r\n            SettingsListItem(\r\n                modifier = Modifier.focusRequester(firstItemFocusRequester),\r\n                headlineContent = stringResource(R.string.ui_player_view_player_core),\r\n                trailingContent = settingsViewModel.videoPlayerCore.label,\r\n                onSelect = toVideoPlayerCoreScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_render_mode),\r\n                trailingContent = settingsViewModel.videoPlayerRenderMode.label,\r\n                onSelect = toVideoPlayerRenderModeScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_force_soft_decode),\r\n                supportingContent = stringResource(R.string.ui_player_view_force_soft_decode_desc),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerForceSoftDecode, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerForceSoftDecode =\r\n                        !settingsViewModel.videoPlayerForceSoftDecode\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_stop_previous_media_item),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerStopPreviousMediaItem, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerStopPreviousMediaItem =\r\n                        !settingsViewModel.videoPlayerStopPreviousMediaItem\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_skip_multiple_frames_on_same_vsync),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerSkipMultipleFramesOnSameVSync, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerSkipMultipleFramesOnSameVSync =\r\n                        !settingsViewModel.videoPlayerSkipMultipleFramesOnSameVSync\r\n                },\r\n            )\r\n        }\r\n\r\n        item{\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_fit_frame_rate),\r\n                supportingContent = stringResource(R.string.ui_player_view_fit_frame_rate_desc),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerFitFrameRate, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerFitFrameRate =\r\n                        !settingsViewModel.videoPlayerFitFrameRate\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_support_ts_high_profile),\r\n                supportingContent = stringResource(R.string.ui_player_view_support_ts_high_profile_desc),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerSupportTSHighProfile, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerSupportTSHighProfile =\r\n                        !settingsViewModel.videoPlayerSupportTSHighProfile\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_hls_allow_chunkless_preparation),\r\n                supportingContent = stringResource(R.string.ui_player_view_hls_allow_chunkless_preparation_desc),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerHlsAllowChunklessPreparation, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerHlsAllowChunklessPreparation =\r\n                        !settingsViewModel.videoPlayerHlsAllowChunklessPreparation\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_extract_header_from_link),\r\n                supportingContent = stringResource(R.string.ui_player_view_extract_header_from_link_desc),\r\n                trailingContent = {\r\n                    Switch(settingsViewModel.videoPlayerExtractHeaderFromLink, null)\r\n                },\r\n                onSelect = {\r\n                    settingsViewModel.videoPlayerExtractHeaderFromLink =\r\n                        !settingsViewModel.videoPlayerExtractHeaderFromLink\r\n                },\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_display_mode),\r\n                trailingContent = settingsViewModel.videoPlayerDisplayMode.label,\r\n                onSelect = toVideoPlayerDisplayModeScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_load_timeout),\r\n                supportingContent = stringResource(R.string.ui_player_view_load_timeout_desc),\r\n                trailingContent = settingsViewModel.videoPlayerLoadTimeout.humanizeMs(),\r\n                onSelect = toVideoPlayerLoadTimeoutScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_buffer_time),\r\n                supportingContent = stringResource(R.string.ui_player_view_buffer_time_desc),\r\n                trailingContent = settingsViewModel.videoPlayerBufferTime.humanizeBufferNum(),\r\n                onSelect = toVideoPlayerBufferTimeScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_rtsp_transport),\r\n                trailingContent = settingsViewModel.videoPlayerRtspTransport.label,\r\n                onSelect = toVideoPlayerRtspTransportScreen,\r\n                link = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_user_agent),\r\n                trailingContent = settingsViewModel.videoPlayerUserAgent,\r\n                remoteConfig = true,\r\n            )\r\n        }\r\n\r\n        item {\r\n            val isValid = settingsViewModel.videoPlayerHeaders.headersValid()\r\n\r\n            SettingsListItem(\r\n                headlineContent = stringResource(R.string.ui_player_view_custom_headers),\r\n                supportingContent = settingsViewModel.videoPlayerHeaders,\r\n                remoteConfig = true,\r\n                trailingIcon = if (!isValid) Icons.Default.ErrorOutline else null,\r\n            )\r\n        }\r\n\r\n        // item {\r\n        //     SettingsListItem(\r\n        //         headlineContent = stringResource(R.string.ui_player_view_volume_normalization),\r\n        //         supportingContent = stringResource(R.string.ui_player_view_volume_normalization_desc),\r\n        //         trailingContent = {\r\n        //             Switch(settingsViewModel.videoPlayerVolumeNormalization, null)\r\n        //         },\r\n        //         onSelect = {\r\n        //             settingsViewModel.videoPlayerVolumeNormalization =\r\n        //                 !settingsViewModel.videoPlayerVolumeNormalization\r\n        //         },\r\n        //     )\r\n        // }\r\n    }\r\n}\r\n\r\n@Preview(device = \"id:Android TV (720p)\")\r\n@Composable\r\nprivate fun SettingsVideoPlayerScreenPreview() {\r\n    MyTvTheme {\r\n        SettingsVideoPlayerScreen(\r\n            settingsViewModel = SettingsViewModel().apply {\r\n                videoPlayerUserAgent =\r\n                    \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36\"\r\n                videoPlayerHeaders = \"Accept: \"\r\n            }\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/categories/SettingsVideoPlayerScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/categories/SettingsVideoPlayerScreen.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/categories/SettingsVideoPlayerScreen.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/categories/SettingsVideoPlayerScreen.kt	(date 1756691200113)
@@ -27,6 +27,7 @@
     toVideoPlayerRenderModeScreen: () -> Unit = {},
     toVideoPlayerRtspTransportScreen: () -> Unit = {},
     toVideoPlayerDisplayModeScreen: () -> Unit = {},
+    toVideoPlayerPlaybackModeScreen: () -> Unit = {},
     toVideoPlayerLoadTimeoutScreen: () -> Unit = {},
     toVideoPlayerBufferTimeScreen: () -> Unit = {},
     onBackPressed: () -> Unit = {},
@@ -159,6 +160,15 @@
                 link = true,
             )
         }
+
+        item {
+            SettingsListItem(
+                headlineContent = stringResource(R.string.ui_player_view_playback_mode),
+                trailingContent = settingsViewModel.videoPlayerPlaybackMode.label,
+                onSelect = toVideoPlayerPlaybackModeScreen,
+                link = true,
+            )
+        }
 
         item {
             SettingsListItem(
Index: .idea/misc.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"EntryPointsManager\">\r\n    <list size=\"1\">\r\n      <item index=\"0\" class=\"java.lang.String\" itemvalue=\"android.webkit.JavascriptInterface\" />\r\n    </list>\r\n  </component>\r\n  <component name=\"ExternalStorageConfigurationManager\" enabled=\"true\" />\r\n  <component name=\"ProjectRootManager\" version=\"2\" languageLevel=\"JDK_21\" default=\"true\" project-jdk-name=\"jbr-21\" project-jdk-type=\"JavaSDK\">\r\n    <output url=\"file://$PROJECT_DIR$/build/classes\" />\r\n  </component>\r\n  <component name=\"ProjectType\">\r\n    <option name=\"id\" value=\"Android\" />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/misc.xml b/.idea/misc.xml
--- a/.idea/misc.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/.idea/misc.xml	(date 1756691200052)
@@ -1,4 +1,3 @@
-<?xml version="1.0" encoding="UTF-8"?>
 <project version="4">
   <component name="EntryPointsManager">
     <list size="1">
Index: tv/src/main/AndroidManifest.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\"\r\n    xmlns:tools=\"http://schemas.android.com/tools\">\r\n\r\n    <uses-feature\r\n        android:name=\"android.hardware.touchscreen\"\r\n        android:required=\"false\" />\r\n    <uses-feature\r\n        android:name=\"android.software.leanback\"\r\n        android:required=\"false\" />\r\n\r\n    <queries>\r\n        <package android:name=\"com.google.android.webview\" />\r\n    </queries>\r\n\r\n    <uses-permission android:name=\"android.permission.INTERNET\" />\r\n    <uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\" />\r\n    <uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\" />\r\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />\r\n    <uses-permission android:name=\"android.permission.WAKE_LOCK\"/>\r\n    <uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />  <!-- X5权限需求 -->\r\n    <uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" /> <!-- X5权限需求 -->\r\n    <uses-permission android:name=\"android.permission.GET_TASKS\"/><!-- X5权限需求 -->\r\n    <uses-permission android:name=\"android.permission.READ_PHONE_STATE\" /><!-- X5权限需求 -->\r\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" /><!-- X5权限需求 -->\r\n    <uses-permission\r\n        android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\"\r\n        tools:ignore=\"ScopedStorage\" />\r\n\r\n    <application\r\n        android:name=\".MyTVApplication\"\r\n        android:allowBackup=\"true\"\r\n        android:banner=\"@drawable/ic_banner\"\r\n        android:icon=\"@mipmap/ic_launcher\"\r\n        android:label=\"@string/app_name\"\r\n        android:networkSecurityConfig=\"@xml/network_security_config\"\r\n        android:requestLegacyExternalStorage=\"true\"\r\n        android:supportsRtl=\"true\"\r\n        android:theme=\"@style/Theme.MyTV\"\r\n        android:usesCleartextTraffic=\"true\"\r\n        android:hardwareAccelerated=\"true\" \r\n        android:largeHeap=\"true\"\r\n        android:directBootAware=\"false\"\r\n        tools:targetApi=\"n\"\r\n        android:extractNativeLibs=\"true\">\r\n        <property android:name=\"android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY\" android:value=\"true\" />\r\n        <activity\r\n            android:name=\".MainActivity\"\r\n            android:configChanges=\"screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden\"\r\n            android:exported=\"true\"\r\n            android:resizeableActivity=\"true\"\r\n            android:screenOrientation=\"sensorLandscape\"\r\n            android:supportsPictureInPicture=\"true\"\r\n            tools:ignore=\"DiscouragedApi\">\r\n            <intent-filter>\r\n                <action android:name=\"android.intent.action.MAIN\" />\r\n                <category android:name=\"android.intent.category.LAUNCHER\" />\r\n                <category android:name=\"android.intent.category.LEANBACK_LAUNCHER\" />\r\n            </intent-filter>\r\n        </activity>\r\n\r\n        <activity android:name=\".CrashHandlerActivity\" />\r\n        <receiver\r\n            android:name=\".BootReceiver\"\r\n            android:enabled=\"true\"\r\n            android:exported=\"false\"\r\n            android:permission=\"android.permission.RECEIVE_BOOT_COMPLETED\">\r\n            <intent-filter>\r\n                <action android:name=\"android.intent.action.BOOT_COMPLETED\" />\r\n                <category android:name=\"android.intent.category.DEFAULT\" />\r\n            </intent-filter>\r\n        </receiver>\r\n\r\n        <service android:name=\".HttpServerService\" />\r\n        <service \r\n            android:name=\".X5CorePreLoadService\"\r\n            android:permission=\"android.permission.BIND_JOB_SERVICE\"\r\n            android:enabled=\"true\"\r\n            android:exported=\"false\" />\r\n        <service\r\n            android:name=\"androidx.appcompat.app.AppLocalesMetadataHolderService\"\r\n            android:enabled=\"false\"\r\n            android:exported=\"false\">\r\n            <meta-data\r\n            android:name=\"autoStoreLocales\"\r\n            android:value=\"true\" />\r\n        </service>\r\n\r\n        <provider\r\n            android:name=\"androidx.core.content.FileProvider\"\r\n            android:authorities=\"${applicationId}.FileProvider\"\r\n            android:exported=\"false\"\r\n            android:grantUriPermissions=\"true\">\r\n            <meta-data\r\n                android:name=\"android.support.FILE_PROVIDER_PATHS\"\r\n                android:resource=\"@xml/file_paths\" />\r\n        </provider>\r\n\r\n        <meta-data\r\n            android:name=\"io.sentry.auto-init\"\r\n            android:value=\"false\" />\r\n\r\n    </application>\r\n\r\n</manifest>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/AndroidManifest.xml b/tv/src/main/AndroidManifest.xml
--- a/tv/src/main/AndroidManifest.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/AndroidManifest.xml	(date 1756691200098)
@@ -96,9 +96,9 @@
                 android:resource="@xml/file_paths" />
         </provider>
 
-        <meta-data
-            android:name="io.sentry.auto-init"
-            android:value="false" />
+        <!-- <meta-data -->
+        <!--     android:name="io.sentry.auto-init" -->
+        <!--     android:value="false" /> -->
 
     </application>
 
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/VideoPlayerControllerScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller\r\n\r\nimport androidx.annotation.IntRange\r\nimport androidx.compose.foundation.layout.Arrangement\r\nimport androidx.compose.foundation.layout.Row\r\nimport androidx.compose.foundation.layout.padding\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.LaunchedEffect\r\nimport androidx.compose.runtime.Stable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Alignment\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.compose.ui.unit.dp\r\nimport androidx.tv.material3.Text\r\nimport top.yogiczy.mytv.tv.ui.material.Drawer\r\nimport top.yogiczy.mytv.tv.ui.material.DrawerPosition\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components.VideoPlayerControllerPositionCtrl\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components.VideoPlayerControllerStateCtrl\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids\r\nimport top.yogiczy.mytv.tv.ui.utils.backHandler\r\nimport top.yogiczy.mytv.tv.ui.utils.focusOnLaunchedSaveable\r\nimport top.yogiczy.mytv.tv.ui.utils.handleKeyEvents\r\nimport top.yogiczy.mytv.tv.R\r\nimport kotlinx.coroutines.FlowPreview\r\nimport kotlinx.coroutines.channels.Channel\r\nimport kotlinx.coroutines.flow.consumeAsFlow\r\nimport kotlinx.coroutines.flow.debounce\r\nimport kotlin.math.max\r\nimport kotlin.math.min\r\nimport androidx.compose.ui.res.stringResource\r\n\r\n@Composable\r\nfun VideoPlayerControllerScreen(\r\n    modifier: Modifier = Modifier,\r\n    isVideoPlayerPlayingProvider: () -> Boolean = { false },\r\n    isVideoPlayerBufferingProvider: () -> Boolean = { false },\r\n    videoPlayerCurrentPositionProvider: () -> Long = { 0L },\r\n    videoPlayerDurationProvider: () -> Pair<Long, Long> = { 0L to 0L },\r\n    onVideoPlayerPlay: () -> Unit = {},\r\n    onVideoPlayerPause: () -> Unit = {},\r\n    onVideoPlayerSeekTo: (Long) -> Unit = {},\r\n    onClose: () -> Unit = {},\r\n) {\r\n    var seekToPosition by remember { mutableStateOf<Long?>(null) }\r\n\r\n    val debounce = rememberDebounce(\r\n        wait = 1000L,\r\n        func = {\r\n            seekToPosition?.let { nnSeekToPosition ->\r\n                val startPosition = videoPlayerDurationProvider().first\r\n                onVideoPlayerSeekTo(nnSeekToPosition - startPosition)\r\n                seekToPosition = null\r\n            }\r\n        },\r\n    )\r\n    LaunchedEffect(seekToPosition) {\r\n        if (seekToPosition != null) debounce.active()\r\n    }\r\n\r\n    fun seekForward(ms: Long) {\r\n        val currentPosition = videoPlayerCurrentPositionProvider()\r\n        val startPosition = videoPlayerDurationProvider().first\r\n        seekToPosition = max(startPosition, (seekToPosition ?: currentPosition) - ms)\r\n    }\r\n\r\n    fun seekNext(ms: Long) {\r\n        val currentPosition = videoPlayerCurrentPositionProvider()\r\n        val endPosition = videoPlayerDurationProvider().second\r\n        seekToPosition = min(\r\n            if (endPosition <= 0L) Long.MAX_VALUE else min(endPosition, System.currentTimeMillis()),\r\n            (seekToPosition ?: currentPosition) + ms\r\n        )\r\n    }\r\n\r\n    Drawer(\r\n        modifier = modifier.backHandler { onClose() }\r\n            .handleKeyEvents(\r\n                onUp = { seekForward(1000L * 10) },\r\n                onLongUp = { seekForward(1000L * 60) },\r\n                onDown = { seekNext(1000L * 10) },\r\n                onLongDown = { seekNext(1000L * 60) }\r\n            ),\r\n        onDismissRequest = onClose,\r\n        position = DrawerPosition.Bottom,\r\n        header = { Text(stringResource(R.string.ui_channel_view_playback_control)) },\r\n    ) {\r\n        Row(\r\n            modifier = Modifier.padding(top = 10.dp),\r\n            horizontalArrangement = Arrangement.spacedBy(20.dp),\r\n            verticalAlignment = Alignment.CenterVertically,\r\n        ) {\r\n            VideoPlayerControllerStateCtrl(\r\n                modifier = Modifier.focusOnLaunchedSaveable(),\r\n                isPlayingProvider = isVideoPlayerPlayingProvider,\r\n                isBufferingProvider = isVideoPlayerBufferingProvider,\r\n                onPlay = onVideoPlayerPlay,\r\n                onPause = onVideoPlayerPause,\r\n            )\r\n\r\n            VideoPlayerControllerPositionCtrl(\r\n                currentPositionProvider = videoPlayerCurrentPositionProvider,\r\n                durationProvider = videoPlayerDurationProvider,\r\n                seekToPositionProvider = { seekToPosition},\r\n                seekForward = ::seekForward,\r\n                seekNext = ::seekNext,\r\n            )\r\n        }\r\n    }\r\n}\r\n\r\n@Stable\r\nclass Debounce internal constructor(\r\n    @param:IntRange(from = 0) private val wait: Long,\r\n    private val func: () -> Unit = {},\r\n) {\r\n    fun active() {\r\n        channel.trySend(wait)\r\n    }\r\n\r\n    private val channel = Channel<Long>(Channel.CONFLATED)\r\n\r\n    @OptIn(FlowPreview::class)\r\n    suspend fun observe() {\r\n        channel.consumeAsFlow().debounce { it }.collect {\r\n            func()\r\n        }\r\n    }\r\n}\r\n\r\n@Composable\r\nfun rememberDebounce(\r\n    @IntRange(from = 0) wait: Long,\r\n    func: () -> Unit = {},\r\n) = remember { Debounce(wait = wait, func = func) }.also {\r\n    LaunchedEffect(it) { it.observe() }\r\n}\r\n\r\n@Preview(device = \"id:Android TV (720p)\")\r\n@Composable\r\nprivate fun VideoPlayerControllerScreenPreview() {\r\n    MyTvTheme {\r\n        PreviewWithLayoutGrids {\r\n            VideoPlayerControllerScreen(\r\n                videoPlayerCurrentPositionProvider = { System.currentTimeMillis() },\r\n                videoPlayerDurationProvider = {\r\n                    System.currentTimeMillis() - 1000L * 60 * 60 to System.currentTimeMillis() + 1000L * 60 * 60\r\n                },\r\n            )\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/VideoPlayerControllerScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/VideoPlayerControllerScreen.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/VideoPlayerControllerScreen.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayercontroller/VideoPlayerControllerScreen.kt	(date 1756691200122)
@@ -33,14 +33,14 @@
 import kotlin.math.max
 import kotlin.math.min
 import androidx.compose.ui.res.stringResource
+import java.util.Calendar
 
 @Composable
 fun VideoPlayerControllerScreen(
     modifier: Modifier = Modifier,
     isVideoPlayerPlayingProvider: () -> Boolean = { false },
     isVideoPlayerBufferingProvider: () -> Boolean = { false },
-    videoPlayerCurrentPositionProvider: () -> Long = { 0L },
-    videoPlayerDurationProvider: () -> Pair<Long, Long> = { 0L to 0L },
+    videoTimelineProvider: () -> Triple<Long, Long, Long> = { Triple(0L, 0L, 0L) }, // (start, end, current)
     onVideoPlayerPlay: () -> Unit = {},
     onVideoPlayerPause: () -> Unit = {},
     onVideoPlayerSeekTo: (Long) -> Unit = {},
@@ -52,7 +52,7 @@
         wait = 1000L,
         func = {
             seekToPosition?.let { nnSeekToPosition ->
-                val startPosition = videoPlayerDurationProvider().first
+                val startPosition = videoTimelineProvider().first
                 onVideoPlayerSeekTo(nnSeekToPosition - startPosition)
                 seekToPosition = null
             }
@@ -63,17 +63,18 @@
     }
 
     fun seekForward(ms: Long) {
-        val currentPosition = videoPlayerCurrentPositionProvider()
-        val startPosition = videoPlayerDurationProvider().first
+        val (startPosition, _, currentPosition) = videoTimelineProvider()
         seekToPosition = max(startPosition, (seekToPosition ?: currentPosition) - ms)
     }
 
+
+
     fun seekNext(ms: Long) {
-        val currentPosition = videoPlayerCurrentPositionProvider()
-        val endPosition = videoPlayerDurationProvider().second
+        val (startPosition, endPositionRaw, currentPosition) = videoTimelineProvider()
+        val endPosition = if (endPositionRaw <= 0L) Long.MAX_VALUE else min(endPositionRaw, System.currentTimeMillis())
         seekToPosition = min(
-            if (endPosition <= 0L) Long.MAX_VALUE else min(endPosition, System.currentTimeMillis()),
-            (seekToPosition ?: currentPosition) + ms
+            endPosition,
+            max(startPosition, (seekToPosition ?: currentPosition) + ms)
         )
     }
 
@@ -103,8 +104,7 @@
             )
 
             VideoPlayerControllerPositionCtrl(
-                currentPositionProvider = videoPlayerCurrentPositionProvider,
-                durationProvider = videoPlayerDurationProvider,
+                timelineProvider = videoTimelineProvider,
                 seekToPositionProvider = { seekToPosition},
                 seekForward = ::seekForward,
                 seekNext = ::seekNext,
@@ -146,10 +146,10 @@
     MyTvTheme {
         PreviewWithLayoutGrids {
             VideoPlayerControllerScreen(
-                videoPlayerCurrentPositionProvider = { System.currentTimeMillis() },
-                videoPlayerDurationProvider = {
-                    System.currentTimeMillis() - 1000L * 60 * 60 to System.currentTimeMillis() + 1000L * 60 * 60
-                },
+                videoTimelineProvider = {
+                    val now = System.currentTimeMillis()
+                    Triple(now - 1000L * 60 * 60, now + 1000L * 60 * 60, now)
+                }
             )
         }
     }
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/crash/CrashHandlerScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.crash\r\n\r\nimport android.content.ClipData\r\nimport android.content.ClipboardManager\r\nimport android.content.Context\r\nimport android.widget.Toast\r\nimport androidx.compose.foundation.layout.Arrangement\r\nimport androidx.compose.foundation.layout.Row\r\nimport androidx.compose.foundation.layout.fillMaxWidth\r\nimport androidx.compose.foundation.layout.padding\r\nimport androidx.compose.foundation.lazy.LazyColumn\r\nimport androidx.compose.material.icons.Icons\r\nimport androidx.compose.material.icons.filled.ContentCopy\r\nimport androidx.compose.material.icons.filled.RestartAlt\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.platform.LocalContext\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.compose.ui.unit.dp\r\nimport androidx.tv.material3.Text\r\nimport io.sentry.Sentry\r\nimport top.yogiczy.mytv.tv.ui.rememberChildPadding\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppScaffoldHeaderBtn\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppScreen\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport top.yogiczy.mytv.tv.ui.utils.focusOnLaunched\r\nimport java.text.SimpleDateFormat\r\nimport java.util.Locale\r\nimport top.yogiczy.mytv.tv.R\r\nimport androidx.compose.ui.res.stringResource\r\n@Composable\r\nfun CrashHandlerScreen(\r\n    modifier: Modifier = Modifier,\r\n    errorMessage: String,\r\n    errorStacktrace: String = \"\",\r\n    onRestart: () -> Unit = {},\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    val childPaddings = rememberChildPadding()\r\n    val context = LocalContext.current\r\n    \r\n    // 完整崩溃日志文本\r\n    val fullCrashLog = buildString {\r\n        append(\"崩溃时间：${SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\", Locale.getDefault()).format(System.currentTimeMillis())}\\n\\n\")\r\n        append(\"错误信息：\\n$errorMessage\\n\\n\")\r\n        append(\"堆栈跟踪：\\n$errorStacktrace\")\r\n    }\r\n\r\n    AppScreen(\r\n        modifier = modifier.padding(childPaddings.copy(top = 10.dp).paddingValues),\r\n        header = { Text(text = stringResource(R.string.ui_crash_handler_app_crashed)) },\r\n        headerExtra = {\r\n            Row(\r\n                modifier = Modifier.fillMaxWidth(),\r\n                horizontalArrangement = Arrangement.spacedBy(16.dp)\r\n            ) {\r\n                val crashLogLabel = stringResource(R.string.ui_crash_handler_crash_log)\r\n                val crashLogCopiedMsg = stringResource(R.string.ui_crash_handler_crash_log_copied)\r\n                AppScaffoldHeaderBtn(\r\n                    title = stringResource(R.string.ui_crash_handler_copy_log),\r\n                    imageVector = Icons.Default.ContentCopy,\r\n                    onSelect = {\r\n                        // 复制崩溃日志到剪贴板\r\n                        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager\r\n                        if (clipboardManager != null) {\r\n                            val clipData = ClipData.newPlainText(crashLogLabel, fullCrashLog)\r\n                            clipboardManager.setPrimaryClip(clipData)\r\n                            Toast.makeText(context, crashLogCopiedMsg, Toast.LENGTH_SHORT).show()\r\n                        }\r\n                        else {\r\n                            Toast.makeText(context, R.string.ui_error_clipboard_service_unavailable, Toast.LENGTH_SHORT).show()\r\n                        }\r\n                    },\r\n                )\r\n                AppScaffoldHeaderBtn(\r\n                    modifier = Modifier.focusOnLaunched(),\r\n                    title = stringResource(R.string.ui_crash_handler_restart),\r\n                    imageVector = Icons.Default.RestartAlt,\r\n                    onSelect = onRestart,\r\n                )\r\n            }\r\n        },\r\n        onBackPressed = onBackPressed,\r\n    ) {\r\n        LazyColumn {\r\n            @Suppress(\"UnstableApiUsage\")\r\n            Sentry.withScope {\r\n                it.options.distinctId?.let { distinctId ->\r\n                    item { Text(text = \"设备ID: $distinctId\") }\r\n                }\r\n            }\r\n\r\n            item {\r\n                val timeFormat = SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\", Locale.getDefault())\r\n                Text(text = \"崩溃时间：${timeFormat.format(System.currentTimeMillis())}\")\r\n            }\r\n\r\n            item { Text(\"错误信息：\") }\r\n            item { Text(errorMessage) }\r\n            \r\n            item { Text(\"堆栈跟踪：\") }\r\n            item { Text(errorStacktrace) }\r\n            \r\n            item { \r\n                Text(\r\n                    text = stringResource(R.string.ui_crash_handler_tip),\r\n                    modifier = Modifier.padding(top = 16.dp)\r\n                ) \r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@Preview(device = \"id:Android TV (720p)\")\r\n@Composable\r\nprivate fun CrashHandlerScreenPreview() {\r\n    MyTvTheme {\r\n        CrashHandlerScreen(\r\n            errorMessage = \"ChannelsChannelItem should not be used directly\",\r\n            errorStacktrace = \"\"\"\r\n                java.lang.IllegalStateException: ChannelsChannelItem should not be used directly\r\n            \"\"\".trimIndent().repeat(100),\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/crash/CrashHandlerScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/crash/CrashHandlerScreen.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/crash/CrashHandlerScreen.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/crash/CrashHandlerScreen.kt	(date 1756691200102)
@@ -18,7 +18,6 @@
 import androidx.compose.ui.tooling.preview.Preview
 import androidx.compose.ui.unit.dp
 import androidx.tv.material3.Text
-import io.sentry.Sentry
 import top.yogiczy.mytv.tv.ui.rememberChildPadding
 import top.yogiczy.mytv.tv.ui.screen.components.AppScaffoldHeaderBtn
 import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
@@ -83,13 +82,6 @@
         onBackPressed = onBackPressed,
     ) {
         LazyColumn {
-            @Suppress("UnstableApiUsage")
-            Sentry.withScope {
-                it.options.distinctId?.let { distinctId ->
-                    item { Text(text = "设备ID: $distinctId") }
-                }
-            }
-
             item {
                 val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                 Text(text = "崩溃时间：${timeFormat.format(System.currentTimeMillis())}")
@@ -105,7 +97,7 @@
                 Text(
                     text = stringResource(R.string.ui_crash_handler_tip),
                     modifier = Modifier.padding(top = 16.dp)
-                ) 
+                )
             }
         }
     }
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/VideoPlayerState.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.videoplayer\r\n\r\nimport android.view.SurfaceView\r\nimport android.view.TextureView\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.DisposableEffect\r\nimport androidx.compose.runtime.Stable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableFloatStateOf\r\nimport androidx.compose.runtime.mutableLongStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.rememberCoroutineScope\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.platform.LocalContext\r\nimport androidx.lifecycle.Lifecycle\r\nimport androidx.lifecycle.LifecycleEventObserver\r\nimport androidx.lifecycle.compose.LocalLifecycleOwner\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.IjkVideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.Media3VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VLCVideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\n\r\n@Stable\r\nclass VideoPlayerState(\r\n    val instance: VideoPlayer,\r\n    private var defaultDisplayModeProvider: () -> VideoPlayerDisplayMode = { VideoPlayerDisplayMode.ORIGINAL },\r\n) {\r\n    /** 显示模式 */\r\n    var displayMode by mutableStateOf(defaultDisplayModeProvider())\r\n\r\n    /** 视频宽高比 */\r\n    var aspectRatio by mutableFloatStateOf(16f / 9f)\r\n\r\n    /** 错误 */\r\n    var error by mutableStateOf<String?>(null)\r\n\r\n    /** 正在缓冲 */\r\n    var isBuffering by mutableStateOf(false)\r\n\r\n    /** 正在播放 */\r\n    var isPlaying by mutableStateOf(false)\r\n\r\n    /** 总时长 */\r\n    var duration by mutableLongStateOf(0L)\r\n\r\n    /** 当前播放位置 */\r\n    var currentPosition by mutableLongStateOf(0L)\r\n\r\n    /** 当前音量 */\r\n    private var _volume by mutableFloatStateOf(1f)\r\n    var volume: Float\r\n        get() = _volume\r\n        set(value) {\r\n            _volume = value\r\n            instance.setVolume(value)\r\n        }\r\n\r\n    /** 元数据 */\r\n    var metadata by mutableStateOf(VideoPlayer.Metadata())\r\n\r\n    fun prepare(line: ChannelLine) {\r\n        error = null\r\n        metadata = VideoPlayer.Metadata()\r\n        instance.prepare(line)\r\n    }\r\n\r\n    fun play() {\r\n        instance.play()\r\n    }\r\n\r\n    fun pause() {\r\n        instance.pause()\r\n    }\r\n\r\n    fun seekTo(position: Long) {\r\n        instance.seekTo(position)\r\n    }\r\n\r\n    fun stop() {\r\n        instance.stop()\r\n    }\r\n\r\n    fun selectVideoTrack(track: VideoPlayer.Metadata.Video?) {\r\n        instance.selectVideoTrack(track)\r\n    }\r\n\r\n    fun selectAudioTrack(track: VideoPlayer.Metadata.Audio?) {\r\n        instance.selectAudioTrack(track)\r\n    }\r\n\r\n    fun selectSubtitleTrack(track: VideoPlayer.Metadata.Subtitle?) {\r\n        instance.selectSubtitleTrack(track)\r\n    }\r\n\r\n    fun setVideoView(surfaceView: SurfaceView) {\r\n        instance.setVideoSurfaceView(surfaceView)\r\n    }\r\n\r\n    fun setVideoView(textureView: TextureView) {\r\n        instance.setVideoTextureView(textureView)\r\n    }\r\n\r\n    fun setSubtitleView(surfaceView: SurfaceView) {\r\n        instance.setSubtitleSurfaceView(surfaceView)\r\n    }\r\n\r\n    fun setSubtitleView(textureView: TextureView) {\r\n        instance.setSubtitleTextureView(textureView)\r\n    }\r\n\r\n    private val onReadyListeners = mutableListOf<() -> Unit>()\r\n    private val onErrorListeners = mutableListOf<() -> Unit>()\r\n    private val onInterruptListeners = mutableListOf<() -> Unit>()\r\n    private val onIsBufferingListeners = mutableListOf<(Boolean) -> Unit>()\r\n\r\n    fun onReady(listener: () -> Unit) {\r\n        onReadyListeners.add(listener)\r\n    }\r\n\r\n    fun onError(listener: () -> Unit) {\r\n        onErrorListeners.add(listener)\r\n    }\r\n\r\n    fun onInterrupt(listener: () -> Unit) {\r\n        onInterruptListeners.add(listener)\r\n    }\r\n\r\n    fun onIsBuffering(listener: (Boolean) -> Unit) {\r\n        onIsBufferingListeners.add(listener)\r\n    }\r\n\r\n    fun initialize() {\r\n        instance.initialize()\r\n        instance.onResolution { width, height ->\r\n            if (width > 0 && height > 0) aspectRatio = width.toFloat() / height\r\n        }\r\n        instance.onError { ex ->\r\n            error = ex?.let { \"${it.errorCodeName}(${it.errorCode})\" }\r\n                ?.apply { onErrorListeners.forEach { it.invoke() } }\r\n\r\n        }\r\n        instance.onReady {\r\n            onReadyListeners.forEach { it.invoke() }\r\n            error = null\r\n            displayMode = defaultDisplayModeProvider()\r\n        }\r\n        instance.onBuffering {\r\n            isBuffering = it\r\n            if (it) error = null\r\n            onIsBufferingListeners.forEach { cb -> cb(isBuffering) }\r\n        }\r\n        instance.onPrepared { }\r\n        instance.onIsPlayingChanged { isPlaying = it }\r\n        instance.onDurationChanged { duration = it }\r\n        instance.onCurrentPositionChanged { currentPosition = it }\r\n        instance.onMetadata { metadata = it }\r\n        instance.onInterrupt { onInterruptListeners.forEach { it.invoke() } }\r\n    }\r\n\r\n    fun release() {\r\n        onReadyListeners.clear()\r\n        onErrorListeners.clear()\r\n        onInterruptListeners.clear()\r\n        onIsBufferingListeners.clear()\r\n        instance.release()\r\n    }\r\n}\r\n\r\n@Composable\r\nfun rememberVideoPlayerState(\r\n    defaultDisplayModeProvider: () -> VideoPlayerDisplayMode = { VideoPlayerDisplayMode.ORIGINAL },\r\n): VideoPlayerState {\r\n    val context = LocalContext.current\r\n    val lifecycleOwner = LocalLifecycleOwner.current\r\n    val coroutineScope = rememberCoroutineScope()\r\n\r\n    val videoPlayerCore = settingsVM.videoPlayerCore\r\n    val forceSoftDecode = settingsVM.videoPlayerForceSoftDecode \r\n    val state = remember(videoPlayerCore, forceSoftDecode) {\r\n        val player = when (videoPlayerCore) {\r\n            Configs.VideoPlayerCore.MEDIA3 -> Media3VideoPlayer(context, coroutineScope)\r\n            Configs.VideoPlayerCore.IJK -> IjkVideoPlayer(context, coroutineScope)\r\n            Configs.VideoPlayerCore.VLC -> VLCVideoPlayer(context, coroutineScope)\r\n        }\r\n\r\n        VideoPlayerState(player, defaultDisplayModeProvider)\r\n    }\r\n\r\n    DisposableEffect(videoPlayerCore, forceSoftDecode) {\r\n        state.initialize()\r\n        onDispose { state.release() }\r\n    }\r\n\r\n    DisposableEffect(lifecycleOwner, videoPlayerCore, forceSoftDecode) {\r\n        val observer = LifecycleEventObserver { _, event ->\r\n            if (event == Lifecycle.Event.ON_RESUME) state.play()\r\n            else if (event == Lifecycle.Event.ON_STOP) {\r\n                if (!Configs.appPipEnable) state.pause()\r\n            }\r\n        }\r\n\r\n        lifecycleOwner.lifecycle.addObserver(observer)\r\n        onDispose { lifecycleOwner.lifecycle.removeObserver(observer) }\r\n    }\r\n    return state\r\n}\r\n\r\nenum class VideoPlayerDisplayMode(val value: Int, val label: String) {\r\n    /** 原始 */\r\n    ORIGINAL(0, \"原始\"),\r\n\r\n    /** 填充 */\r\n    FILL(1, \"填充\"),\r\n\r\n    /** 裁剪 */\r\n    CROP(2, \"裁剪\"),\r\n\r\n    /** 4:3 */\r\n    FOUR_THREE(3, \"4:3\"),\r\n\r\n    /** 16:9 */\r\n    SIXTEEN_NINE(4, \"16:9\"),\r\n\r\n    /** 2.35:1 */\r\n    WIDE(5, \"2.35:1\");\r\n\r\n    companion object {\r\n        fun fromValue(value: Int): VideoPlayerDisplayMode {\r\n            return entries.firstOrNull { it.value == value } ?: ORIGINAL\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/VideoPlayerState.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/VideoPlayerState.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/VideoPlayerState.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/VideoPlayerState.kt	(date 1756691200119)
@@ -233,4 +233,18 @@
             return entries.firstOrNull { it.value == value } ?: ORIGINAL
         }
     }
+}
+
+enum class VideoPlayerPlaybackMode(val value: Int, val label: String) {
+    /** 重载URL跳转 */
+    RELOAD_URL(0, "重载URL跳转"),
+
+    /** 播放器seekTo跳转 */
+    SEEK_TO(1, "播放器seekTo跳转");
+
+    companion object {
+        fun fromValue(value: Int): VideoPlayerPlaybackMode {
+            return entries.firstOrNull { it.value == value } ?: RELOAD_URL
+        }
+    }
 }
\ No newline at end of file
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/IjkVideoPlayer.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player\r\n\r\nimport android.content.Context\r\nimport android.graphics.SurfaceTexture\r\nimport android.view.Surface\r\nimport android.view.SurfaceView\r\nimport android.view.TextureView\r\nimport android.view.TextureView.SurfaceTextureListener\r\nimport androidx.media3.common.text.Cue\r\nimport com.google.common.collect.ImmutableList\r\nimport kotlinx.coroutines.CoroutineScope\r\nimport kotlinx.coroutines.Job\r\nimport kotlinx.coroutines.delay\r\nimport kotlinx.coroutines.launch\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.util.utils.toHeaders\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport tv.danmaku.ijk.media.player.misc.IjkTrackInfo\r\nimport tv.danmaku.ijk.media.player.IMediaPlayer\r\nimport tv.danmaku.ijk.media.player.IjkMediaMeta\r\nimport tv.danmaku.ijk.media.player.IjkMediaPlayer\r\nimport tv.danmaku.ijk.media.player.IjkTimedText\r\nimport top.yogiczy.mytv.core.data.utils.Logger\r\nimport top.yogiczy.mytv.core.data.utils.Loggable\r\nimport kotlin.math.max\r\nimport kotlin.text.Regex\r\n\r\nclass IjkVideoPlayer(\r\n    private val context: Context,\r\n    private val coroutineScope: CoroutineScope,\r\n) : VideoPlayer(coroutineScope),\r\n    IMediaPlayer.OnPreparedListener,\r\n    IMediaPlayer.OnVideoSizeChangedListener,\r\n    IMediaPlayer.OnErrorListener,\r\n    IMediaPlayer.OnTimedTextListener,\r\n    IMediaPlayer.OnSeekCompleteListener {\r\n\r\n    private val logger = Logger.create(\"IjkVideoPlayer\")\r\n    private val player by lazy {\r\n        IjkMediaPlayer().apply{\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"dns_cache_clear\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"dns_cache_timeout\", 0)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"http-detect-range-support\", 0)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"reconnect\", 2)\r\n            setOption(\r\n                IjkMediaPlayer.OPT_CATEGORY_FORMAT,\r\n                \"timeout\",\r\n                Configs.videoPlayerLoadTimeout\r\n            )\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"enable-accurate-seek\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"analyzemaxduration\", 500L)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"analyzeduration\", 100L)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"probesize\", 1024 * 10)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"seek2any\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"fflags\", \"fastseek\")\r\n        }\r\n    }\r\n    private var cacheSurfaceView: SurfaceView? = null\r\n    private var cacheSurfaceTexture: Surface? = null\r\n    private var updateJob: Job? = null\r\n\r\n    private fun setOption(userAgent: String = \"okhttp\") {\r\n        player.apply {\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"allowed_extensions\", \"ALL\")\r\n            if (Configs.videoPlayerForceSoftDecode)\r\n                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"mediacodec\", 0)\r\n            else{\r\n                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"mediacodec\", 1)\r\n                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"mediacodec-all-videos\", 1)\r\n                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"mediacodec-hevc\", 1)\r\n                setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"mediacodec-handle-resolution-change\", 1)\r\n            }\r\n            // setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"protocol_whitelist\", \"crypto,file,dash,http,https,rtp,tcp,tls,udp,rtmp,rtsp,data\")\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"opensles\", 0)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"framedrop\", 5)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"fast\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"start-on-prepared\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"enable-accurate-seek\", 1)\r\n            \r\n            // rtsp设置 https://ffmpeg.org/ffmpeg-protocols.html#rtsp\r\n            val transport = if (Configs.videoPlayerRtspTransport == Configs.RtspTransport.TCP) \"tcp\" else \"udp\"\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"rtsp_transport\", transport)\r\n            // setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"buffer_size\", 1316 * 512)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"max-buffer-size\", getAvailableMemory(context).toLong())\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"infbuf\", 1)  // 无限读\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"flush_packets\", 1L)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"min-frames\", \r\n                    max(25L, (Configs.videoPlayerBufferTime.toLong() * 0.03).toLong())\r\n            )\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"start-on-prepared\", 1)\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, \"user_agent\", userAgent)\r\n            //  关闭播放器缓冲，这个必须关闭，否则会出现播放一段时间后，一直卡住，控制台打印 FFP_MSG_BUFFERING_START\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"packet-buffering\", 0)\r\n\r\n            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, \"subtitle\", 1)\r\n            \r\n            //https://www.cnblogs.com/Fitz/p/18537127\r\n            // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, \"skip_loop_filter\",0) //丢弃一些“无用”的数据包，例如AVI格式中的零大小数据包\r\n            // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, \"skip_frame\", 0) //不跳帧，解码所有帧\r\n        }\r\n    }\r\n\r\n    override fun prepare(line: ChannelLine) {\r\n        super.prepare(line)\r\n        if (Configs.videoPlayerStopPreviousMediaItem)\r\n            player.stop()\r\n        coroutineScope.launch {\r\n            val playData = getPlayableData()\r\n            if (playData == null)\r\n            {\r\n                triggerError(PlaybackException(\"No playable data found for line: ${line.name}\", 10087))\r\n                logger.e(\"No playable data found for line: ${line.name}\")\r\n                return@launch\r\n            }\r\n            var uri = playData.url\r\n            var header: Map<String, String> = playData.headers ?: emptyMap()\r\n            var headers = Configs.videoPlayerHeaders.toHeaders() + mapOf(\r\n                \"Referer\" to (line.httpReferrer ?: \"\"),\r\n                \"Origin\" to (line.httpOrigin ?: \"\"),\r\n                \"Cookie\" to (line.httpCookie ?: \"\"),\r\n            ).filterValues { it.isNotEmpty() } + header\r\n            \r\n            val userAgent = headers[\"User-Agent\"] ?: headers[\"user-agent\"] ?: line.httpUserAgent ?: Configs.videoPlayerUserAgent\r\n            headers = headers - \"User-Agent\" - \"user-agent\" \r\n            // 使用应用内日志系统\r\n            logger.i(\"播放地址: ${uri.toString()}\")\r\n            logger.i(\"请求头: $headers\")\r\n            logger.i(\"User-Agent: $userAgent\")\r\n            player.reset()\r\n            player.setDataSource(\r\n                uri,\r\n                headers\r\n            )\r\n            setOption(userAgent)\r\n            player.prepareAsync()\r\n            triggerPrepared()\r\n        }\r\n    }\r\n\r\n    override fun play() {\r\n        player.start()\r\n    }\r\n\r\n    override fun pause() {\r\n        player.pause()\r\n    }\r\n\r\n    override fun seekTo(position: Long) {\r\n        player.seekTo(position)\r\n    }\r\n\r\n    override fun setVolume(volume: Float) {\r\n    }\r\n\r\n    override fun getVolume(): Float {\r\n        return 1f\r\n    }\r\n\r\n    override fun stop() {\r\n        player.stop()\r\n        updateJob?.cancel()\r\n        super.stop()\r\n    }\r\n\r\n    override fun selectVideoTrack(track: Metadata.Video?) {\r\n        if (track?.index == null)\r\n            metadata.video?.index?.let { player.deselectTrack(it) }\r\n        else\r\n            player.selectTrack(track.index)\r\n        updateVideoInfo(player)\r\n        if(track != null) {\r\n            metadata = metadata.copy(\r\n                video = track.copy(\r\n                    decoder = player.mediaInfo.mVideoDecoderImpl,\r\n                ),\r\n            )\r\n        } else {\r\n            metadata = metadata.copy(video = null)\r\n        }\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    override fun selectAudioTrack(track: Metadata.Audio?) {\r\n        if (track?.index == null)\r\n            metadata.audio?.index?.let { player.deselectTrack(it) }\r\n        else\r\n            player.selectTrack(track.index)\r\n        updateAudioInfo(player)\r\n        \r\n        if(track != null) {\r\n            metadata = metadata.copy(\r\n                audio = track.copy(\r\n                    decoder = player.mediaInfo.mAudioDecoderImpl,\r\n                ),\r\n            )\r\n        } else {\r\n            metadata = metadata.copy(audio = null)\r\n        }\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {\r\n        if (track?.index == null)\r\n            metadata.subtitle?.index?.let { player.deselectTrack(it) }\r\n        else\r\n            player.selectTrack(track.index)\r\n        updateSubtitleInfo(player)\r\n        metadata = metadata.copy(\r\n            subtitle = track,\r\n        )\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    override fun setVideoSurfaceView(surfaceView: SurfaceView) {\r\n        cacheSurfaceView = surfaceView\r\n        cacheSurfaceTexture?.release()\r\n        cacheSurfaceTexture = null\r\n    }\r\n\r\n    override fun setSubtitleSurfaceView(surfaceView: SurfaceView) {\r\n    }\r\n\r\n    override fun setSubtitleTextureView(textureView: TextureView) {\r\n    }\r\n\r\n    override fun setSurfaceFrameRate(frameRate: Float) {\r\n        try{\r\n            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {\r\n                cacheSurfaceView?.getHolder()?.getSurface()?.setFrameRate(frameRate,\r\n                        Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE,\r\n                        Surface.CHANGE_FRAME_RATE_ALWAYS)\r\n            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {\r\n                cacheSurfaceView?.getHolder()?.getSurface()?.setFrameRate(\r\n                    frameRate, Surface.FRAME_RATE_COMPATIBILITY_FIXED_SOURCE)\r\n            }\r\n        } catch (e: Exception) {\r\n            // 如果设置失败，忽略\r\n        }\r\n    }\r\n\r\n    override fun setVideoTextureView(textureView: TextureView) {\r\n        cacheSurfaceView = null\r\n        textureView.surfaceTextureListener = object : SurfaceTextureListener {\r\n            override fun onSurfaceTextureAvailable(\r\n                surfaceTexture: SurfaceTexture,\r\n                width: Int,\r\n                height: Int\r\n            ) {\r\n                cacheSurfaceTexture = Surface(surfaceTexture)\r\n                player.setSurface(cacheSurfaceTexture)\r\n            }\r\n\r\n            override fun onSurfaceTextureSizeChanged(\r\n                surfaceTexture: SurfaceTexture,\r\n                width: Int,\r\n                height: Int\r\n            ) {\r\n            }\r\n\r\n            override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {\r\n                return true\r\n            }\r\n\r\n            override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {\r\n            }\r\n        }\r\n    }\r\n\r\n    override fun initialize() {\r\n        super.initialize()\r\n        player.setOnPreparedListener(this)\r\n        player.setOnVideoSizeChangedListener(this)\r\n        player.setOnErrorListener(this)\r\n    }\r\n\r\n    override fun release() {\r\n        player.setOnPreparedListener(null)\r\n        player.setOnVideoSizeChangedListener(null)\r\n        player.setOnErrorListener(null)\r\n        player.stop()\r\n        player.release()\r\n        cacheSurfaceTexture?.release()\r\n        super.release()\r\n    }\r\n\r\n    override fun onPrepared(player: IMediaPlayer) {\r\n        cacheSurfaceView?.let { player.setDisplay(it.holder) }\r\n        cacheSurfaceTexture?.let { player.setSurface(it) }\r\n\r\n        metadata = metadata.copy(\r\n                videoTracks = emptyList(),\r\n                audioTracks = emptyList(),\r\n                subtitleTracks = emptyList()\r\n            )\r\n        triggerMetadata(metadata)\r\n\r\n        updateVideoInfo(player)\r\n        updateAudioInfo(player)\r\n        updateSubtitleInfo(player)\r\n\r\n        triggerReady()\r\n        triggerBuffering(false)\r\n        triggerDuration(player.duration)\r\n\r\n        updateJob?.cancel()\r\n        updateJob = coroutineScope.launch {\r\n            while (true) {\r\n                triggerIsPlayingChanged(player.isPlaying)\r\n                triggerCurrentPosition(player.currentPosition)\r\n                delay(500)\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun IjkMediaMeta.IjkStreamMeta.toVideoMetadata(): Metadata.Video {\r\n        return Metadata.Video(\r\n            index = mIndex,\r\n            width = mWidth,\r\n            height = mHeight,\r\n            frameRate = mFpsNum?.toFloat(),\r\n            bitrate = mBitrate?.toInt(),\r\n            mimeType = mCodecName,\r\n            isSelected = false,\r\n            language = mLanguage,\r\n        )\r\n    }\r\n\r\n    private fun IjkMediaMeta.IjkStreamMeta.toAudioMetadata(): Metadata.Audio {\r\n        return Metadata.Audio(\r\n            index = mIndex,\r\n            channels = when (mChannelLayout) {\r\n                IjkMediaMeta.AV_CH_LAYOUT_MONO -> 1\r\n                IjkMediaMeta.AV_CH_LAYOUT_STEREO,\r\n                IjkMediaMeta.AV_CH_LAYOUT_2POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> 2\r\n\r\n                IjkMediaMeta.AV_CH_LAYOUT_2_1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> 3\r\n\r\n                IjkMediaMeta.AV_CH_LAYOUT_3POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_4POINT0,\r\n                IjkMediaMeta.AV_CH_LAYOUT_2_2,\r\n                IjkMediaMeta.AV_CH_LAYOUT_QUAD -> 4\r\n\r\n                IjkMediaMeta.AV_CH_LAYOUT_4POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> 5\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_3POINT1POINT2,\r\n                IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL,\r\n                IjkMediaMeta.AV_CH_LAYOUT_5POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> 6\r\n\r\n                IjkMediaMeta.AV_CH_LAYOUT_6POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> 7\r\n\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1,\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE,\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK,\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1_TOP_BACK,\r\n                // IjkMediaMeta.AV_CH_LAYOUT_CUBE,\r\n                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT2_BACK,\r\n                IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> 8\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_9POINT0 -> 9\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT4_BACK,\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT2 -> 10\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT4_BACK,\r\n                // IjkMediaMeta.AV_CH_LAYOUT_10POINT2 -> 12\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_HEXADECAGONAL -> 16\r\n\r\n                // IjkMediaMeta.AV_CH_LAYOUT_22POINT2 -> 24\r\n                else -> 0\r\n            },\r\n            channelsLabel = when (mChannelLayout) {\r\n                IjkMediaMeta.AV_CH_LAYOUT_MONO -> \"单声道\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_STEREO -> \"立体声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_2POINT1 -> \"2.1 声道\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_2_1 -> \"立体声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> \"环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_3POINT1 -> \"3.1 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_3POINT1POINT2 -> \"3.1.2 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_4POINT0 -> \"4.0 四声道\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_4POINT1 -> \"4.1 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_2_2 -> \"四声道\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_QUAD -> \"四声道\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_CUBE -> \"立方声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> \"5.0 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_5POINT1 -> \"5.1 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_5POINT1POINT2_BACK -> \"5.1.2 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> \"6.0 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_6POINT1 -> \"6.1 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> \"7.0 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1 -> \"7.1 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT2 -> \"7.1.2 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1POINT4_BACK -> \"后置 7.1.4 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE -> \"宽域 7.1 环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK -> \"后置 7.1 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_7POINT1_TOP_BACK -> \"上置 7.1 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_HEXADECAGONAL -> \"十六角环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL -> \"六角环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> \"八角环绕声\"\r\n                IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> \"立体声下混音\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_9POINT0 -> \"9.0 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_10POINT2 -> \"10.2 环绕声\"\r\n                // IjkMediaMeta.AV_CH_LAYOUT_22POINT2 -> \"22.2 环绕声\"\r\n                else -> null\r\n            },\r\n            sampleRate = mSampleRate,\r\n            bitrate = mBitrate?.toInt(),\r\n            mimeType = mCodecName,\r\n            language = mLanguage,\r\n        )\r\n    }\r\n\r\n    private fun IjkMediaMeta.IjkStreamMeta.toSubtitleMetadata(): Metadata.Subtitle {\r\n        return Metadata.Subtitle(\r\n            index = mIndex,\r\n            language = mLanguage ?: \"Unknown\",\r\n            mimeType = mCodecName,\r\n            bitrate = mBitrate?.toInt(),\r\n            isSelected = false,\r\n        )\r\n    }\r\n\r\n    override fun onError(player: IMediaPlayer, what: Int, extra: Int): Boolean {\r\n        triggerError(PlaybackException(\"IJK_ERROR_WHAT_$what\", extra))\r\n        return true\r\n    }\r\n\r\n    override fun onTimedText(player: IMediaPlayer, text: IjkTimedText) {\r\n        val bitmap = text.getBitmap() \r\n        val textContent = text.getText()\r\n        val rect = text.getBounds()\r\n        if(bitmap == null && textContent == null) {\r\n            return\r\n        }\r\n        val cue =  Cue.Builder().apply{\r\n            if(bitmap != null){\r\n                setBitmap(bitmap)\r\n                setBitmapHeight(text.getBitmapHeight())\r\n            }else if(textContent != null){\r\n                setText(textContent)\r\n            }\r\n        }\r\n        .build()\r\n        triggerCues(ImmutableList.of(cue))\r\n    }\r\n\r\n    override fun onSeekComplete(player: IMediaPlayer) {\r\n        updateVideoInfo(player)\r\n        updateAudioInfo(player)\r\n        updateSubtitleInfo(player)\r\n        triggerDuration(player.duration)\r\n        triggerCurrentPosition(player.currentPosition)\r\n    }\r\n\r\n    private fun updateVideoInfo(player: IMediaPlayer) {\r\n\r\n        val tracks = this.player.getTrackInfo()\r\n        val videoSelectedTrack = this.player.getSelectedTrack(\r\n            IjkTrackInfo.MEDIA_TRACK_TYPE_VIDEO\r\n        )\r\n        val videoFormats = tracks\r\n            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_VIDEO }\r\n            .map { \r\n                val metadata = it.getStreamMeta().toVideoMetadata()\r\n                metadata.copy(\r\n                    isSelected = metadata.index == videoSelectedTrack,\r\n                )\r\n                \r\n            }\r\n\r\n        val info = player.mediaInfo\r\n        metadata = metadata.copy(\r\n            videoTracks = videoFormats,\r\n            video = info.mMeta.mVideoStream?.toVideoMetadata()?.copy(\r\n                decoder = info.mVideoDecoderImpl,\r\n            ),\r\n        )\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    private fun updateAudioInfo(player: IMediaPlayer) {\r\n        val tracks = this.player.getTrackInfo()\r\n        val audioSelectedTrack = this.player.getSelectedTrack(\r\n            IjkTrackInfo.MEDIA_TRACK_TYPE_AUDIO\r\n        )\r\n        val audioFormats = tracks\r\n            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_AUDIO }\r\n            .map { \r\n                val metadata = it.getStreamMeta().toAudioMetadata()\r\n                metadata.copy(\r\n                    isSelected = metadata.index == audioSelectedTrack,\r\n                )\r\n            }\r\n        val info = player.mediaInfo\r\n        metadata = metadata.copy(\r\n            audioTracks = audioFormats,\r\n            audio = info.mMeta.mAudioStream?.toAudioMetadata()?.copy(\r\n                decoder = info.mAudioDecoderImpl,\r\n            ),\r\n        )\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    private fun updateSubtitleInfo(player: IMediaPlayer) {\r\n        val tracks = this.player.getTrackInfo()\r\n        val subtitleSelectedTrack = this.player.getSelectedTrack(\r\n            IjkTrackInfo.MEDIA_TRACK_TYPE_TIMEDTEXT\r\n        )\r\n        val subtitleFormats = tracks\r\n            .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_TIMEDTEXT }\r\n            .map { \r\n                val metadata = it.getStreamMeta().toSubtitleMetadata()\r\n                metadata.copy(\r\n                    isSelected = metadata.index == subtitleSelectedTrack,\r\n                )\r\n            }\r\n            .mapNotNull { it }\r\n\r\n        metadata = metadata.copy(\r\n            subtitleTracks = subtitleFormats,\r\n            subtitle = subtitleFormats.firstOrNull { it.isSelected == true },\r\n        )\r\n        triggerMetadata(metadata)\r\n    }\r\n\r\n    override fun onVideoSizeChanged(\r\n        player: IMediaPlayer,\r\n        width: Int,\r\n        height: Int,\r\n        sarNum: Int,\r\n        sarDen: Int\r\n    ) {\r\n        triggerResolution(width, height)\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/IjkVideoPlayer.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/IjkVideoPlayer.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/IjkVideoPlayer.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/IjkVideoPlayer.kt	(date 1756691200121)
@@ -49,7 +49,7 @@
             )
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1)
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzemaxduration", 500L)
-            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzeduration", 100L)
+            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzeduration", 1000000)
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "probesize", 1024 * 10)
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "seek2any", 1)
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "fflags", "fastseek")
@@ -76,7 +76,7 @@
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "fast", 1)
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1)
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1)
-            
+
             // rtsp设置 https://ffmpeg.org/ffmpeg-protocols.html#rtsp
             val transport = if (Configs.videoPlayerRtspTransport == Configs.RtspTransport.TCP) "tcp" else "udp"
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", transport)
@@ -84,7 +84,7 @@
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "max-buffer-size", getAvailableMemory(context).toLong())
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "infbuf", 1)  // 无限读
             setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "flush_packets", 1L)
-            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", 
+            setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames",
                     max(25L, (Configs.videoPlayerBufferTime.toLong() * 0.03).toLong())
             )
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1)
@@ -93,7 +93,7 @@
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 0)
 
             setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "subtitle", 1)
-            
+
             //https://www.cnblogs.com/Fitz/p/18537127
             // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter",0) //丢弃一些“无用”的数据包，例如AVI格式中的零大小数据包
             // setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_frame", 0) //不跳帧，解码所有帧
@@ -119,9 +119,9 @@
                 "Origin" to (line.httpOrigin ?: ""),
                 "Cookie" to (line.httpCookie ?: ""),
             ).filterValues { it.isNotEmpty() } + header
-            
+
             val userAgent = headers["User-Agent"] ?: headers["user-agent"] ?: line.httpUserAgent ?: Configs.videoPlayerUserAgent
-            headers = headers - "User-Agent" - "user-agent" 
+            headers = headers - "User-Agent" - "user-agent"
             // 使用应用内日志系统
             logger.i("播放地址: ${uri.toString()}")
             logger.i("请求头: $headers")
@@ -186,7 +186,7 @@
         else
             player.selectTrack(track.index)
         updateAudioInfo(player)
-        
+
         if(track != null) {
             metadata = metadata.copy(
                 audio = track.copy(
@@ -431,7 +431,7 @@
     }
 
     override fun onTimedText(player: IMediaPlayer, text: IjkTimedText) {
-        val bitmap = text.getBitmap() 
+        val bitmap = text.getBitmap()
         val textContent = text.getText()
         val rect = text.getBounds()
         if(bitmap == null && textContent == null) {
@@ -465,12 +465,12 @@
         )
         val videoFormats = tracks
             .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_VIDEO }
-            .map { 
+            .map {
                 val metadata = it.getStreamMeta().toVideoMetadata()
                 metadata.copy(
                     isSelected = metadata.index == videoSelectedTrack,
                 )
-                
+
             }
 
         val info = player.mediaInfo
@@ -490,7 +490,7 @@
         )
         val audioFormats = tracks
             .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_AUDIO }
-            .map { 
+            .map {
                 val metadata = it.getStreamMeta().toAudioMetadata()
                 metadata.copy(
                     isSelected = metadata.index == audioSelectedTrack,
@@ -513,7 +513,7 @@
         )
         val subtitleFormats = tracks
             .filter { it.getTrackType() == IjkTrackInfo.MEDIA_TRACK_TYPE_TIMEDTEXT }
-            .map { 
+            .map {
                 val metadata = it.getStreamMeta().toSubtitleMetadata()
                 metadata.copy(
                     isSelected = metadata.index == subtitleSelectedTrack,
Index: tv/build.gradle.kts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import com.android.build.gradle.internal.dsl.BaseAppModuleExtension\r\nimport java.io.FileInputStream\r\nimport java.util.Properties\r\n\r\nplugins {\r\n    alias(libs.plugins.android.application)\r\n    alias(libs.plugins.kotlin.android)\r\n    alias(libs.plugins.compose)\r\n    alias(libs.plugins.kotlin.serialization)\r\n    alias(libs.plugins.sentry.android.gradle)\r\n}\r\n\r\n\r\nandroid {\r\n    @Suppress(\"UNCHECKED_CAST\")\r\n    apply(extra[\"appConfig\"] as BaseAppModuleExtension.() -> Unit)\r\n\r\n    namespace = \"top.yogiczy.mytv.tv\"\r\n    compileSdk = libs.versions.compileSdk.get().toInt()\r\n\r\n    androidResources {\r\n        generateLocaleConfig = true\r\n    }\r\n    \r\n    defaultConfig {\r\n        applicationId = \"com.github.mytv.android\"\r\n        minSdk = libs.versions.minSdk.get().toInt()\r\n        targetSdk = libs.versions.targetSdk.get().toInt()\r\n        versionCode = \"${System.getenv(\"VERSION_CODE\")}\".toInt()\r\n        versionName = \"2.0.0.${System.getenv(\"VERSION_CODE\")}\"//.${System.getenv(\"COMMIT_HASH\")}\"\r\n        vectorDrawables {\r\n            useSupportLibrary = true\r\n        }\r\n\r\n        buildConfigField(\"String\", \"SENTRY_DSN\", \"\\\"${getProperty(\"sentry.dsn\") ?: System.getenv(\"SENTRY_DSN\")}\\\"\")\r\n    }\r\n\r\n    buildTypes {\r\n        release {\r\n            isMinifyEnabled = true\r\n            isShrinkResources = true\r\n            proguardFiles(\r\n                getDefaultProguardFile(\"proguard-android-optimize.txt\"),\r\n                \"proguard-rules.pro\",\r\n            )\r\n            signingConfig = signingConfigs.getByName(\"release\")\r\n        }\r\n        debug{\r\n            isMinifyEnabled = false\r\n            proguardFiles(\r\n                getDefaultProguardFile(\"proguard-android-optimize.txt\"),\r\n                \"proguard-rules.pro\",\r\n            )\r\n            signingConfig = signingConfigs.getByName(\"release\")\r\n        }\r\n    }\r\n    splits {\r\n        abi {\r\n            isEnable = true\r\n            reset()\r\n            include(\"armeabi-v7a\", \"arm64-v8a\", \"x86\", \"x86_64\")\r\n            isUniversalApk = true\r\n        }\r\n    }\r\n\r\n    compileOptions {\r\n        isCoreLibraryDesugaringEnabled = true\r\n        sourceCompatibility = JavaVersion.VERSION_11\r\n        targetCompatibility = JavaVersion.VERSION_11\r\n    }\r\n\r\n    kotlin {\r\n        compilerOptions {\r\n            jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11\r\n        }\r\n    }\r\n\r\n    buildFeatures {\r\n        compose = true\r\n        buildConfig = true\r\n    }\r\n\r\n    packaging {\r\n        resources {\r\n            excludes += \"/META-INF/{AL2.0,LGPL2.1}\"\r\n        }\r\n    }\r\n\r\n    flavorDimensions += listOf(\"version\")\r\n    productFlavors {\r\n        create(\"original\") {\r\n            dimension = \"version\"\r\n        }\r\n\r\n        create(\"disguised\") {\r\n            dimension = \"version\"\r\n            applicationId = \"com.chinablue.tv\"\r\n        }\r\n\r\n        create(\"x5offline\") {\r\n            dimension = \"version\"\r\n            applicationId = \"com.github.mytv.android.x5offline\"\r\n            ndk {\r\n                abiFilters += listOf(\"armeabi-v7a\", \"arm64-v8a\")\r\n            }\r\n            // sourceSets {\r\n            //     getByName(\"x5offline\") {\r\n            //         jniLibs.srcDirs(\"jniLibs\")\r\n            //     }\r\n            // }\r\n        }\r\n    }\r\n\r\n}\r\n\r\ndependencies {\r\n    implementation(libs.androidx.core.ktx)\r\n    implementation(libs.androidx.appcompat)\r\n    implementation(platform(libs.androidx.compose.bom))\r\n    implementation(libs.androidx.ui.tooling.preview)\r\n    implementation(libs.androidx.compose.foundation.base)\r\n    implementation(libs.androidx.tv.material)\r\n    implementation(libs.androidx.lifecycle.runtime.ktx)\r\n    implementation(libs.androidx.lifecycle.viewmodel.compose)\r\n    implementation(libs.androidx.activity.compose)\r\n    implementation(libs.androidx.navigation.compose)\r\n    implementation(libs.android.material)\r\n    implementation(libs.androidx.material3)\r\n    implementation(libs.kotlinx.serialization)\r\n    implementation(libs.kotlinx.collections.immutable)\r\n    implementation(libs.androidx.material.icons.extended)\r\n\r\n    // 播放器\r\n    val mediaSettingsFile = file(\"../../media/core_settings.gradle\")\r\n    if (mediaSettingsFile.exists()) {\r\n        implementation(project(\":media3:lib-exoplayer\"))\r\n        implementation(project(\":media3:lib-exoplayer-hls\"))\r\n        implementation(project(\":media3:lib-exoplayer-rtsp\"))\r\n        implementation(project(\":media3:lib-exoplayer-dash\"))\r\n        implementation(project(\":media3:lib-ui\"))\r\n    } else {\r\n        implementation(libs.androidx.media3.exoplayer)\r\n        implementation(libs.androidx.media3.exoplayer.hls)\r\n        implementation(libs.androidx.media3.exoplayer.rtsp)\r\n        implementation(libs.androidx.media3.exoplayer.dash)\r\n        implementation(libs.androidx.media3.ui)\r\n    }\r\n    implementation(libs.androidx.media3.common)\r\n    implementation(libs.androidx.media3.datasource.rtmp)\r\n    implementation(libs.androidx.media3.exoplayer.smoothstreaming)\r\n\r\n    // 二维码\r\n    implementation(libs.qrose)\r\n\r\n    implementation(libs.coil.compose)\r\n    implementation(libs.coil.svg)\r\n\r\n    implementation(libs.okhttp)\r\n    implementation(libs.androidasync)\r\n    implementation(libs.quickjs.kt)\r\n    implementation(libs.libvlc)\r\n    implementation(libs.tinypinyin)\r\n\r\n    implementation(project(\":core:data\"))\r\n    implementation(project(\":core:designsystem\"))\r\n    implementation(project(\":core:util\"))\r\n    implementation(project(\":ijkplayer-java\"))\r\n    implementation(project(\":webview\"))\r\n    implementation(fileTree(mapOf(\"dir\" to \"libs\", \"include\" to listOf(\"*.aar\"))))\r\n    implementation(fileTree(mapOf(\"dir\" to \"libs\", \"include\" to listOf(\"*.jar\"))))\r\n    add(\"originalImplementation\", fileTree(mapOf(\"dir\" to \"libs/tbs_online\", \"include\" to listOf(\"*.jar\"))))\r\n    add(\"disguisedImplementation\", fileTree(mapOf(\"dir\" to \"libs/tbs_online\", \"include\" to listOf(\"*.jar\"))))\r\n    add(\"x5offlineImplementation\", fileTree(mapOf(\"dir\" to \"libs/tbs_offline\", \"include\" to listOf(\"*.jar\"))))\r\n    androidTestImplementation(platform(libs.androidx.compose.bom))\r\n    androidTestImplementation(libs.androidx.ui.test.junit4)\r\n    debugImplementation(libs.androidx.ui.tooling)\r\n    debugImplementation(libs.androidx.ui.test.manifest)\r\n    coreLibraryDesugaring(libs.desugar.jdk.libs)\r\n}\r\n\r\nsentry {\r\n    org.set(\"mytv-android\")\r\n    projectName.set(\"mytv\")\r\n    authToken.set(getProperty(\"sentry.auth_token\") ?: System.getenv(\"SENTRY_AUTH_TOKEN\"))\r\n    autoUploadProguardMapping = false\r\n}\r\n\r\nfun getProperty(key: String): String? {\r\n    val propertiesFile = rootProject.file(\"local.properties\")\r\n    if (propertiesFile.exists()) {\r\n        val properties = Properties()\r\n        properties.load(FileInputStream(propertiesFile))\r\n\r\n        return properties.getProperty(key)\r\n    }\r\n\r\n    return null\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/build.gradle.kts b/tv/build.gradle.kts
--- a/tv/build.gradle.kts	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/build.gradle.kts	(date 1756691200096)
@@ -7,7 +7,7 @@
     alias(libs.plugins.kotlin.android)
     alias(libs.plugins.compose)
     alias(libs.plugins.kotlin.serialization)
-    alias(libs.plugins.sentry.android.gradle)
+    // alias(libs.plugins.sentry.android.gradle)
 }
 
 
@@ -26,13 +26,13 @@
         applicationId = "com.github.mytv.android"
         minSdk = libs.versions.minSdk.get().toInt()
         targetSdk = libs.versions.targetSdk.get().toInt()
-        versionCode = "${System.getenv("VERSION_CODE")}".toInt()
-        versionName = "2.0.0.${System.getenv("VERSION_CODE")}"//.${System.getenv("COMMIT_HASH")}"
+        versionCode = "1".toInt()
+        versionName = "2.0.0.0"//.${System.getenv("COMMIT_HASH")}"
         vectorDrawables {
             useSupportLibrary = true
         }
 
-        buildConfigField("String", "SENTRY_DSN", "\"${getProperty("sentry.dsn") ?: System.getenv("SENTRY_DSN")}\"")
+        // buildConfigField("String", "SENTRY_DSN", "\"${getProperty("sentry.dsn") ?: System.getenv("SENTRY_DSN")}\"")
     }
 
     buildTypes {
@@ -171,6 +171,7 @@
     add("originalImplementation", fileTree(mapOf("dir" to "libs/tbs_online", "include" to listOf("*.jar"))))
     add("disguisedImplementation", fileTree(mapOf("dir" to "libs/tbs_online", "include" to listOf("*.jar"))))
     add("x5offlineImplementation", fileTree(mapOf("dir" to "libs/tbs_offline", "include" to listOf("*.jar"))))
+    testImplementation("junit:junit:4.13.2")
     androidTestImplementation(platform(libs.androidx.compose.bom))
     androidTestImplementation(libs.androidx.ui.test.junit4)
     debugImplementation(libs.androidx.ui.tooling)
@@ -178,12 +179,7 @@
     coreLibraryDesugaring(libs.desugar.jdk.libs)
 }
 
-sentry {
-    org.set("mytv-android")
-    projectName.set("mytv")
-    authToken.set(getProperty("sentry.auth_token") ?: System.getenv("SENTRY_AUTH_TOKEN"))
-    autoUploadProguardMapping = false
-}
+//
 
 fun getProperty(key: String): String? {
     val propertiesFile = rootProject.file("local.properties")
Index: tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv\r\n\r\nimport android.app.Application\r\nimport android.content.Intent\r\nimport coil.ImageLoader\r\nimport coil.ImageLoaderFactory\r\nimport coil.decode.SvgDecoder\r\nimport coil.disk.DiskCache\r\nimport coil.memory.MemoryCache\r\nimport coil.request.CachePolicy\r\nimport coil.util.DebugLogger\r\nimport io.sentry.Hint\r\nimport io.sentry.Sentry\r\nimport io.sentry.SentryEvent\r\nimport io.sentry.SentryLevel\r\nimport io.sentry.SentryOptions\r\nimport io.sentry.android.core.SentryAndroid\r\nimport top.yogiczy.mytv.core.data.AppData\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport kotlin.system.exitProcess\r\nimport com.tencent.smtt.sdk.QbSdk\r\nimport com.whl.quickjs.android.QuickJSLoader\r\n\r\nclass MyTVApplication : Application(), ImageLoaderFactory {\r\n    override fun onCreate() {\r\n        super.onCreate()\r\n        \r\n        initSentry()\r\n        crashHandle()\r\n        QuickJSLoader.init()\r\n        AppData.init(applicationContext)\r\n        UnsafeTrustManager.enableUnsafeTrustManager()\r\n        \r\n    }\r\n\r\n    override fun newImageLoader(): ImageLoader {\r\n        return ImageLoader(this).newBuilder()\r\n            .logger(DebugLogger())\r\n            .components {\r\n                add(SvgDecoder.Factory())\r\n            }\r\n            .crossfade(true)\r\n            .memoryCachePolicy(CachePolicy.ENABLED)\r\n            .memoryCache {\r\n                MemoryCache.Builder(this)\r\n                    // .maxSizePercent(0.25)\r\n                    .build()\r\n            }\r\n            .diskCachePolicy(CachePolicy.ENABLED)\r\n            .diskCache {\r\n                DiskCache.Builder()\r\n                    .directory(cacheDir.resolve(\"image_cache\"))\r\n                    // .maxSizeBytes(1024 * 1024 * 100)\r\n                    .build()\r\n            }\r\n            .build()\r\n    }\r\n\r\n    private fun initSentry() {\r\n        SentryAndroid.init(this) { options ->\r\n            options.environment = BuildConfig.BUILD_TYPE\r\n            options.dsn = BuildConfig.SENTRY_DSN\r\n            options.tracesSampleRate = 1.0\r\n            options.beforeSend =\r\n                SentryOptions.BeforeSendCallback { event: SentryEvent, _: Hint ->\r\n                    if (event.level == null) event.level = SentryLevel.FATAL\r\n\r\n                    if (BuildConfig.DEBUG) return@BeforeSendCallback null\r\n                    if (SentryLevel.ERROR != event.level && SentryLevel.FATAL != event.level) return@BeforeSendCallback null\r\n                    if (event.exceptions?.any { ex -> ex.type?.contains(\"Http\") == true } == true) return@BeforeSendCallback null\r\n\r\n                    event\r\n                }\r\n        }\r\n\r\n        @Suppress(\"UnstableApiUsage\")\r\n        Sentry.withScope { scope ->\r\n            Globals.deviceId = scope.options.distinctId ?: \"\"\r\n        }\r\n    }\r\n\r\n    private fun crashHandle() {\r\n        Thread.setDefaultUncaughtExceptionHandler { _, throwable ->\r\n            throwable.printStackTrace()\r\n            Sentry.captureException(throwable)\r\n\r\n            val intent = Intent(this, CrashHandlerActivity::class.java).apply {\r\n                putExtra(\"error_message\", throwable.message)\r\n                putExtra(\"error_stacktrace\", throwable.stackTraceToString())\r\n                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)\r\n            }\r\n            startActivity(intent)\r\n\r\n            android.os.Process.killProcess(android.os.Process.myPid())\r\n            exitProcess(1)\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt b/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt	(date 1756691200099)
@@ -9,12 +9,6 @@
 import coil.memory.MemoryCache
 import coil.request.CachePolicy
 import coil.util.DebugLogger
-import io.sentry.Hint
-import io.sentry.Sentry
-import io.sentry.SentryEvent
-import io.sentry.SentryLevel
-import io.sentry.SentryOptions
-import io.sentry.android.core.SentryAndroid
 import top.yogiczy.mytv.core.data.AppData
 import top.yogiczy.mytv.core.data.utils.Globals
 import kotlin.system.exitProcess
@@ -25,7 +19,7 @@
     override fun onCreate() {
         super.onCreate()
         
-        initSentry()
+        // initSentry()
         crashHandle()
         QuickJSLoader.init()
         AppData.init(applicationContext)
@@ -56,33 +50,9 @@
             .build()
     }
 
-    private fun initSentry() {
-        SentryAndroid.init(this) { options ->
-            options.environment = BuildConfig.BUILD_TYPE
-            options.dsn = BuildConfig.SENTRY_DSN
-            options.tracesSampleRate = 1.0
-            options.beforeSend =
-                SentryOptions.BeforeSendCallback { event: SentryEvent, _: Hint ->
-                    if (event.level == null) event.level = SentryLevel.FATAL
-
-                    if (BuildConfig.DEBUG) return@BeforeSendCallback null
-                    if (SentryLevel.ERROR != event.level && SentryLevel.FATAL != event.level) return@BeforeSendCallback null
-                    if (event.exceptions?.any { ex -> ex.type?.contains("Http") == true } == true) return@BeforeSendCallback null
-
-                    event
-                }
-        }
-
-        @Suppress("UnstableApiUsage")
-        Sentry.withScope { scope ->
-            Globals.deviceId = scope.options.distinctId ?: ""
-        }
-    }
-
     private fun crashHandle() {
         Thread.setDefaultUncaughtExceptionHandler { _, throwable ->
             throwable.printStackTrace()
-            Sentry.captureException(throwable)
 
             val intent = Intent(this, CrashHandlerActivity::class.java).apply {
                 putExtra("error_message", throwable.message)
Index: tv/src/main/res/values/strings.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><resources>\r\n    <string name=\"app_name\">电视直播</string>\r\n    <string name=\"codec_detail_audio_bitrate_range_title\">音频码率范围</string>\r\n    <string name=\"codec_detail_color_formats_title\">颜色格式</string>\r\n    <string name=\"codec_detail_hs_hardware\">硬解</string>\r\n    <string name=\"codec_detail_hs_software\">软解</string>\r\n    <string name=\"codec_detail_hs_title\">软硬解码</string>\r\n    <string name=\"codec_detail_max_supported_instances_title\">最大并发编解码器实例数量</string>\r\n    <string name=\"codec_detail_video_frame_achievable_title\">可实现的视频帧率（可能无数据）</string>\r\n    <string name=\"codec_detail_video_frame_range_title\">视频帧率范围</string>\r\n    <string name=\"codec_detail_video_frame_supported_title\">支持的视频帧率</string>\r\n    <string name=\"codec_detail_video_frame_unsupported\">不受支持</string>\r\n    <string name=\"codec_detail_video_max_bitrate_title\">最大视频码率</string>\r\n    <string name=\"codec_detail_video_resolution_1080p\">1080P</string>\r\n    <string name=\"codec_detail_video_resolution_1440p\">2K</string>\r\n    <string name=\"codec_detail_video_resolution_2160p\">4K</string>\r\n    <string name=\"codec_detail_video_resolution_360p\">360P</string>\r\n    <string name=\"codec_detail_video_resolution_4320p\">8K</string>\r\n    <string name=\"codec_detail_video_resolution_480p\">480P</string>\r\n    <string name=\"codec_detail_video_resolution_720p\">720P</string>\r\n    <string name=\"codec_detail_video_resolution_unknown\">未知</string>\r\n    <string name=\"ui_welcome_sentence0\">欢迎使用</string>\r\n    <string name=\"ui_welcome_sentence1\">，请在使用前仔细阅读以下内容：</string>\r\n    <string name=\"ui_welcome_sentence2\">1. 本软件仅供学习交流使用，禁止用于任何商业用途，您不得二次编辑和修改本软件。</string>\r\n    <string name=\"ui_welcome_sentence3\">2. 本软件不提供任何直播内容，所有直播内容均来自网络。</string>\r\n    <string name=\"ui_welcome_sentence4\">3. 本软件完全基于您个人意愿使用，您应该对自己的使用行为和所有结果承担全部责任。</string>\r\n    <string name=\"ui_welcome_sentence5\">4. 如果本软件存在侵犯您的合法权益的情况，请及时与作者联系，作者将会及时删除有关内容。</string>\r\n    <string name=\"ui_welcome_sentence6\">如您继续使用本软件即代表您已完全理解并同意上述内容。</string>\r\n    <string name=\"ui_agreement_title\">使用须知</string>\r\n    <string name=\"ui_agreement_agree\">已阅读并同意</string>\r\n    <string name=\"ui_agreement_disagree\">退出应用</string>\r\n    <string name=\"ui_dashboard_module_live\">直播</string>\r\n    <string name=\"ui_dashboard_module_all_channels\">全部频道</string>\r\n    <string name=\"ui_dashboard_module_favorites\">收藏</string>\r\n    <string name=\"ui_dashboard_module_search\">搜索</string>\r\n    <string name=\"ui_dashboard_module_multi_view\">多屏同播</string>\r\n    <string name=\"ui_dashboard_module_push\">推送</string>\r\n    <string name=\"ui_dashboard_module_settings\">设置</string>\r\n    <string name=\"ui_dashboard_module_about\">关于</string>\r\n    <string name=\"ui_dashboard_module_recently_watched\">最近观看</string>\r\n    <string name=\"ui_favorites_clear\">清空</string>\r\n    <string name=\"ui_channel_group_all\">全部</string>\r\n    <string name=\"ui_channel_info_time_shift\">时移</string>\r\n    <string name=\"ui_channel_info_replay\">回放</string>\r\n    <string name=\"ui_channel_info_ready\">就绪</string>\r\n    <string name=\"ui_channel_info_loading\">加载中...</string>\r\n    <string name=\"ui_channel_info_load_failed\">加载失败</string>\r\n    <string name=\"ui_channel_info_favorite_cancel\">取消收藏：</string>\r\n    <string name=\"ui_channel_info_favorite_add\">已收藏：</string>\r\n    <string name=\"ui_channel_info_favorite_clear\">已清空所有收藏</string>\r\n    <string name=\"ui_channel_info_update_checking\">正在检查更新...</string>\r\n    <string name=\"ui_channel_info_update_found\">发现新版本: </string>\r\n    <string name=\"ui_channel_info_update_latest\">当前已是最新版本</string>\r\n    <string name=\"ui_channel_info_exit_live\">再按一次退出直播</string>\r\n    <string name=\"ui_app_exit\">再按一次退出</string>\r\n    <string name=\"ui_cache_cleared\">缓存已清除</string>\r\n    <string name=\"ui_cloud_sync_pull\">拉取云端数据</string>\r\n    <string name=\"ui_iptv_source_loading\">加载订阅源</string>\r\n    <string name=\"ui_iptv_channel_merge\">合并相似频道</string>\r\n    <string name=\"ui_iptv_channel_hybrid\">匹配可用的网页源</string>\r\n    <string name=\"ui_iptv_channel_epg_failed\">节目单获取失败，请检查网络连接</string>\r\n    <string name=\"ui_multi_view_max_count_exceeded\">超出最大添加频道数：</string>\r\n    <string name=\"ui_multi_view_channel_exists\">已存在该频道</string>\r\n    <string name=\"ui_multi_view_channel_minimum\">至少保留一个频道</string>\r\n    <string name=\"ui_push_service_started\">服务已启动：</string>\r\n    <string name=\"ui_push_scan_qr\">请扫描二维码或输入IP地址进行连接</string>\r\n    <string name=\"ui_search_keyword_hint\">关键词...</string>\r\n    <string name=\"ui_crash_handler_app_crashed\">应用崩溃了</string>\r\n    <string name=\"ui_crash_handler_copy_log\">复制日志</string>\r\n    <string name=\"ui_crash_handler_crash_log\">崩溃日志</string>\r\n    <string name=\"ui_crash_handler_crash_log_copied\">崩溃日志已复制到剪贴板</string>\r\n    <string name=\"ui_crash_handler_restart\">重启</string>\r\n    <string name=\"ui_crash_handler_tip\">提示：您可以点击上方的\\\"复制日志\\\"按钮复制完整崩溃信息，以便报告问题。</string>\r\n    <string name=\"ui_return\">返回</string>\r\n    <string name=\"ui_excellent_program\">精彩节目</string>\r\n    <string name=\"ui_minutes\">分钟</string>\r\n    <string name=\"ui_favorites_empty\">没有收藏的频道</string>\r\n    <string name=\"ui_channel_empty\">尚未加载列表</string>\r\n    <string name=\"ui_channel_view_schedule\">向右查看节目单</string>\r\n    <string name=\"ui_channel_view_subscription_left\">向左查看订阅源</string>\r\n    <string name=\"ui_channel_view_source\">订阅源</string>\r\n    <string name=\"ui_channel_view_epg\">节目单</string>\r\n    <string name=\"ui_channel_view_route\">线路</string>\r\n    <string name=\"ui_channel_view_playback_control\">播放控制</string>\r\n    <string name=\"ui_channel_view_player1\">播放器：</string>\r\n    <string name=\"ui_channel_view_video_track\">视轨</string>\r\n    <string name=\"ui_channel_view_audio_track\">音轨</string>\r\n    <string name=\"ui_channel_view_subtitle\">字幕</string>\r\n    <string name=\"ui_channel_view_display_mode\">显示模式</string>\r\n    <string name=\"ui_channel_view_clear_cache\">清除缓存</string>\r\n    <string name=\"ui_channel_view_home\">主页</string>\r\n    <string name=\"ui_channel_view_force_soft_decode\">强制软解</string>\r\n    <string name=\"ui_channel_view_auto_decode\">自动解码</string>\r\n    <string name=\"ui_channel_view_refresh\">刷新</string>\r\n    <string name=\"ui_channel_view_general\">通用</string>\r\n    <string name=\"ui_channel_view_interface\">界面</string>\r\n    <string name=\"ui_channel_view_theme\">主题</string>\r\n    <string name=\"ui_channel_view_control\">控制</string>\r\n    <string name=\"ui_channel_view_player\">播放器</string>\r\n    <string name=\"ui_channel_view_update\">更新</string>\r\n    <string name=\"ui_channel_view_network\">网络</string>\r\n    <string name=\"ui_channel_view_cloud_sync\">云同步</string>\r\n    <string name=\"ui_channel_view_permissions\">权限</string>\r\n    <string name=\"ui_channel_view_debug\">调试</string>\r\n    <string name=\"ui_channel_view_log\">日志</string>\r\n    <string name=\"ui_channel_view_boot_start\">开机自启</string>\r\n    <string name=\"ui_channel_view_boot_start_support\">请确保当前设备支持该功能</string>\r\n    <string name=\"ui_channel_view_boot_start_live\">打开直接进入直播</string>\r\n    <string name=\"ui_channel_view_picture_in_picture\">画中画</string>\r\n    <string name=\"ui_channel_view_clear_cache_support\">约</string>\r\n    <string name=\"ui_channel_view_restore_initialization\">恢复初始化</string>\r\n    <string name=\"ui_channel_view_restore_initialization_support\">已恢复初始化</string>\r\n    <string name=\"cloud_sync_pull_failed\">拉取云端失败</string>\r\n    <string name=\"cloud_sync_pull\">拉取云端</string>\r\n    <string name=\"cloud_sync_push\">推送云端</string>\r\n    <string name=\"cloud_sync_push_success\">推送云端成功</string>\r\n    <string name=\"cloud_sync_push_failed\">推送云端失败</string>\r\n    <string name=\"cloud_sync_data\">云端数据</string>\r\n    <string name=\"cloud_sync_data_long_press\">长按应用当前云端数据</string>\r\n    <string name=\"cloud_sync_no_data\">无云端数据</string>\r\n    <string name=\"cloud_sync_version\">云端版本</string>\r\n    <string name=\"cloud_sync_push_time\">推送时间</string>\r\n    <string name=\"cloud_sync_push_device\">推送设备</string>\r\n    <string name=\"cloud_sync_description\">备注</string>\r\n    <string name=\"cloud_sync_apply_success\">应用云端数据成功</string>\r\n    <string name=\"cloud_sync_auto_pull\">自动拉取</string>\r\n    <string name=\"cloud_sync_auto_pull_desc\">应用启动时自动拉取云端数据并应用</string>\r\n    <string name=\"cloud_sync_provider\">云同步服务商</string>\r\n    <string name=\"cloud_sync_github_gist_id\">Github Gist Id</string>\r\n    <string name=\"cloud_sync_github_gist_token\">Github Gist Token</string>\r\n    <string name=\"cloud_sync_gitee_gist_id\">Gitee 代码片段 Id</string>\r\n    <string name=\"cloud_sync_gitee_gist_token\">Gitee 代码片段 Token</string>\r\n    <string name=\"cloud_sync_network_url\">网络链接</string>\r\n    <string name=\"cloud_sync_local_file_path\">本地文件路径</string>\r\n    <string name=\"cloud_sync_webdav_url\">WebDAV 地址</string>\r\n    <string name=\"cloud_sync_webdav_username\">WebDAV 用户名</string>\r\n    <string name=\"cloud_sync_webdav_password\">WebDAV 密码</string>\r\n    <string name=\"ui_channel_no_select\">数字选台</string>\r\n    <string name=\"ui_channel_no_select_desc\">通过数字键选择频道</string>\r\n    <string name=\"ui_channel_list_loop\">频道列表首尾循环</string>\r\n    <string name=\"ui_channel_list_loop_desc\">启用后，到达列表首尾时将循环切换到另一端</string>\r\n    <string name=\"ui_channel_change_cross_group\">频道切换跨分组</string>\r\n    <string name=\"ui_channel_change_cross_group_desc\">启用后，上下键可在所有频道间切换；关闭则仅在当前分组内切换</string>\r\n    <string name=\"ui_control_action_settings\">按键（手势）行为</string>\r\n    <string name=\"ui_control_action_settings_desc\">自定义播放界面的按键/手势行为</string>\r\n    <string name=\"ui_debug_show_fps\">显示FPS</string>\r\n    <string name=\"ui_debug_show_fps_desc\">在屏幕左上角显示fps和柱状图</string>\r\n    <string name=\"ui_debug_show_player_metadata\">显示播放器信息</string>\r\n    <string name=\"ui_debug_show_player_metadata_desc\">显示播放器详细信息（编码、解码器、采样率等）</string>\r\n    <string name=\"ui_debug_show_layout_grids\">显示布局网格</string>\r\n    <string name=\"ui_debug_decoder_info\">解码器信息</string>\r\n    <string name=\"ui_debug_decoder_info_desc\">查看系统解码器</string>\r\n    <string name=\"ui_epg_enable\">节目单启用</string>\r\n    <string name=\"ui_epg_enable_desc\">首次加载时可能会较为缓慢</string>\r\n    <string name=\"ui_epg_source_follow_iptv\">跟随订阅源</string>\r\n    <string name=\"ui_epg_source_follow_iptv_desc\">优先使用订阅源中定义的节目单</string>\r\n    <string name=\"ui_epg_source_custom\">自定义节目单</string>\r\n    <string name=\"ui_epg_refresh_time_threshold\">刷新时间阈值</string>\r\n    <string name=\"ui_epg_refresh_time_threshold_desc\">时间不到%1$d:00节目单将不会刷新</string>\r\n    <string name=\"ui_custom_subscription_source\">自定义订阅源</string>\r\n    <string name=\"ui_subscription_source_cache_time\">订阅源缓存时间</string>\r\n    <string name=\"ui_subscription_source_cache_time_none\">不缓存</string>\r\n    <string name=\"ui_subscription_source_cache_time_forever\">永久</string>\r\n    <string name=\"ui_channel_group_manage\">频道分组管理</string>\r\n    <string name=\"ui_channel_group_count\">共%1$d个分组</string>\r\n    <string name=\"ui_channel_group_count_hidden\">共%1$d个分组，已隐藏%2$d个分组</string>\r\n    <string name=\"ui_channel_alias\">频道别名</string>\r\n    <string name=\"ui_channel_alias_count\">共%1$d个频道，%2$d个别名</string>\r\n    <string name=\"ui_similar_channel_merge\">相似频道合并</string>\r\n    <string name=\"ui_similar_channel_merge_desc\">相同频道别名将进行合并</string>\r\n    <string name=\"ui_channel_logo_provider\">频道图标提供</string>\r\n    <string name=\"ui_channel_logo_override\">频道图标覆盖</string>\r\n    <string name=\"ui_channel_logo_override_desc\">使用频道图标提供覆盖订阅源中定义的频道图标</string>\r\n    <string name=\"ui_iptv_pltv_to_tvod\">PLTV转TVOD</string>\r\n    <string name=\"ui_iptv_pltv_to_tvod_desc\">自动将订阅源链接中的PLTV替换为TVOD以支持回看</string>\r\n    <string name=\"ui_auto_add_web_source\">自动添加网页源</string>\r\n    <string name=\"ui_auto_add_web_source_desc\">为订阅源中的频道自动添加对应的网页源线路</string>\r\n    <string name=\"ui_iptv_hybrid_yangshipin_cookie\">网页源央视频Cookie</string>\r\n    <string name=\"ui_iptv_hybrid_yangshipin_cookie_desc\">登录到央视频以收看付费频道</string>\r\n    <string name=\"ui_network_retry_count\">HTTP请求重试次数</string>\r\n    <string name=\"ui_network_retry_count_desc\">影响订阅源、节目单数据获取</string>\r\n    <string name=\"ui_network_retry_interval\">HTTP请求重试间隔时间</string>\r\n    <string name=\"ui_network_retry_interval_desc\">影响订阅源、节目单数据获取</string>\r\n    <string name=\"ui_permission_install_other_apps\">安装未知应用</string>\r\n    <string name=\"ui_permission_read_external_storage\">读取外部存储/管理全部文件</string>\r\n    <string name=\"ui_theme_reset_to_default\">恢复默认</string>\r\n    <string name=\"ui_show_epg_programme_progress\">节目进度</string>\r\n    <string name=\"ui_show_epg_programme_progress_desc\">在频道底部显示当前节目进度条</string>\r\n    <string name=\"ui_show_epg_programme_permanent_progress\">常驻节目进度</string>\r\n    <string name=\"ui_show_epg_programme_permanent_progress_desc\">在播放器底部显示当前节目进度条</string>\r\n    <string name=\"ui_show_channel_logo\">台标显示</string>\r\n    <string name=\"ui_show_channel_preview\">频道预览</string>\r\n    <string name=\"ui_use_classic_panel_screen\">经典选台界面</string>\r\n    <string name=\"ui_use_classic_panel_screen_desc\">将选台界面替换为经典三段式结构</string>\r\n    <string name=\"ui_classic_show_source_list\">显示订阅源列表</string>\r\n    <string name=\"ui_classic_show_source_list_desc\">在经典选台界面中启用\"向左查看订阅源\"功能</string>\r\n    <string name=\"ui_classic_show_channel_info\">显示频道信息</string>\r\n    <string name=\"ui_classic_show_channel_info_desc\">在经典选台界面中显示当前频道的详细信息</string>\r\n    <string name=\"ui_classic_show_all_channels\">显示全部频道</string>\r\n    <string name=\"ui_classic_show_all_channels_desc\">是否显示当前订阅源全部频道列表</string>\r\n    <string name=\"ui_time_show_mode\">时间显示</string>\r\n    <string name=\"ui_screen_auto_close_delay\">超时自动关闭界面</string>\r\n    <string name=\"ui_screen_auto_close_delay_never\">不关闭</string>\r\n    <string name=\"ui_density_scale_ratio\">界面整体缩放比例</string>\r\n    <string name=\"ui_density_scale_ratio_auto\">自适应</string>\r\n    <string name=\"ui_font_scale_ratio\">界面字体缩放比例</string>\r\n    <string name=\"ui_video_player_subtitle_settings\">字幕设置</string>\r\n    <string name=\"ui_video_player_subtitle_settings_desc\">字幕样式调整</string>\r\n    <string name=\"ui_focus_optimize\">焦点优化</string>\r\n    <string name=\"ui_focus_optimize_desc\">关闭后可解决触摸设备在部分场景下闪退</string>\r\n    <string name=\"iptv_channel_favorite_enable\">启用收藏</string>\r\n    <string name=\"iptv_channel_favorite_enable_desc\">是否显示当前订阅源频道收藏列表</string>\r\n    <string name=\"ui_channel_view_update_channel\">更新通道</string>\r\n    <string name=\"ui_channel_view_stable\">稳定版</string>\r\n    <string name=\"ui_channel_view_beta\">测试版</string>\r\n    <string name=\"ui_channel_view_dev\">开发版</string>\r\n    <string name=\"ui_channel_view_force_remind\">更新强提醒</string>\r\n    <string name=\"ui_channel_view_force_remind_on\">检测到新版本时会全屏提醒</string>\r\n    <string name=\"ui_channel_view_force_remind_off\">检测到新版本时仅消息提示</string>\r\n    <string name=\"ui_player_view_player\">视频播放器</string>\r\n    <string name=\"ui_player_view_player_core\">视频播放器内核</string>\r\n    <string name=\"ui_player_view_render_mode\">渲染方式</string>\r\n    <string name=\"ui_player_view_force_soft_decode\">强制软解</string>\r\n    <string name=\"ui_player_view_force_soft_decode_desc\">对于Media3，使用设备和扩展软解码器;\\n对于IJK，将禁用MediaCodec解码（使用ffmpeg）</string>\r\n    <string name=\"ui_player_view_stop_previous_media_item\">停止上一媒体项</string>\r\n    <string name=\"ui_player_view_skip_multiple_frames_on_same_vsync\">跳过多帧渲染</string>\r\n    <string name=\"ui_player_view_support_ts_high_profile\">支持 Media TS 高复杂度解析</string>\r\n    <string name=\"ui_player_view_support_ts_high_profile_desc\">支持在某些设备上使用 Media3 播放缺少 AUD 或 IDR 关键帧的 MPEG-TS 文件，启用该选项可能导致意外错误</string>\r\n    <string name=\"ui_player_view_extract_header_from_link\">在链接中提取 Header</string>\r\n    <string name=\"ui_player_view_extract_header_from_link_desc\">支持在链接中提取 以|分隔的 Header 信息</string>\r\n    <string name=\"ui_player_view_display_mode\">全局显示模式</string>\r\n    <string name=\"ui_player_view_webview_core\">WebView内核</string>\r\n    <string name=\"ui_player_view_load_timeout\">加载超时</string>\r\n    <string name=\"ui_player_view_load_timeout_desc\">影响超时换源、断线重连</string>\r\n    <string name=\"ui_player_view_buffer_time\">播放缓冲</string>\r\n    <string name=\"ui_player_view_buffer_time_desc\">对于Media3，为播放前的最小缓存加载时间（秒）;\\n对于Ijk，为播放前的最小缓存加载帧（f）</string>\r\n    <string name=\"ui_player_view_user_agent\">全局UA</string>\r\n    <string name=\"ui_player_view_rtsp_transport\">RTSP 传输方式</string>\r\n    <string name=\"ui_player_view_custom_headers\">自定义headers</string>\r\n    <!--\r\n    <string name=\"ui_player_view_volume_normalization\">音量平衡</string>\r\n    <string name=\"ui_player_view_volume_normalization_desc\">启用后，将统一均衡输出播放音量，解决订阅源音量大小不一致的情况；仅支持Media3播放器</string>\r\n    -->\r\n    <string name=\"ui_multiview_action_operate_screen\">操作屏幕</string>\r\n    <string name=\"ui_multiview_action_add\">添加</string>\r\n    <string name=\"ui_multiview_action_switch\">切换</string>\r\n    <string name=\"ui_multiview_action_delete\">删除</string>\r\n    <string name=\"ui_multiview_action_zoom_in\">放大</string>\r\n    <string name=\"ui_multiview_action_zoom_out\">缩小</string>\r\n    <string name=\"ui_multiview_action_pause\">暂停</string>\r\n    <string name=\"ui_multiview_action_play\">播放</string>\r\n    <string name=\"ui_multiview_action_mute\">静音</string>\r\n    <string name=\"ui_multiview_action_unmute\">取消静音</string>\r\n    <string name=\"ui_multiview_action_move_screen\">移动屏幕</string>\r\n    <string name=\"ui_multiview_action_move_to_screen\">移动至屏幕</string>\r\n    <string name=\"ui_close\">关闭</string>\r\n    <string name=\"ui_close_not\">不关闭</string>\r\n    <string name=\"ui_add_other_playlists\">添加其他节目单</string>\r\n    <string name=\"ui_channel_view_delete\">删除</string>\r\n    <string name=\"ui_channel_view_set_current\">设为当前</string>\r\n    <string name=\"ui_hybrid_mode_disable_auto_add_web_source\">禁用自动添加网页源</string>\r\n    <string name=\"ui_hybrid_mode_auto_add_web_source_to_back\">将自动添加的网页源线路排在订阅源的后面</string>\r\n    <string name=\"ui_hybrid_mode_auto_add_web_source_to_front\">将自动添加的网页源线路排在订阅源的前面</string>\r\n    <string name=\"ui_refresh_all\">刷新全部</string>\r\n    <string name=\"ui_add_other_iptv_sources\">添加其他订阅源</string>\r\n    <string name=\"ui_convert_js\">转换JS</string>\r\n    <string name=\"ui_iptv_source_info\">%1$d个分组,%2$d个频道,%3$d条源</string>\r\n    <string name=\"ui_time_unit_seconds\">秒</string>\r\n    <string name=\"ui_time_unit_minutes\">分钟</string>\r\n    <string name=\"ui_time_unit_hours\">小时</string>\r\n    <string name=\"ui_time_unit_days\">天</string>\r\n    <string name=\"ui_Density_Ratio_adaptive\">自适应</string>\r\n    <string name=\"ui_control_up\">上键/上滑</string>\r\n    <string name=\"ui_control_down\">下键/下滑</string>\r\n    <string name=\"ui_control_left\">左键/左滑</string>\r\n    <string name=\"ui_control_right\">右键/右滑</string>\r\n    <string name=\"ui_control_select\">选择键</string>\r\n    <string name=\"ui_control_long_select\">长按选择键</string>\r\n    <string name=\"ui_control_long_up\">长按上键</string>\r\n    <string name=\"ui_control_long_down\">长按下键</string>\r\n    <string name=\"ui_control_long_left\">长按左键</string>\r\n    <string name=\"ui_control_long_right\">长按右键</string>\r\n    <string name=\"ui_time_show_hidden\">不显示时间</string>\r\n    <string name=\"ui_time_show_always\">总是显示时间</string>\r\n    <string name=\"ui_time_show_every_hour\">整点前后%s秒显示时间</string>\r\n    <string name=\"ui_time_show_half_hour\">半点前后%s秒显示时间</string>\r\n    <string name=\"ui_video_player_subtitle_use_system_style\">使用系统样式</string>\r\n    <string name=\"ui_video_player_subtitle_use_system_style_desc\">使用Android系统（设置-无障碍）中设置的字体样式</string>\r\n    <string name=\"ui_video_player_subtitle_follow_embedded_style\">跟随源嵌入样式</string>\r\n    <string name=\"ui_video_player_subtitle_follow_embedded_style_desc\">使用视频源中嵌入的字幕样式</string>\r\n    <string name=\"ui_video_player_subtitle_foreground_color\">字体颜色</string>\r\n    <string name=\"ui_video_player_subtitle_background_color\">背景颜色</string>\r\n    <string name=\"ui_video_player_subtitle_edge_color\">边框颜色</string>\r\n    <string name=\"ui_video_player_subtitle_window_color\">窗口颜色</string>\r\n    <string name=\"ui_video_player_subtitle_text_size\">字体大小</string>\r\n    <string name=\"ui_video_player_subtitle_example\">示例字幕</string>\r\n    <string name=\"ui_video_player_core_media3_desc\">除RTSP单播以外基本支持全部功能</string>\r\n    <string name=\"ui_video_player_core_ijk_desc\">部分视频（如加密的dash）可能无法正常使用</string>\r\n    <string name=\"ui_video_player_webview_core_system_desc\">系统自带内核</string>\r\n    <string name=\"ui_video_player_webview_core_x5_desc\">腾讯X5内核,仅支持armv7和arm64架构，第一次使用时需要初始化下载</string>\r\n    <string name=\"ui_about_app_id\">应用标识</string>\r\n    <string name=\"ui_about_repo\">本项目代码仓库</string>\r\n    <string name=\"ui_about_repo_qrcode_desc\">扫码前往代码仓库</string>\r\n    <string name=\"ui_about_telegram\">讨论交流 Telegram</string>\r\n    <string name=\"ui_about_device_name\">设备名称</string>\r\n    <string name=\"ui_about_device_id\">设备ID</string>\r\n    <string name=\"ui_about_origin_repo\">天光云影仓库</string>\r\n    <string name=\"ui_about_origin_repo_qrcode_desc\">本项目的诞生离不开天光云影项目，扫码以前往</string>\r\n    <string name=\"ui_about_origin_reward\">向天光云影作者赞赏</string>\r\n    <string name=\"ui_about_origin_reward_support\">仅支持微信赞赏码</string>\r\n    <string name=\"ui_about_check_update\">检查更新</string>\r\n    <string name=\"ui_about_update_new\">新版本: %1$s</string>\r\n    <string name=\"ui_about_update_none\">无更新</string>\r\n    <string name=\"ui_update_latest_version\">最新版本: v%1$s</string>\r\n    <string name=\"ui_update_updating\">更新中，请勿关闭页面</string>\r\n    <string name=\"ui_update_now\">立即更新</string>\r\n    <string name=\"ui_update_ignore_and_back\">忽略并返回</string>\r\n    <string name=\"ui_update_is_latest\">当前为最新版本（点击以返回）</string>\r\n    <string name=\"ui_channel_count\">共%d个频道</string>\r\n    <string name=\"ui_hybrid_mode_disable\">禁用</string>\r\n    <string name=\"ui_hybrid_mode_to_back\">订阅源优先</string>\r\n    <string name=\"ui_hybrid_mode_to_front\">网页源优先</string>\r\n    <string name=\"ui_time_shows_hidden\">隐藏</string>\r\n    <string name=\"ui_time_shows_always\">常显</string>\r\n    <string name=\"ui_time_shows_every_hour\">整点</string>\r\n    <string name=\"ui_time_shows_half_hour\">半点</string>\r\n    <string name=\"ui_keydown_action_previous_channel\">前一频道</string>\r\n    <string name=\"ui_keydown_action_next_channel\">后一频道</string>\r\n    <string name=\"ui_keydown_action_previous_line\">前一线路</string>\r\n    <string name=\"ui_keydown_action_next_line\">后一线路</string>\r\n    <string name=\"ui_keydown_action_manage_sources\">管理订阅源</string>\r\n    <string name=\"ui_keydown_action_channel_list\">频道列表</string>\r\n    <string name=\"ui_keydown_action_quick_settings\">快捷设置</string>\r\n    <string name=\"ui_keydown_action_program_list\">节目单</string>\r\n    <string name=\"ui_keydown_action_line_list\">线路列表</string>\r\n    <string name=\"ui_keydown_action_playback_control\">播放控制</string>\r\n    <string name=\"ui_x5_core_preload_success\">X5内核加载成功,重启应用以生效</string>\r\n    <string name=\"ui_x5_core_preload_failure\">X5WebView内核加载失败</string>\r\n    <string name=\"ui_x5_core_preload_downloading\">正在远程下载X5Core，下载完成前请不要关闭应用</string>\r\n    <string name=\"ui_x5_core_preload_download_success\">下载X5Core成功！</string>\r\n    <string name=\"ui_x5_core_preload_download_failure\">获取X5Core失败，请使用系统WebView内核</string>\r\n    <string name=\"ui_x5_core_preload_arch_not_supported\">X5不支持架构</string>\r\n    <string name=\"ui_channel_info_now_replay\">正在回放</string>\r\n    <string name=\"ui_channel_info_next_play\">稍后播放</string>\r\n    <string name=\"ui_channel_info_now_play\">正在播放</string>\r\n    <string name=\"ui_hybrid_type_cctv\">央视网</string>\r\n    <string name=\"ui_hybrid_type_yangshipin\">央视频</string>\r\n    <string name=\"ui_hybrid_type_official_site\">官网</string>\r\n    <string name=\"ui_hybrid_type_webview\">其它</string>\r\n    <string name=\"ui_hybrid_type\">网页</string>\r\n    <string name=\"ui_hybrid_type_unknown\">未知</string>\r\n    <string name=\"ui_channel_info_reserved\">已预约</string>\r\n    <string name=\"ui_channel_info_reserve\">预约</string>\r\n    <string name=\"ui_channel_info_timeout\">超时</string>\r\n    <string name=\"iptv_source_local\">本地</string>\r\n    <string name=\"iptv_source_remote\">远程</string>\r\n    <string name=\"ui_epg_item_today\">今天</string>\r\n    <string name=\"ui_epg_item_tomorrow\">明天</string>\r\n    <string name=\"ui_epg_item_day_after_tomorrow\">后天</string>\r\n    <string name=\"ui_x5_core_preload_not_supported\">X5内核不可用，将进行初始化。已切换为系统内核</string>\r\n    <string name=\"ui_player_view_hls_allow_chunkless_preparation\">HLS 允许无块准备</string>\r\n    <string name=\"ui_player_view_hls_allow_chunkless_preparation_desc\">允许Media3在播放 HLS 视频时启用无块准备以减少缓冲时间，启用此选项可能导致无法检测到嵌入的字幕</string>\r\n    <string name=\"ui_video_player_track_closed\">关闭</string>\r\n    <string name=\"ui_settings_language\">语言</string>\r\n    <string name=\"ui_settings_language_desc\">选择应用界面语言</string>\r\n    <string name=\"ui_settings_language_selected_tip\">请重启应用以生效</string>\r\n    <string name=\"iptv_channel_recent_enable\">启用最近观看</string>\r\n    <string name=\"iptv_channel_recent_enable_desc\">是否显示最近观看的频道列表</string>\r\n    <string name=\"ui_replace_system_webview\">替换系统WebView</string>\r\n    <string name=\"ui_replace_system_webview_desc\">使用下包名为com.google.android.webview的应用替换系统WebView内核（重启生效）</string>\r\n    <string name=\"ui_player_view_fit_frame_rate\">适配视频内容帧率</string>\r\n    <string name=\"ui_player_view_fit_frame_rate_desc\">启用后，播放器将尝试适配视频内容的帧率\\n需要启用系统设置和渲染模式为SufaceView，系统版本大于 11\\n在切换期间，你可能会观察到屏幕闪烁，部分视频源可能不适用或出现问题</string>\r\n    <string name=\"ui_error_clipboard_service_unavailable\">剪贴板服务不可用</string>\r\n    <string name=\"ui_dashboard_module_list_title\">导航</string>\r\n    <string name=\"ui_channel_view_webview_player\">WebView</string>\r\n    <string name=\"ui_webview_load_timeout\">WebView 加载超时</string>\r\n    <string name=\"ui_webview_load_timeout_desc\">WebView加载超时时间（秒）</string>\r\n    <string name=\"ui_video_player_core_vlc_desc\">VLC播放器，支持更多的字幕格式</string>\r\n</resources>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/res/values/strings.xml b/tv/src/main/res/values/strings.xml
--- a/tv/src/main/res/values/strings.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/res/values/strings.xml	(date 1756691200127)
@@ -229,6 +229,7 @@
     <string name="ui_player_view_extract_header_from_link">在链接中提取 Header</string>
     <string name="ui_player_view_extract_header_from_link_desc">支持在链接中提取 以|分隔的 Header 信息</string>
     <string name="ui_player_view_display_mode">全局显示模式</string>
+    <string name="ui_player_view_playback_mode">回放方式</string>
     <string name="ui_player_view_webview_core">WebView内核</string>
     <string name="ui_player_view_load_timeout">加载超时</string>
     <string name="ui_player_view_load_timeout_desc">影响超时换源、断线重连</string>
Index: .idea/inspectionProfiles/Project_Default.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><component name=\"InspectionProjectProfileManager\">\r\n  <profile version=\"1.0\">\r\n    <option name=\"myName\" value=\"Project Default\" />\r\n    <inspection_tool class=\"AndroidLintUnsafeImplicitIntentLaunch\" enabled=\"false\" level=\"ERROR\" enabled_by_default=\"false\" />\r\n    <inspection_tool class=\"ComposePreviewDimensionRespectsLimit\" enabled=\"true\" level=\"WARNING\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"ComposePreviewMustBeTopLevelFunction\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"ComposePreviewNeedsComposableAnnotation\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"ComposePreviewNotSupportedInUnitTestFiles\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"GlancePreviewDimensionRespectsLimit\" enabled=\"true\" level=\"WARNING\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"GlancePreviewMustBeTopLevelFunction\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"GlancePreviewNeedsComposableAnnotation\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"GlancePreviewNotSupportedInUnitTestFiles\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewAnnotationInFunctionWithParameters\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewApiLevelMustBeValid\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewDeviceShouldUseNewSpec\" enabled=\"true\" level=\"WEAK WARNING\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewFontScaleMustBeGreaterThanZero\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewMultipleParameterProviders\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n    <inspection_tool class=\"PreviewPickerAnnotation\" enabled=\"true\" level=\"ERROR\" enabled_by_default=\"true\">\r\n      <option name=\"composableFile\" value=\"true\" />\r\n      <option name=\"previewFile\" value=\"true\" />\r\n    </inspection_tool>\r\n  </profile>\r\n</component>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/inspectionProfiles/Project_Default.xml b/.idea/inspectionProfiles/Project_Default.xml
--- a/.idea/inspectionProfiles/Project_Default.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/.idea/inspectionProfiles/Project_Default.xml	(date 1756691200050)
@@ -50,6 +50,10 @@
       <option name="composableFile" value="true" />
       <option name="previewFile" value="true" />
     </inspection_tool>
+    <inspection_tool class="PreviewParameterProviderOnFirstParameter" enabled="true" level="ERROR" enabled_by_default="true">
+      <option name="composableFile" value="true" />
+      <option name="previewFile" value="true" />
+    </inspection_tool>
     <inspection_tool class="PreviewPickerAnnotation" enabled="true" level="ERROR" enabled_by_default="true">
       <option name="composableFile" value="true" />
       <option name="previewFile" value="true" />
Index: .idea/gradle.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"GradleSettings\">\r\n    <option name=\"linkedExternalProjectsSettings\">\r\n      <GradleProjectSettings>\r\n        <option name=\"testRunner\" value=\"CHOOSE_PER_TEST\" />\r\n        <option name=\"externalProjectPath\" value=\"$PROJECT_DIR$\" />\r\n        <option name=\"gradleJvm\" value=\"#GRADLE_LOCAL_JAVA_HOME\" />\r\n        <option name=\"modules\">\r\n          <set>\r\n            <option value=\"$PROJECT_DIR$\" />\r\n            <!-- <option value=\"$PROJECT_DIR$/allinone\" /> -->\r\n            <option value=\"$PROJECT_DIR$/core\" />\r\n            <option value=\"$PROJECT_DIR$/core/data\" />\r\n            <option value=\"$PROJECT_DIR$/core/designsystem\" />\r\n            <option value=\"$PROJECT_DIR$/core/util\" />\r\n            <option value=\"$PROJECT_DIR$/ijkplayer-java\" />\r\n            <option value=\"$PROJECT_DIR$/tv\" />\r\n            <option value=\"$PROJECT_DIR$/webview\" />\r\n          </set>\r\n        </option>\r\n        <option name=\"resolveExternalAnnotations\" value=\"false\" />\r\n      </GradleProjectSettings>\r\n    </option>\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/gradle.xml b/.idea/gradle.xml
--- a/.idea/gradle.xml	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/.idea/gradle.xml	(date 1756691200049)
@@ -1,5 +1,6 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <project version="4">
+  <component name="GradleMigrationSettings" migrationVersion="1" />
   <component name="GradleSettings">
     <option name="linkedExternalProjectsSettings">
       <GradleProjectSettings>
@@ -9,7 +10,6 @@
         <option name="modules">
           <set>
             <option value="$PROJECT_DIR$" />
-            <!-- <option value="$PROJECT_DIR$/allinone" /> -->
             <option value="$PROJECT_DIR$/core" />
             <option value="$PROJECT_DIR$/core/data" />
             <option value="$PROJECT_DIR$/core/designsystem" />
@@ -19,7 +19,6 @@
             <option value="$PROJECT_DIR$/webview" />
           </set>
         </option>
-        <option name="resolveExternalAnnotations" value="false" />
       </GradleProjectSettings>
     </option>
   </component>
Index: build.gradle.kts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import com.android.build.gradle.internal.api.BaseVariantOutputImpl\r\nimport com.android.build.gradle.internal.dsl.BaseAppModuleExtension\r\nimport com.android.build.gradle.tasks.PackageAndroidArtifact\r\nimport java.io.FileInputStream\r\nimport java.util.Properties\r\n\r\n// Top-level build file where you can add configuration options common to all sub-projects/modules.\r\nplugins {\r\n    alias(libs.plugins.android.application) apply false\r\n    alias(libs.plugins.android.library) apply false\r\n    alias(libs.plugins.kotlin.android) apply false\r\n    alias(libs.plugins.compose) apply false\r\n    alias(libs.plugins.ksp) apply false\r\n    alias(libs.plugins.kotlin.serialization) apply false\r\n}\r\n\r\nval keystorePropertiesFile = rootProject.file(\"signing.properties\")\r\nval keystoreProperties = Properties()\r\nif (keystorePropertiesFile.exists()) {\r\n    keystoreProperties.load(FileInputStream(keystorePropertiesFile))\r\n}\r\n\r\nallprojects {\r\n    val appConfig: BaseAppModuleExtension.() -> Unit = {\r\n        signingConfigs {\r\n            val localKeystore = rootProject.file(\"keystore.jks\")\r\n            val userKeystore = file(\r\n                System.getenv(\"KEYSTORE\") ?: keystoreProperties.getProperty(\"storeFile\")\r\n                ?: \"keystore.jks\"\r\n            )\r\n\r\n            create(\"release\") {\r\n                storeFile = if (userKeystore.exists()) userKeystore else localKeystore\r\n                storePassword = System.getenv(\"KEYSTORE_PASSWORD\")\r\n                    ?: keystoreProperties.getProperty(\"storePassword\")\r\n                keyAlias = System.getenv(\"KEY_ALIAS\") ?: keystoreProperties.getProperty(\"keyAlias\")\r\n                keyPassword =\r\n                    System.getenv(\"KEY_PASSWORD\") ?: keystoreProperties.getProperty(\"keyPassword\")\r\n            }\r\n        }\r\n\r\n        applicationVariants.all {\r\n            outputs.all {\r\n                val ver = defaultConfig.versionName\r\n                val minSdk = defaultConfig.minSdk\r\n                val abi = filters.find { it.filterType == \"ABI\" }?.identifier ?: \"all\"\r\n                val flavor = productFlavors.joinToString(\"-\") { it.name }\r\n\r\n                (this as BaseVariantOutputImpl).outputFileName =\r\n                    \"mytv-android-${project.name}-$ver-${abi}-sdk$minSdk-${flavor}.apk\"\r\n            }\r\n        }\r\n    }\r\n\r\n    extra[\"appConfig\"] = appConfig\r\n\r\n    tasks.withType<PackageAndroidArtifact>().configureEach {\r\n        doLast {\r\n            val outputDir = outputDirectory.get().asFile\r\n            val targetDir = file(\"$outputDir/../../release\")\r\n\r\n            if (!targetDir.exists()) targetDir.mkdirs()\r\n\r\n            outputDir.listFiles { file -> file.extension == \"apk\"}?.forEach { apkFile ->\r\n                apkFile.copyTo(file(\"${targetDir}/${apkFile.name}\"), overwrite = true)\r\n            }\r\n            outputDir.listFiles { file -> file.extension == \"json\" }?.forEach { jsonFile ->\r\n                jsonFile.copyTo(file(\"${targetDir}/${jsonFile.name}\"), overwrite = true)\r\n            }\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/build.gradle.kts b/build.gradle.kts
--- a/build.gradle.kts	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/build.gradle.kts	(date 1756691200089)
@@ -31,11 +31,9 @@
 
             create("release") {
                 storeFile = if (userKeystore.exists()) userKeystore else localKeystore
-                storePassword = System.getenv("KEYSTORE_PASSWORD")
-                    ?: keystoreProperties.getProperty("storePassword")
-                keyAlias = System.getenv("KEY_ALIAS") ?: keystoreProperties.getProperty("keyAlias")
-                keyPassword =
-                    System.getenv("KEY_PASSWORD") ?: keystoreProperties.getProperty("keyPassword")
+                storePassword = "123456"
+                keyAlias = "myalias"
+                keyPassword = "123456"
             }
         }
 
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/subcategories/SettingsVideoPlayerPlaybackModeScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/subcategories/SettingsVideoPlayerPlaybackModeScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/subcategories/SettingsVideoPlayerPlaybackModeScreen.kt
new file mode 100644
--- /dev/null	(date 1756691200114)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/subcategories/SettingsVideoPlayerPlaybackModeScreen.kt	(date 1756691200114)
@@ -0,0 +1,72 @@
+package top.yogiczy.mytv.tv.ui.screen.settings.subcategories
+
+import androidx.compose.foundation.layout.Arrangement
+import androidx.compose.foundation.layout.padding
+import androidx.compose.foundation.lazy.grid.GridCells
+import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
+import androidx.compose.foundation.lazy.grid.items
+import androidx.compose.runtime.Composable
+import androidx.compose.ui.Modifier
+import androidx.compose.ui.res.stringResource
+import androidx.compose.ui.tooling.preview.Preview
+import androidx.compose.ui.unit.dp
+import androidx.tv.material3.ListItem
+import androidx.tv.material3.RadioButton
+import androidx.tv.material3.Text
+import top.yogiczy.mytv.tv.R
+import top.yogiczy.mytv.tv.ui.rememberChildPadding
+import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
+import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
+import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
+import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents
+
+@Composable
+fun SettingsVideoPlayerPlaybackModeScreen(
+    modifier: Modifier = Modifier,
+    playbackModeProvider: () -> VideoPlayerPlaybackMode = { VideoPlayerPlaybackMode.RELOAD_URL },
+    onPlaybackModeChanged: (VideoPlayerPlaybackMode) -> Unit = {},
+    onBackPressed: () -> Unit = {},
+) {
+    val currentPlaybackMode = playbackModeProvider()
+    val childPadding = rememberChildPadding()
+
+    AppScreen(
+        modifier = modifier.padding(top = 10.dp),
+        header = { Text("${stringResource(R.string.ui_dashboard_module_settings)} / ${stringResource(R.string.ui_channel_view_player)} / ${stringResource(R.string.ui_player_view_playback_mode)}") },
+        canBack = true,
+        onBackPressed = onBackPressed,
+    ) {
+        LazyVerticalGrid(
+            modifier = Modifier,
+            columns = GridCells.Fixed(2),
+            contentPadding = childPadding.copy(top = 10.dp).paddingValues,
+            horizontalArrangement = Arrangement.spacedBy(12.dp),
+            verticalArrangement = Arrangement.spacedBy(10.dp),
+        ) {
+            items(VideoPlayerPlaybackMode.entries) { mode ->
+                ListItem(
+                    modifier = Modifier.handleKeyEvents(
+                        onSelect = { onPlaybackModeChanged(mode) },
+                    ),
+                    selected = mode == currentPlaybackMode,
+                    onClick = {},
+                    headlineContent = { Text(mode.label) },
+                    trailingContent = {
+                        RadioButton(
+                            selected = mode == currentPlaybackMode,
+                            onClick = {},
+                        )
+                    },
+                )
+            }
+        }
+    }
+}
+
+@Preview
+@Composable
+private fun SettingsVideoPlayerPlaybackModeScreenPreview() {
+    MyTvTheme {
+        SettingsVideoPlayerPlaybackModeScreen()
+    }
+}
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsCategoriesScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.settings\r\n\r\nimport androidx.compose.animation.core.animateFloatAsState\r\nimport androidx.compose.animation.core.LinearEasing\r\nimport androidx.compose.animation.core.RepeatMode\r\nimport androidx.compose.animation.core.animateFloat\r\nimport androidx.compose.animation.core.infiniteRepeatable\r\nimport androidx.compose.animation.core.rememberInfiniteTransition\r\nimport androidx.compose.animation.core.tween\r\nimport androidx.compose.foundation.background\r\nimport androidx.compose.foundation.layout.Arrangement\r\nimport androidx.compose.foundation.layout.Box\r\nimport androidx.compose.foundation.layout.Column\r\nimport androidx.compose.foundation.layout.Spacer\r\nimport androidx.compose.foundation.layout.fillMaxSize\r\nimport androidx.compose.foundation.layout.height\r\nimport androidx.compose.foundation.layout.padding\r\nimport androidx.compose.foundation.layout.size\r\nimport androidx.compose.foundation.lazy.grid.GridCells\r\nimport androidx.compose.foundation.lazy.grid.LazyVerticalGrid\r\nimport androidx.compose.foundation.lazy.grid.items\r\nimport androidx.compose.foundation.shape.CircleShape\r\nimport androidx.compose.material.icons.Icons\r\nimport androidx.compose.material.icons.automirrored.outlined.FormatListBulleted\r\nimport androidx.compose.material.icons.automirrored.outlined.LibraryBooks\r\nimport androidx.compose.material.icons.outlined.BugReport\r\nimport androidx.compose.material.icons.outlined.CloudSync\r\nimport androidx.compose.material.icons.outlined.ColorLens\r\nimport androidx.compose.material.icons.outlined.ControlCamera\r\nimport androidx.compose.material.icons.outlined.Devices\r\nimport androidx.compose.material.icons.outlined.DisplaySettings\r\nimport androidx.compose.material.icons.outlined.Subscriptions\r\nimport androidx.compose.material.icons.outlined.Shield\r\nimport androidx.compose.material.icons.outlined.PlayCircle\r\nimport androidx.compose.material.icons.outlined.SystemUpdate\r\nimport androidx.compose.material.icons.outlined.Wifi\r\nimport androidx.compose.material.icons.outlined.Web\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Alignment\r\nimport androidx.compose.ui.draw.clip\r\nimport androidx.compose.ui.draw.scale\r\nimport androidx.compose.ui.ExperimentalComposeUiApi\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.focus.FocusRequester\r\nimport androidx.compose.ui.focus.focusRequester\r\nimport androidx.compose.ui.focus.onFocusChanged\r\nimport androidx.compose.ui.geometry.Offset\r\nimport androidx.compose.ui.graphics.Brush\r\nimport androidx.compose.ui.graphics.Color\r\nimport androidx.compose.ui.graphics.vector.ImageVector\r\nimport androidx.compose.ui.layout.onSizeChanged\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.compose.ui.unit.IntSize\r\nimport androidx.compose.ui.unit.dp\r\nimport androidx.tv.material3.ClickableSurfaceDefaults\r\nimport androidx.tv.material3.Icon\r\nimport androidx.tv.material3.MaterialTheme\r\nimport androidx.tv.material3.Surface\r\nimport androidx.tv.material3.Text\r\nimport top.yogiczy.mytv.tv.ui.rememberChildPadding\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppScreen\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids\r\nimport top.yogiczy.mytv.tv.ui.utils.focusOnLaunched\r\nimport top.yogiczy.mytv.tv.ui.utils.gridColumns\r\nimport top.yogiczy.mytv.tv.ui.utils.handleKeyEvents\r\nimport top.yogiczy.mytv.tv.ui.utils.ifElse\r\nimport top.yogiczy.mytv.tv.ui.utils.requestFocusRestorer\r\nimport top.yogiczy.mytv.tv.R\r\nimport androidx.compose.ui.res.stringResource\r\n\r\n@OptIn(ExperimentalComposeUiApi::class)\r\n@Composable\r\nfun SettingsCategoriesScreen(\r\n    modifier: Modifier = Modifier,\r\n    toSettingsCategoryScreen: (category: SettingsCategories) -> Unit = {},\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    val childPadding = rememberChildPadding()\r\n\r\n    AppScreen(\r\n        modifier = modifier\r\n            .padding(top = 10.dp)\r\n            .focusOnLaunched(),\r\n        header = { Text(stringResource(R.string.ui_dashboard_module_settings)) },\r\n        canBack = true,\r\n        onBackPressed = onBackPressed,\r\n    ) {\r\n        val firstItemFocusRequester = remember { FocusRequester() }\r\n\r\n        LazyVerticalGrid(\r\n            modifier = Modifier.ifElse(\r\n                settingsVM.uiFocusOptimize,\r\n                Modifier.requestFocusRestorer { firstItemFocusRequester },\r\n            ),\r\n            columns = GridCells.Fixed(6),\r\n            contentPadding = childPadding.copy(top = 10.dp).paddingValues,\r\n            verticalArrangement = Arrangement.spacedBy(20.dp),\r\n            horizontalArrangement = Arrangement.spacedBy(20.dp),\r\n        ) {\r\n            items(SettingsCategories.entries) {\r\n                SettingsCategoryItem(\r\n                    modifier = Modifier.ifElse(\r\n                        it == SettingsCategories.entries.first(),\r\n                        Modifier.focusRequester(firstItemFocusRequester)\r\n                    ),\r\n                    title = it.title,\r\n                    imageVector = it.icon,\r\n                    onSelected = { toSettingsCategoryScreen(it) },\r\n                )\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@Composable\r\nprivate fun SettingsCategoryItem(\r\n    modifier: Modifier = Modifier,\r\n    title: Int,\r\n    imageVector: ImageVector,\r\n    onSelected: () -> Unit = {},\r\n) {\r\n    var isFocused by remember { mutableStateOf(false) }\r\n    val scale by animateFloatAsState(\r\n        targetValue = if (isFocused) 1.05f else 1.0f,\r\n        label = \"scale\"\r\n    )\r\n    Column(\r\n        horizontalAlignment = Alignment.CenterHorizontally,\r\n        verticalArrangement = Arrangement.Center,\r\n    ) {\r\n        Surface(\r\n            modifier = modifier\r\n                .scale(scale)\r\n                .size(1.5f.gridColumns()).clip(CircleShape)\r\n                .onFocusChanged { isFocused = it.isFocused || it.hasFocus }\r\n                .handleKeyEvents(onSelect = onSelected),\r\n            colors = ClickableSurfaceDefaults.colors(\r\n                containerColor = MaterialTheme.colorScheme.onSurface.copy(0.1f),\r\n            ),\r\n            onClick = {},\r\n        ) {\r\n            if (title == SettingsCategories.THEME.title && isFocused) {\r\n                SettingsCategoryItemThemeBackground()\r\n            }\r\n            Icon(\r\n                imageVector, \r\n                contentDescription = null, \r\n                modifier = Modifier.size(45.dp).align(Alignment.Center),\r\n            )\r\n        }\r\n        Text(\r\n            stringResource(title), \r\n            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),\r\n            style = MaterialTheme.typography.titleLarge\r\n        )\r\n    }\r\n}\r\n\r\n@Composable\r\nprivate fun SettingsCategoryItemThemeBackground() {\r\n    val colors = listOf(\r\n        Color(0xFFE8FBFC),\r\n        Color(0xFFB3EAFD),\r\n        Color(0xFFBED6FF),\r\n        Color(0xFFD7CFFE),\r\n        Color(0xFFE8CEF2),\r\n        Color(0xFFFBCFDB),\r\n        Color(0xFFFFECC0),\r\n    )\r\n\r\n    val infiniteTransition = rememberInfiniteTransition(label = \"\")\r\n    val animatedOffset = infiniteTransition.animateFloat(\r\n        initialValue = 0f,\r\n        targetValue = 1f,\r\n        animationSpec = infiniteRepeatable(\r\n            animation = tween(durationMillis = 3000, easing = LinearEasing),\r\n            repeatMode = RepeatMode.Reverse\r\n        ),\r\n        label = \"\",\r\n    )\r\n\r\n    var size by remember { mutableStateOf(IntSize(0, 0)) }\r\n\r\n    Box(\r\n        modifier = Modifier\r\n            .fillMaxSize()\r\n            .onSizeChanged { size = it }\r\n            .background(\r\n                Brush.linearGradient(\r\n                    colors = colors,\r\n                    start = Offset(\r\n                        -animatedOffset.value * size.width / 2,\r\n                        -animatedOffset.value * size.height / 2\r\n                    ),\r\n                    end = Offset(\r\n                        -animatedOffset.value * size.width + size.width * 2,\r\n                        -animatedOffset.value * size.height + size.height * 2\r\n                    ),\r\n                )\r\n            )\r\n    )\r\n}\r\n\r\nenum class SettingsCategories(\r\n    val icon: ImageVector,\r\n    val title: Int,\r\n) {\r\n    APP(Icons.Outlined.Devices, R.string.ui_channel_view_general),\r\n    IPTV(Icons.Outlined.Subscriptions, R.string.ui_channel_view_source),\r\n    EPG(Icons.AutoMirrored.Outlined.LibraryBooks, R.string.ui_channel_view_epg),\r\n    UI(Icons.Outlined.DisplaySettings, R.string.ui_channel_view_interface),\r\n    THEME(Icons.Outlined.ColorLens, R.string.ui_channel_view_theme),\r\n    CONTROL(Icons.Outlined.ControlCamera, R.string.ui_channel_view_control),\r\n    VIDEO_PLAYER(Icons.Outlined.PlayCircle, R.string.ui_channel_view_player),\r\n    WEBVIEW_PLAYER(Icons.Outlined.Web, R.string.ui_channel_view_webview_player),\r\n    UPDATE(Icons.Outlined.SystemUpdate, R.string.ui_channel_view_update),\r\n    // FAVORITE(Icons.Outlined.FavoriteBorder, \"收藏\"),\r\n    // EPG_RESERVE(Icons.Default.BookmarkBorder, \"预约\"),\r\n    NETWORK(Icons.Outlined.Wifi, R.string.ui_channel_view_network),\r\n    CLOUD_SYNC(Icons.Outlined.CloudSync, R.string.ui_channel_view_cloud_sync),\r\n    PERMISSIONS(Icons.Outlined.Shield, R.string.ui_channel_view_permissions),\r\n    DEBUG(Icons.Outlined.BugReport, R.string.ui_channel_view_debug),\r\n    LOG(Icons.AutoMirrored.Outlined.FormatListBulleted, R.string.ui_channel_view_log),\r\n}\r\n\r\nenum class SettingsSubCategories {\r\n    IPTV_SOURCE,\r\n    APP_LANGUAGE,\r\n    IPTV_SOURCE_CACHE_TIME,\r\n    CHANNEL_GROUP_VISIBILITY,\r\n    IPTV_HYBRID_MODE,\r\n    EPG_SOURCE,\r\n    EPG_REFRESH_TIME_THRESHOLD,\r\n    UI_CONTROL_ACTION,\r\n    UI_TIME_SHOW_MODE,\r\n    UI_SCREEN_AUTO_CLOSE_DELAY,\r\n    UI_DENSITY_SCALE_RATIO,\r\n    UI_FONT_SCALE_RATIO,\r\n    UI_VIDEO_PLAYER_SUBTITLE,\r\n    VIDEO_PLAYER_CORE,\r\n    WEBVIEW_CORE,\r\n    WEBVIEW_PLAYER_LOAD_TIMEOUT,\r\n    VIDEO_PLAYER_RENDER_MODE,\r\n    VIDEO_PLAYER_RTSP_TRANSPORT,\r\n    VIDEO_PLAYER_DISPLAY_MODE,\r\n    VIDEO_PLAYER_LOAD_TIMEOUT,\r\n    VIDEO_PLAYER_BUFFER_TIME,\r\n    UPDATE_CHANNEL,\r\n    CLOUD_SYNC_PROVIDER,\r\n    UI_SETTINGS_DECODER_INFO,\r\n    NETWORK_RETRY_COUNT,\r\n    NETWORK_RETRY_INTERVAL\r\n}\r\n\r\n@Preview(device = \"id:Android TV (720p)\")\r\n@Composable\r\nprivate fun SettingsCategoriesScreenPreview() {\r\n    MyTvTheme {\r\n        SettingsCategoriesScreen()\r\n        PreviewWithLayoutGrids { }\r\n    }\r\n}\r\n\r\n@Preview\r\n@Composable\r\nprivate fun SettingsCategoryItemThemePreview() {\r\n    MyTvTheme {\r\n        SettingsCategoryItem(\r\n            modifier = Modifier\r\n                .focusOnLaunched()\r\n                .padding(20.dp),\r\n            title = SettingsCategories.THEME.title,\r\n            imageVector = SettingsCategories.THEME.icon,\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsCategoriesScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsCategoriesScreen.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsCategoriesScreen.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsCategoriesScreen.kt	(date 1756691200103)
@@ -248,6 +248,7 @@
     VIDEO_PLAYER_RENDER_MODE,
     VIDEO_PLAYER_RTSP_TRANSPORT,
     VIDEO_PLAYER_DISPLAY_MODE,
+    VIDEO_PLAYER_PLAYBACK_MODE,
     VIDEO_PLAYER_LOAD_TIMEOUT,
     VIDEO_PLAYER_BUFFER_TIME,
     UPDATE_CHANNEL,
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsScreen.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.settings\r\n\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsNetworkRetryIntervalScreen\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.LaunchedEffect\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.rememberCoroutineScope\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.navigation.compose.NavHost\r\nimport androidx.navigation.compose.composable\r\nimport androidx.navigation.compose.rememberNavController\r\nimport kotlinx.coroutines.delay\r\nimport kotlinx.coroutines.launch\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.repositories.epg.EpgRepository\r\nimport top.yogiczy.mytv.core.data.repositories.iptv.IptvRepository\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsAppScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsCloudSyncScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsControlScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsDebugScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsEpgScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsIptvScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsLogScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsNetworkScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsPermissionsScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsThemeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsUiScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsUpdateScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsVideoPlayerScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.categories.SettingsWebViewPlayerScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsChannelGroupVisibilityScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsCloudSyncProviderScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsDecoderInfoScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsEpgRefreshTimeThresholdScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsEpgSourceScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsIptvHybridModeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiControlSettingScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsIptvSourceCacheTimeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsIptvSourceScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsLanguageScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsNetworkRetryCountScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiDensityScaleRatioScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiFontScaleRatioScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiScreenAutoCloseScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiTimeShowModeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUiVideoPlayerSubtitleSettingsScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsUpdateChannelScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerCoreScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsWebViewCoreScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerDisplayModeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerLoadTimeoutScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerBufferTimeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerRenderModeScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerRtspTransportScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsWebViewPlayerLoadTimeoutScreen\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport top.yogiczy.mytv.tv.ui.utils.navigateSingleTop\r\nimport top.yogiczy.mytv.tv.R\r\nimport androidx.compose.ui.platform.LocalContext\r\n\r\nobject SettingsScreen {\r\n    const val START_DESTINATION = \"startDestination\"\r\n}\r\n\r\n@Composable\r\nfun SettingsScreen(\r\n    modifier: Modifier = Modifier,\r\n    startDestinationProvider: () -> String? = { null },\r\n    channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n    onCheckUpdate: () -> Unit = {},\r\n    onReload: () -> Unit = {},\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    val coroutineScope = rememberCoroutineScope()\r\n\r\n    LaunchedEffect(Unit) {\r\n        while (true) {\r\n            settingsViewModel.refresh()\r\n            delay(1000)\r\n        }\r\n    }\r\n    val context = LocalContext.current\r\n    val navController = rememberNavController()\r\n\r\n    AppScreen(modifier = modifier, onBackPressed = onBackPressed) {\r\n        NavHost(\r\n            navController = navController,\r\n            startDestination = startDestinationProvider() ?: \"categories\",\r\n            builder = {\r\n                composable(route = \"categories\") {\r\n                    SettingsCategoriesScreen(\r\n                        toSettingsCategoryScreen = { navController.navigateSingleTop(it.name) },\r\n                        onBackPressed = onBackPressed,\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.APP.name) {\r\n                    SettingsAppScreen(\r\n                        toLanguageScreen  = { navController.navigateSingleTop(SettingsSubCategories.APP_LANGUAGE.name) },\r\n                        onReload = onReload,\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.IPTV.name) {\r\n                    SettingsIptvScreen(\r\n                        channelGroupListProvider = channelGroupListProvider,\r\n                        toIptvSourceScreen = { navController.navigateSingleTop(SettingsSubCategories.IPTV_SOURCE.name) },\r\n                        toIptvSourceCacheTimeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.IPTV_SOURCE_CACHE_TIME.name)\r\n                        },\r\n                        toChannelGroupVisibilityScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.CHANNEL_GROUP_VISIBILITY.name)\r\n                        },\r\n                        toIptvHybridModeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.IPTV_HYBRID_MODE.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.EPG.name) {\r\n                    SettingsEpgScreen(\r\n                        toEpgSourceScreen = { navController.navigateSingleTop(SettingsSubCategories.EPG_SOURCE.name) },\r\n                        toEpgRefreshTimeThresholdScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.EPG_REFRESH_TIME_THRESHOLD.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.UI.name) {\r\n                    SettingsUiScreen(\r\n                        toUiTimeShowModeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_TIME_SHOW_MODE.name)\r\n                        },\r\n                        toUiScreenAutoCloseDelayScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_SCREEN_AUTO_CLOSE_DELAY.name)\r\n                        },\r\n                        toUiDensityScaleRatioScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_DENSITY_SCALE_RATIO.name)\r\n                        },\r\n                        toUiFontScaleRatioScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_FONT_SCALE_RATIO.name)\r\n                        },\r\n                        toUiVideoPlayerSubtitleSettingsScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_VIDEO_PLAYER_SUBTITLE.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.NETWORK.name) {\r\n                    SettingsNetworkScreen(\r\n                        settingsViewModel = settingsViewModel,\r\n                        toNetworkRetryCountScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.NETWORK_RETRY_COUNT.name)\r\n                        },\r\n                        toNetworkRetryIntervalScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.NETWORK_RETRY_INTERVAL.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.CONTROL.name) {\r\n                    SettingsControlScreen(\r\n                        settingsViewModel = settingsViewModel,\r\n                        toUiControlActionSettingsScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_CONTROL_ACTION.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.VIDEO_PLAYER.name) {\r\n                    SettingsVideoPlayerScreen(\r\n                        toVideoPlayerCoreScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_CORE.name)\r\n                        },\r\n                        toVideoPlayerRenderModeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_RENDER_MODE.name)\r\n                        },\r\n                        toVideoPlayerRtspTransportScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_RTSP_TRANSPORT.name)\r\n                        },\r\n                        toVideoPlayerDisplayModeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_DISPLAY_MODE.name)\r\n                        },\r\n                        toVideoPlayerLoadTimeoutScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_LOAD_TIMEOUT.name)\r\n                        },\r\n                        toVideoPlayerBufferTimeScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_BUFFER_TIME.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.WEBVIEW_PLAYER.name) {\r\n                    SettingsWebViewPlayerScreen(\r\n                        toWebViewCoreScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.WEBVIEW_CORE.name)\r\n                        },\r\n                        toWebViewLoadTimeoutScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.WEBVIEW_PLAYER_LOAD_TIMEOUT.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.UPDATE.name) {\r\n                    SettingsUpdateScreen(\r\n                        toUpdateChannelScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UPDATE_CHANNEL.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.THEME.name) {\r\n                    SettingsThemeScreen(\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.CLOUD_SYNC.name) {\r\n                    SettingsCloudSyncScreen(\r\n                        toCloudSyncProviderScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.CLOUD_SYNC_PROVIDER.name)\r\n                        },\r\n                        onReload = onReload,\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.DEBUG.name) {\r\n                    SettingsDebugScreen(\r\n                        toUiSettingsDecoderInfoScreen = {\r\n                            navController.navigateSingleTop(SettingsSubCategories.UI_SETTINGS_DECODER_INFO.name)\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.LOG.name) {\r\n                    SettingsLogScreen(\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsCategories.PERMISSIONS.name) {\r\n                    SettingsPermissionsScreen(\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.APP_LANGUAGE.name) {\r\n                    SettingsLanguageScreen(\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }   \r\n\r\n                composable(SettingsSubCategories.IPTV_SOURCE.name) {\r\n                    SettingsIptvSourceScreen(\r\n                        currentIptvSourceProvider = { settingsViewModel.iptvSourceCurrent },\r\n                        iptvSourceListProvider = { settingsViewModel.iptvSourceList },\r\n                        onSetCurrent = {\r\n                            settingsViewModel.iptvSourceCurrent = it\r\n                            settingsViewModel.iptvChannelGroupHiddenList = emptySet()\r\n                            settingsViewModel.iptvChannelLastPlay = Channel.EMPTY\r\n                            onReload()\r\n                        },\r\n                        onDelete = {\r\n                            settingsViewModel.iptvSourceList =\r\n                                IptvSourceList(settingsViewModel.iptvSourceList - it)\r\n                        },\r\n                        onClearCache = {\r\n                            coroutineScope.launch {\r\n                                IptvRepository(it).clearCache()\r\n                                Snackbar.show(\"${context.getString(R.string.ui_cache_cleared)}\")\r\n                            }\r\n                        },\r\n                        refresh = {\r\n                            settingsViewModel.iptvSourceList = Configs.iptvSourceList\r\n                        },\r\n                        onBackPressed = {\r\n                            if (!navController.navigateUp()) onBackPressed()\r\n                        },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.IPTV_SOURCE_CACHE_TIME.name) {\r\n                    SettingsIptvSourceCacheTimeScreen(\r\n                        cacheTimeProvider = { settingsViewModel.iptvSourceCacheTime },\r\n                        onCacheTimeChanged = {\r\n                            settingsViewModel.iptvSourceCacheTime = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_CONTROL_ACTION.name) {\r\n                    SettingsUiControlSettingScreen(\r\n                        keyDownEventUpProvider = { settingsViewModel.keyDownEventUp },\r\n                        onkeyDownEventUpChanged = {\r\n                            settingsViewModel.keyDownEventUp = it\r\n                        },\r\n                        keyDownEventDownProvider = { settingsViewModel.keyDownEventDown },\r\n                        onkeyDownEventDownChanged = {\r\n                            settingsViewModel.keyDownEventDown = it\r\n                        },\r\n                        keyDownEventLeftProvider = { settingsViewModel.keyDownEventLeft },\r\n                        onkeyDownEventLeftChanged = {\r\n                            settingsViewModel.keyDownEventLeft = it\r\n                        },\r\n                        keyDownEventRightProvider = { settingsViewModel.keyDownEventRight },\r\n                        onkeyDownEventRightChanged = {\r\n                            settingsViewModel.keyDownEventRight = it\r\n                        },\r\n                        keyDownEventSelectProvider = { settingsViewModel.keyDownEventSelect },\r\n                        onkeyDownEventSelectChanged = {\r\n                            settingsViewModel.keyDownEventSelect = it\r\n                        },\r\n                        keyDownEventLongSelectProvider = { settingsViewModel.keyDownEventLongSelect },\r\n                        onkeyDownEventLongSelectChanged = {\r\n                            settingsViewModel.keyDownEventLongSelect = it\r\n                        },\r\n                        keyDownEventLongUpProvider = { settingsViewModel.keyDownEventLongUp },\r\n                        onkeyDownEventLongUpChanged = {\r\n                            settingsViewModel.keyDownEventLongUp = it\r\n                        },\r\n                        keyDownEventLongDownProvider = { settingsViewModel.keyDownEventLongDown },\r\n                        onkeyDownEventLongDownChanged = {\r\n                            settingsViewModel.keyDownEventLongDown = it\r\n                        },\r\n                        keyDownEventLongLeftProvider = { settingsViewModel.keyDownEventLongLeft },\r\n                        onkeyDownEventLongLeftChanged = {\r\n                            settingsViewModel.keyDownEventLongLeft = it\r\n                        },\r\n                        keyDownEventLongRightProvider = { settingsViewModel.keyDownEventLongRight },\r\n                        onkeyDownEventLongRightChanged = {\r\n                            settingsViewModel.keyDownEventLongRight = it\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.CHANNEL_GROUP_VISIBILITY.name) {\r\n                    var hasChanged by remember { mutableStateOf(false) }\r\n                    SettingsChannelGroupVisibilityScreen(\r\n                        channelGroupListProvider = channelGroupListProvider,\r\n                        channelGroupNameHiddenListProvider = { settingsViewModel.iptvChannelGroupHiddenList.toList() },\r\n                        onChannelGroupNameHiddenListChange = {\r\n                            settingsViewModel.iptvChannelGroupHiddenList = it.toSet()\r\n                            hasChanged = true\r\n                        },\r\n                        onBackPressed = {\r\n                            if (hasChanged) onReload()\r\n                            else navController.navigateUp()\r\n                        },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.IPTV_HYBRID_MODE.name) {\r\n                    SettingsIptvHybridModeScreen(\r\n                        hybridModeProvider = { settingsViewModel.iptvHybridMode },\r\n                        onHybridModeChanged = {\r\n                            settingsViewModel.iptvHybridMode = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.EPG_SOURCE.name) {\r\n                    SettingsEpgSourceScreen(\r\n                        currentEpgSourceProvider = { settingsViewModel.epgSourceCurrent },\r\n                        epgSourceListProvider = { settingsViewModel.epgSourceList },\r\n                        onSetCurrent = {\r\n                            settingsViewModel.epgSourceCurrent = it\r\n                            onReload()\r\n                        },\r\n                        onDelete = {\r\n                            settingsViewModel.epgSourceList =\r\n                                EpgSourceList(settingsViewModel.epgSourceList - it)\r\n                        },\r\n                        onClearCache = {\r\n                            coroutineScope.launch {\r\n                                EpgRepository(it).clearCache()\r\n                                Snackbar.show(\"${context.getString(R.string.ui_cache_cleared)}\")\r\n                            }\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.EPG_REFRESH_TIME_THRESHOLD.name) {\r\n                    SettingsEpgRefreshTimeThresholdScreen(\r\n                        timeThresholdProvider = { settingsViewModel.epgRefreshTimeThreshold },\r\n                        onTimeThresholdChanged = { settingsViewModel.epgRefreshTimeThreshold = it },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_TIME_SHOW_MODE.name) {\r\n                    SettingsUiTimeShowModeScreen(\r\n                        timeShowModeProvider = { settingsViewModel.uiTimeShowMode },\r\n                        onTimeShowModeChanged = {\r\n                            settingsViewModel.uiTimeShowMode = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_SCREEN_AUTO_CLOSE_DELAY.name) {\r\n                    SettingsUiScreenAutoCloseScreen(\r\n                        delayProvider = { settingsViewModel.uiScreenAutoCloseDelay },\r\n                        onDelayChanged = {\r\n                            settingsViewModel.uiScreenAutoCloseDelay = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_DENSITY_SCALE_RATIO.name) {\r\n                    SettingsUiDensityScaleRatioScreen(\r\n                        scaleRatioProvider = { settingsViewModel.uiDensityScaleRatio },\r\n                        onScaleRatioChanged = { settingsViewModel.uiDensityScaleRatio = it },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_FONT_SCALE_RATIO.name) {\r\n                    SettingsUiFontScaleRatioScreen(\r\n                        scaleRatioProvider = { settingsViewModel.uiFontScaleRatio },\r\n                        onScaleRatioChanged = { settingsViewModel.uiFontScaleRatio = it },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_VIDEO_PLAYER_SUBTITLE.name) {\r\n                    SettingsUiVideoPlayerSubtitleSettingsScreen(\r\n                        subtitleSettingsProvider = { settingsViewModel.uiVideoPlayerSubtitle },\r\n                        onSubtitleSettingsChanged = {\r\n                            settingsViewModel.uiVideoPlayerSubtitle = it\r\n                            // navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.NETWORK_RETRY_COUNT.name) {\r\n                    SettingsNetworkRetryCountScreen(\r\n                        countProvider = { settingsViewModel.networkRetryCount },\r\n                        onCountChanged = { count ->\r\n                            settingsViewModel.networkRetryCount = count\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.NETWORK_RETRY_INTERVAL.name) {\r\n                    SettingsNetworkRetryIntervalScreen(\r\n                        intervalProvider = { settingsViewModel.networkRetryInterval },\r\n                        onIntervalChanged = { interval ->\r\n                            settingsViewModel.networkRetryInterval = interval\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_CORE.name) {\r\n                    SettingsVideoPlayerCoreScreen(\r\n                        coreProvider = { settingsViewModel.videoPlayerCore },\r\n                        onCoreChanged = {\r\n                            settingsViewModel.videoPlayerCore = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.WEBVIEW_CORE.name) {\r\n                    SettingsWebViewCoreScreen(\r\n                        coreProvider = { settingsViewModel.webViewCore },\r\n                        onCoreChanged = {\r\n                            settingsViewModel.webViewCore = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_RENDER_MODE.name) {\r\n                    SettingsVideoPlayerRenderModeScreen(\r\n                        renderModeProvider = { settingsViewModel.videoPlayerRenderMode },\r\n                        onRenderModeChanged = {\r\n                            settingsViewModel.videoPlayerRenderMode = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_RTSP_TRANSPORT.name) {\r\n                    SettingsVideoPlayerRtspTransportScreen(\r\n                        transportProvider = { settingsViewModel.videoPlayerRtspTransport },\r\n                        onTransportChanged = {\r\n                            settingsViewModel.videoPlayerRtspTransport = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_DISPLAY_MODE.name) {\r\n                    SettingsVideoPlayerDisplayModeScreen(\r\n                        displayModeProvider = { settingsViewModel.videoPlayerDisplayMode },\r\n                        onDisplayModeChanged = {\r\n                            settingsViewModel.videoPlayerDisplayMode = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_LOAD_TIMEOUT.name) {\r\n                    SettingsVideoPlayerLoadTimeoutScreen(\r\n                        timeoutProvider = { settingsViewModel.videoPlayerLoadTimeout },\r\n                        onTimeoutChanged = {\r\n                            settingsViewModel.videoPlayerLoadTimeout = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.WEBVIEW_PLAYER_LOAD_TIMEOUT.name) {\r\n                    SettingsWebViewPlayerLoadTimeoutScreen(\r\n                        timeoutProvider = { settingsViewModel.webViewLoadTimeout },\r\n                        onTimeoutChanged = {\r\n                            settingsViewModel.webViewLoadTimeout = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.VIDEO_PLAYER_BUFFER_TIME.name) {\r\n                    SettingsVideoPlayerBufferTimeScreen(\r\n                        bufferTimeProvider = { settingsViewModel.videoPlayerBufferTime },\r\n                        onBufferTimeChanged = {\r\n                            settingsViewModel.videoPlayerBufferTime = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n                \r\n                composable(SettingsSubCategories.UPDATE_CHANNEL.name) {\r\n                    SettingsUpdateChannelScreen(\r\n                        updateChannelProvider = { settingsViewModel.updateChannel },\r\n                        onUpdateChannelChanged = {\r\n                            settingsViewModel.updateChannel = it\r\n                            navController.navigateUp()\r\n                            onCheckUpdate()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.CLOUD_SYNC_PROVIDER.name) {\r\n                    SettingsCloudSyncProviderScreen(\r\n                        providerProvider = { settingsViewModel.cloudSyncProvider },\r\n                        onProviderChanged = {\r\n                            settingsViewModel.cloudSyncProvider = it\r\n                            navController.navigateUp()\r\n                        },\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n\r\n                composable(SettingsSubCategories.UI_SETTINGS_DECODER_INFO.name) {\r\n                    SettingsDecoderInfoScreen(\r\n                        onBackPressed = { navController.navigateUp() },\r\n                    )\r\n                }\r\n            }\r\n        )\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsScreen.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsScreen.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsScreen.kt	(revision 25a7a46c1f0e8a4340fc5339b7cfb1db7f44bb8f)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsScreen.kt	(date 1756691200105)
@@ -57,6 +57,7 @@
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerCoreScreen
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsWebViewCoreScreen
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerDisplayModeScreen
+import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerPlaybackModeScreen
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerLoadTimeoutScreen
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerBufferTimeScreen
 import top.yogiczy.mytv.tv.ui.screen.settings.subcategories.SettingsVideoPlayerRenderModeScreen
@@ -197,6 +198,9 @@
                         toVideoPlayerDisplayModeScreen = {
                             navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_DISPLAY_MODE.name)
                         },
+                        toVideoPlayerPlaybackModeScreen = {
+                            navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_PLAYBACK_MODE.name)
+                        },
                         toVideoPlayerLoadTimeoutScreen = {
                             navController.navigateSingleTop(SettingsSubCategories.VIDEO_PLAYER_LOAD_TIMEOUT.name)
                         },
@@ -539,6 +543,17 @@
                         onBackPressed = { navController.navigateUp() },
                     )
                 }
+
+                composable(SettingsSubCategories.VIDEO_PLAYER_PLAYBACK_MODE.name) {
+                    SettingsVideoPlayerPlaybackModeScreen(
+                        playbackModeProvider = { settingsViewModel.videoPlayerPlaybackMode },
+                        onPlaybackModeChanged = {
+                            settingsViewModel.videoPlayerPlaybackMode = it
+                            navController.navigateUp()
+                        },
+                        onBackPressed = { navController.navigateUp() },
+                    )
+                }
 
                 composable(SettingsSubCategories.VIDEO_PLAYER_LOAD_TIMEOUT.name) {
                     SettingsVideoPlayerLoadTimeoutScreen(
